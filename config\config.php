<?php
/**
 * Main Configuration File for IGSS Management System
 * Intellectual Giant Secondary School Electronic Management System
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include database configuration
require_once __DIR__ . '/database.php';

// Application Configuration
define('APP_NAME', 'IGSS Management System');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/IGSS');
define('BASE_PATH', __DIR__ . '/..');

// File Upload Configuration
define('UPLOAD_PATH', BASE_PATH . '/uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'csv']);

// Security Configuration
define('PASSWORD_MIN_LENGTH', 8);
define('SESSION_TIMEOUT', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOCKOUT_TIME', 900); // 15 minutes

// School Information (will be loaded from database)
$school_info = [
    'name' => 'Intellectual Giant Secondary School',
    'address' => 'Port-Harcourt, Rivers State, Nigeria',
    'phone' => '+234 ************',
    'email' => '<EMAIL>',
    'website' => 'https://intellectualgiants.org',
    'logo' => 'assets/images/logo.png'
];

// User Types
define('USER_TYPES', [
    'admin' => 'Administrator',
    'teacher' => 'Teacher',
    'student' => 'Student',
    'parent' => 'Parent/Guardian'
]);

// Grade Configuration
define('GRADE_SCALE', [
    'A' => ['min' => 70, 'max' => 100, 'remark' => 'Excellent'],
    'B' => ['min' => 60, 'max' => 69, 'remark' => 'Very Good'],
    'C' => ['min' => 50, 'max' => 59, 'remark' => 'Good'],
    'D' => ['min' => 45, 'max' => 49, 'remark' => 'Pass'],
    'E' => ['min' => 40, 'max' => 44, 'remark' => 'Fair'],
    'F' => ['min' => 0, 'max' => 39, 'remark' => 'Fail']
]);

// Class Configuration
define('CLASS_LEVELS', [
    'JS' => [
        'name' => 'Junior Secondary',
        'classes' => ['JSS1', 'JSS2', 'JSS3']
    ],
    'SS' => [
        'name' => 'Senior Secondary',
        'classes' => ['SS1', 'SS2', 'SS3'],
        'types' => ['Science', 'Art']
    ]
]);

// Terms Configuration
define('TERMS', [
    1 => 'First Term',
    2 => 'Second Term',
    3 => 'Third Term'
]);

// Utility Functions

/**
 * Get current academic session and term from database
 */
function getCurrentAcademicInfo() {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("
            SELECT setting_key, setting_value
            FROM system_settings
            WHERE setting_key IN ('current_session', 'current_term')
        ");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

        return [
            'session' => $settings['current_session'] ?? '2024/2025',
            'term' => $settings['current_term'] ?? '1'
        ];
    } catch (Exception $e) {
        return [
            'session' => '2024/2025',
            'term' => '1'
        ];
    }
}

/**
 * Get school information from database
 */
function getSchoolInfo() {
    global $school_info;

    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("
            SELECT setting_key, setting_value
            FROM system_settings
            WHERE setting_key LIKE 'school_%'
        ");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

        if (!empty($settings)) {
            $school_info['name'] = $settings['school_name'] ?? $school_info['name'];
            $school_info['address'] = $settings['school_address'] ?? $school_info['address'];
            $school_info['phone'] = $settings['school_phone'] ?? $school_info['phone'];
            $school_info['email'] = $settings['school_email'] ?? $school_info['email'];
            $school_info['logo'] = $settings['school_logo'] ?? $school_info['logo'];
        }
    } catch (Exception $e) {
        // Use default values if database is not available
    }

    return $school_info;
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['user_type']);
}

/**
 * Check if user has specific role
 */
function hasRole($role) {
    return isLoggedIn() && $_SESSION['user_type'] === $role;
}

/**
 * Redirect to login if not authenticated
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: ' . APP_URL . '/login.php');
        exit();
    }
}

/**
 * Redirect to appropriate dashboard based on user type
 */
function redirectToDashboard() {
    if (!isLoggedIn()) {
        header('Location: ' . APP_URL . '/login.php');
        exit();
    }

    $userType = $_SESSION['user_type'];
    switch ($userType) {
        case 'admin':
            header('Location: ' . APP_URL . '/admin/dashboard.php');
            break;
        case 'teacher':
            header('Location: ' . APP_URL . '/teacher/dashboard.php');
            break;
        case 'student':
            header('Location: ' . APP_URL . '/student/index.php');
            break;
        case 'parent':
            header('Location: ' . APP_URL . '/parent/index.php');
            break;
        default:
            header('Location: ' . APP_URL . '/login.php');
    }
    exit();
}

/**
 * Generate unique user code
 */
function generateUserCode($userType) {
    if ($userType === 'student') {
        return generateStudentCode();
    } elseif ($userType === 'teacher') {
        return generateTeacherCode();
    } elseif ($userType === 'parent') {
        return generateParentCode();
    } elseif ($userType === 'admin') {
        return generateAdminCode();
    }

    // Fallback for unknown user types
    $prefix = strtoupper(substr($userType, 0, 3));
    $timestamp = time();
    $random = rand(100, 999);
    return $prefix . $timestamp . $random;
}

/**
 * Generate unique student code (format: IGSS-YYYY-XXXX)
 */
function generateStudentCode() {
    try {
        $conn = getDBConnection();
        $year = date('Y');

        // Get the last student code for current year
        $stmt = $conn->prepare("
            SELECT user_code FROM users
            WHERE user_type = 'student' AND user_code LIKE ?
            ORDER BY user_code DESC LIMIT 1
        ");
        $stmt->execute(["IGSS-{$year}-%"]);
        $lastCode = $stmt->fetchColumn();

        if ($lastCode) {
            // Extract the number part and increment
            $parts = explode('-', $lastCode);
            $lastNumber = intval($parts[2]);
            $newNumber = $lastNumber + 1;
        } else {
            // First student for this year
            $newNumber = 1;
        }

        // Format: IGSS-2024-0001
        return sprintf("IGSS-%s-%04d", $year, $newNumber);

    } catch (Exception $e) {
        // Fallback to timestamp-based code
        return 'IGSS-' . date('Y') . '-' . rand(1000, 9999);
    }
}

/**
 * Generate unique teacher code (format: TEACHER_XXX)
 */
function generateTeacherCode() {
    try {
        $conn = getDBConnection();

        // Get the last teacher code
        $stmt = $conn->prepare("
            SELECT user_code FROM users
            WHERE user_type = 'teacher' AND user_code LIKE 'TEACHER_%'
            ORDER BY user_code DESC LIMIT 1
        ");
        $stmt->execute();
        $lastCode = $stmt->fetchColumn();

        if ($lastCode) {
            // Extract the number part and increment
            $parts = explode('_', $lastCode);
            $lastNumber = intval($parts[1]);
            $newNumber = $lastNumber + 1;
        } else {
            // First teacher
            $newNumber = 1;
        }

        // Format: TEACHER_001
        return sprintf("TEACHER_%03d", $newNumber);

    } catch (Exception $e) {
        // Fallback to random number
        return 'TEACHER_' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
    }
}

/**
 * Generate unique parent code (format: PARENT_XXX)
 */
function generateParentCode() {
    try {
        $conn = getDBConnection();

        // Get the last parent code
        $stmt = $conn->prepare("
            SELECT user_code FROM users
            WHERE user_type = 'parent' AND user_code LIKE 'PARENT_%'
            ORDER BY user_code DESC LIMIT 1
        ");
        $stmt->execute();
        $lastCode = $stmt->fetchColumn();

        if ($lastCode) {
            // Extract the number part and increment
            $parts = explode('_', $lastCode);
            $lastNumber = intval($parts[1]);
            $newNumber = $lastNumber + 1;
        } else {
            // First parent
            $newNumber = 1;
        }

        // Format: PARENT_001
        return sprintf("PARENT_%03d", $newNumber);

    } catch (Exception $e) {
        // Fallback to random number
        return 'PARENT_' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
    }
}

/**
 * Generate unique admin code (format: ADMIN_XXX)
 */
function generateAdminCode() {
    try {
        $conn = getDBConnection();

        // Get the last admin code
        $stmt = $conn->prepare("
            SELECT user_code FROM users
            WHERE user_type = 'admin' AND user_code LIKE 'ADMIN_%'
            ORDER BY user_code DESC LIMIT 1
        ");
        $stmt->execute();
        $lastCode = $stmt->fetchColumn();

        if ($lastCode) {
            // Extract the number part and increment
            $parts = explode('_', $lastCode);
            $lastNumber = intval($parts[1]);
            $newNumber = $lastNumber + 1;
        } else {
            // First admin
            $newNumber = 1;
        }

        // Format: ADMIN_001
        return sprintf("ADMIN_%03d", $newNumber);

    } catch (Exception $e) {
        // Fallback to random number
        return 'ADMIN_' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
    }
}

/**
 * Find student by student code
 */
function findStudentByCode($studentCode) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("
            SELECT s.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone,
                   u.date_of_birth, u.gender, u.user_code, c.class_name, l.level_name
            FROM students s
            JOIN users u ON s.user_id = u.id
            JOIN classes c ON s.class_id = c.id
            JOIN levels l ON c.level_id = l.id
            WHERE u.user_code = ? AND u.is_active = 1
        ");
        $stmt->execute([$studentCode]);
        return $stmt->fetch();
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Link parent to student
 */
function linkParentToStudent($parentId, $studentId, $relationshipType) {
    try {
        $conn = getDBConnection();

        // Check if relationship already exists
        $stmt = $conn->prepare("
            SELECT id FROM parent_student_relationships
            WHERE parent_id = ? AND student_id = ?
        ");
        $stmt->execute([$parentId, $studentId]);

        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'You are already linked to this student.'];
        }

        // Create the relationship
        $stmt = $conn->prepare("
            INSERT INTO parent_student_relationships (parent_id, student_id, relationship_type, is_primary)
            VALUES (?, ?, ?, ?)
        ");

        // Check if this is the first child for this parent (make it primary)
        $stmt2 = $conn->prepare("SELECT COUNT(*) FROM parent_student_relationships WHERE parent_id = ?");
        $stmt2->execute([$parentId]);
        $isPrimary = $stmt2->fetchColumn() == 0;

        $stmt->execute([$parentId, $studentId, $relationshipType, $isPrimary]);

        return ['success' => true, 'message' => 'Student linked successfully!'];

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to link student: ' . $e->getMessage()];
    }
}

/**
 * Get parent's linked students
 */
function getParentStudents($parentId) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("
            SELECT s.*, u.first_name, u.last_name, u.middle_name, u.user_code,
                   c.class_name, l.level_name, psr.relationship_type, psr.is_primary
            FROM parent_student_relationships psr
            JOIN students s ON psr.student_id = s.id
            JOIN users u ON s.user_id = u.id
            JOIN classes c ON s.class_id = c.id
            JOIN levels l ON c.level_id = l.id
            WHERE psr.parent_id = ?
            ORDER BY psr.is_primary DESC, u.first_name, u.last_name
        ");
        $stmt->execute([$parentId]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Format currency (Nigerian Naira)
 */
function formatCurrency($amount) {
    return '₦' . number_format($amount, 2);
}

/**
 * Calculate grade from score
 */
function calculateGrade($score) {
    foreach (GRADE_SCALE as $grade => $range) {
        if ($score >= $range['min'] && $score <= $range['max']) {
            return [
                'grade' => $grade,
                'remark' => $range['remark']
            ];
        }
    }
    return ['grade' => 'F', 'remark' => 'Fail'];
}

/**
 * Check if database is properly configured
 */
function checkDatabaseSetup() {
    if (!isDatabaseConfigured()) {
        // Redirect to setup page or show error
        header('Location: ' . APP_URL . '/setup.php');
        exit();
    }
}

// Initialize application
checkDatabaseSetup();

// Set timezone
date_default_timezone_set('Africa/Lagos');

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);
?>
