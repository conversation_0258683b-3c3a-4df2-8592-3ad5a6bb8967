<?php
/**
 * Professional Fee Management Page
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
requireLogin();
if (!hasRole('admin')) {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];

    if ($action === 'add_fee_type') {
        $feeName = sanitizeInput($_POST['fee_name']);
        $feeAmount = floatval($_POST['fee_amount']);
        $feeDescription = sanitizeInput($_POST['fee_description'] ?? '');
        $isExamFee = isset($_POST['is_exam_fee']) ? 1 : 0;
        $targetClass = $_POST['target_class'] ?? '';
        $paymentTerm = $_POST['payment_term'] ?? '';

        try {
            // Validation
            if (empty($feeName) || $feeAmount <= 0) {
                throw new Exception('Fee name and amount are required');
            }

            if ($isExamFee && (empty($targetClass) || empty($paymentTerm))) {
                throw new Exception('Target class and payment term are required for exam fees');
            }

            // Check if fee name already exists
            $stmt = $conn->prepare("SELECT id FROM fee_types WHERE fee_name = ? AND is_active = 1");
            $stmt->execute([$feeName]);
            if ($stmt->fetch()) {
                throw new Exception('Fee name already exists');
            }

            $stmt = $conn->prepare("
                INSERT INTO fee_types (fee_name, amount_js, amount_ss, fee_description,
                                     is_exam_fee, target_class, payment_term, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $feeName, $feeAmount, $feeAmount, $feeDescription,
                $isExamFee, $targetClass, $paymentTerm, $_SESSION['user_id']
            ]);

            $message = 'Fee type added successfully.';
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Get fee types
$stmt = $conn->prepare("
    SELECT ft.*,
           ft.fee_name as fee_type_name,
           ft.fee_description as description,
           ft.amount_js as amount,
           CASE
               WHEN ft.is_exam_fee = 1 THEN CONCAT(ft.target_class, ' - Term ', ft.payment_term)
               ELSE 'All Levels'
           END as level_name,
           CONCAT(u.first_name, ' ', u.last_name) as created_by_name
    FROM fee_types ft
    LEFT JOIN users u ON ft.created_by = u.id
    WHERE ft.is_active = 1
    ORDER BY ft.is_exam_fee ASC, ft.fee_name
");
$stmt->execute();
$fee_types = $stmt->fetchAll();

// Get school info
$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Fees - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        /* Custom white scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.8);
        }

        /* For Firefox */
        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.6) rgba(255, 255, 255, 0.2);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 80px;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.25) 100%);
            border-radius: 60px 0 0 60px;
        }

        .sidebar-header {
            padding: 25px 20px; /* Reduced padding */
            text-align: center;
            position: relative;
            background: rgba(255, 255, 255, 0.01); /* Minimal opacity */
            border-radius: 0 0 30px 30px;
            margin-bottom: 15px; /* Reduced margin */
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 700;
            font-size: 1.4rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header small {
            opacity: 0.9;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            margin-bottom: 80px; /* Space for back to home button */
        }

        .sidebar-menu li {
            margin: 1px 15px; /* Minimal margin for tight fit */
        }

        .sidebar-menu a {
            color: white;
            text-decoration: none;
            padding: 8px 20px; /* Smaller padding like other pages */
            display: block;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 25px;
            position: relative;
            font-weight: 500; /* Normal font weight */
            font-size: 0.85rem; /* Smaller font size */
            background: rgba(255, 255, 255, 0.05); /* Very subtle background */
            border: none; /* Remove border */
            margin-bottom: 1px; /* Minimal spacing */
        }

        .sidebar-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover::before, .sidebar-menu a.active::before {
            opacity: 1;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            transform: translateX(8px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.15);
        }

        .sidebar-menu a.active {
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .back-to-home {
            position: absolute;
            bottom: 60px; /* Moved higher up */
            left: 15px;
            right: 15px;
            z-index: 1001;
        }

        .back-to-home .btn {
            background: rgba(255, 255, 255, 0.3); /* Even more visible white background */
            border: 3px solid rgba(255, 255, 255, 0.6); /* Thicker, more visible border */
            color: white;
            font-weight: 800;
            padding: 12px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            width: 100%;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            font-size: 0.95rem;
        }

        .back-to-home .btn:hover {
            background: rgba(255, 255, 255, 0.3); /* More visible hover effect */
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 25px;
            backdrop-filter: blur(20px);
        }

        .fee-type-card {
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            background: white;
        }

        .fee-type-card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .amount-display {
            font-size: 1.5rem;
            font-weight: bold;
            color: #28a745;
        }

        .exam-fee-badge {
            background: #ff6b35;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .btn-action {
            padding: 8px 12px;
            margin: 0 2px;
            border-radius: 8px;
            border: none;
            transition: all 0.3s ease;
        }

        .btn-edit {
            background: #007bff;
            color: white;
        }

        .btn-edit:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .btn-delete {
            background: #dc3545;
            color: white;
        }

        .btn-delete:hover {
            background: #c82333;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-graduation-cap me-2"></i>IGSS Admin</h4>
            <small>Management System</small>
        </div>

        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
            <li><a href="manage-users.php"><i class="fas fa-users me-2"></i>Manage Users</a></li>
            <li><a href="academic-settings.php"><i class="fas fa-calendar-alt me-2"></i>Academic Settings</a></li>
            <li><a href="assign-subjects.php"><i class="fas fa-book me-2"></i>Assign Subjects</a></li>
            <li><a href="assign-form-teachers.php"><i class="fas fa-chalkboard-teacher me-2"></i>Form Teachers</a></li>
            <li><a href="manage-fees.php" class="active"><i class="fas fa-money-bill me-2"></i>Manage Fees</a></li>
            <li><a href="fee-confirmations.php"><i class="fas fa-money-check-alt me-2"></i>Fee Confirmations</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar me-2"></i>Reports</a></li>
            <li><a href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
            <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
        </ul>

        <!-- Back to Home Button -->
        <div class="back-to-home">
            <a href="../home.php" class="btn">
                <i class="fas fa-home me-2"></i>Back to Home
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><i class="fas fa-money-bill me-2"></i>Fee Management</h4>
                    <small class="text-muted">Manage school fees and examination fees</small>
                </div>
                <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addFeeModal">
                    <i class="fas fa-plus me-2"></i>Add New Fee
                </button>
            </div>
        </div>

        <!-- Alerts -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Fee Types List -->
        <div class="content-card">
            <h5 class="mb-4"><i class="fas fa-list me-2"></i>Current Fee Types</h5>

            <?php if (empty($fee_types)): ?>
                <div class="text-center text-muted py-5">
                    <i class="fas fa-money-bill fa-4x mb-3"></i>
                    <h5>No fee types configured</h5>
                    <p>Click "Add New Fee" to create your first fee type.</p>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($fee_types as $fee_type): ?>
                        <div class="col-md-6 col-lg-4">
                            <div class="fee-type-card">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h6 class="mb-0">
                                        <?php echo htmlspecialchars($fee_type['fee_type_name'] ?? 'Unknown Fee'); ?>
                                        <?php if ($fee_type['is_exam_fee']): ?>
                                            <span class="exam-fee-badge ms-2">EXAM FEE</span>
                                        <?php endif; ?>
                                    </h6>
                                    <div>
                                        <button class="btn-action btn-edit" onclick="editFeeType(<?php echo $fee_type['id']; ?>)" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-action btn-delete" onclick="deleteFeeType(<?php echo $fee_type['id']; ?>)" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="amount-display mb-2">₦<?php echo number_format($fee_type['amount'] ?? 0, 2); ?></div>

                                <span class="badge bg-primary mb-2"><?php echo htmlspecialchars($fee_type['level_name'] ?? 'All Levels'); ?></span>

                                <?php if (!empty($fee_type['description'])): ?>
                                    <p class="text-muted small mb-2"><?php echo htmlspecialchars($fee_type['description']); ?></p>
                                <?php endif; ?>

                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>Created by: <?php echo htmlspecialchars($fee_type['created_by_name'] ?? 'Unknown'); ?>
                                </small>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Add Fee Modal -->
    <div class="modal fade" id="addFeeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add New Fee Type</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_fee_type">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Fee Name <span class="text-danger">*</span></label>
                                    <input type="text" name="fee_name" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Amount (₦) <span class="text-danger">*</span></label>
                                    <input type="number" name="fee_amount" class="form-control" step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_exam_fee" id="addIsExamFee" onchange="toggleAddExamFields()">
                                <label class="form-check-label" for="addIsExamFee">
                                    This is an Exam Fee (WAEC)
                                </label>
                            </div>
                        </div>

                        <div id="addExamFields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Target Class</label>
                                        <select name="target_class" class="form-select">
                                            <option value="">Select Class</option>
                                            <option value="JSS3">JSS3 (Junior WAEC)</option>
                                            <option value="SS3">SS3 (Senior WAEC)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Payment Term</label>
                                        <select name="payment_term" class="form-select">
                                            <option value="">Select Term</option>
                                            <option value="1">First Term</option>
                                            <option value="2">Second Term</option>
                                            <option value="3">Third Term</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea name="fee_description" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>Add Fee Type
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Fee Modal -->
    <div class="modal fade" id="editFeeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Fee Type</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="editFeeForm">
                    <div class="modal-body">
                        <input type="hidden" name="fee_id" id="editFeeId">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Fee Name <span class="text-danger">*</span></label>
                                    <input type="text" name="fee_name" id="editFeeName" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Amount (₦) <span class="text-danger">*</span></label>
                                    <input type="number" name="fee_amount" id="editFeeAmount" class="form-control" step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_exam_fee" id="editIsExamFee" onchange="toggleEditExamFields()">
                                <label class="form-check-label" for="editIsExamFee">
                                    This is an Exam Fee (WAEC)
                                </label>
                            </div>
                        </div>

                        <div id="editExamFields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Target Class</label>
                                        <select name="target_class" id="editTargetClass" class="form-select">
                                            <option value="">Select Class</option>
                                            <option value="JSS3">JSS3 (Junior WAEC)</option>
                                            <option value="SS3">SS3 (Senior WAEC)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Payment Term</label>
                                        <select name="payment_term" id="editPaymentTerm" class="form-select">
                                            <option value="">Select Term</option>
                                            <option value="1">First Term</option>
                                            <option value="2">Second Term</option>
                                            <option value="3">Third Term</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea name="fee_description" id="editFeeDescription" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Fee Type
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // Toggle exam fee fields in add modal
        function toggleAddExamFields() {
            const checkbox = document.getElementById('addIsExamFee');
            const examFields = document.getElementById('addExamFields');
            examFields.style.display = checkbox.checked ? 'block' : 'none';
        }

        // Toggle exam fee fields in edit modal
        function toggleEditExamFields() {
            const checkbox = document.getElementById('editIsExamFee');
            const examFields = document.getElementById('editExamFields');
            examFields.style.display = checkbox.checked ? 'block' : 'none';
        }

        // Edit fee type
        async function editFeeType(feeId) {
            try {
                const response = await fetch(`ajax/edit-fee-type.php?id=${feeId}`);
                const result = await response.json();

                if (result.success) {
                    const data = result.data;

                    // Populate form fields
                    document.getElementById('editFeeId').value = data.id;
                    document.getElementById('editFeeName').value = data.fee_name;
                    document.getElementById('editFeeAmount').value = data.amount_js;
                    document.getElementById('editFeeDescription').value = data.fee_description || '';

                    // Handle exam fee checkbox
                    const isExamFeeCheckbox = document.getElementById('editIsExamFee');
                    isExamFeeCheckbox.checked = data.is_exam_fee == 1;

                    if (data.is_exam_fee == 1) {
                        document.getElementById('editExamFields').style.display = 'block';
                        document.getElementById('editTargetClass').value = data.target_class || '';
                        document.getElementById('editPaymentTerm').value = data.payment_term || '';
                    } else {
                        document.getElementById('editExamFields').style.display = 'none';
                    }

                    // Show modal
                    new bootstrap.Modal(document.getElementById('editFeeModal')).show();
                } else {
                    Swal.fire('Error', result.message, 'error');
                }
            } catch (error) {
                Swal.fire('Error', 'Failed to load fee type details', 'error');
            }
        }

        // Delete fee type
        function deleteFeeType(feeId) {
            Swal.fire({
                title: 'Are you sure?',
                text: 'This action cannot be undone!',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, delete it!'
            }).then(async (result) => {
                if (result.isConfirmed) {
                    try {
                        const formData = new FormData();
                        formData.append('fee_id', feeId);

                        const response = await fetch('ajax/delete-fee-type.php', {
                            method: 'POST',
                            body: formData
                        });

                        const deleteResult = await response.json();

                        if (deleteResult.success) {
                            Swal.fire('Deleted!', deleteResult.message, 'success').then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('Error', deleteResult.message, 'error');
                        }
                    } catch (error) {
                        Swal.fire('Error', 'Failed to delete fee type', 'error');
                    }
                }
            });
        }

        // Handle edit form submission
        document.getElementById('editFeeForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            try {
                const response = await fetch('ajax/edit-fee-type.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    Swal.fire('Success', result.message, 'success').then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('Error', result.message, 'error');
                }
            } catch (error) {
                Swal.fire('Error', 'Failed to update fee type', 'error');
            }
        });
    </script>
</body>
</html>