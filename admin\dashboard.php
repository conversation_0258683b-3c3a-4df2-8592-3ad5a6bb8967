<?php
/**
 * Admin Dashboard for IGSS Management System
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
requireLogin();
if (!hasRole('admin')) {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$current_academic = getCurrentAcademicInfo();

// Get dashboard statistics
$stats = [];

// Total users by type
$stmt = $conn->prepare("
    SELECT user_type, COUNT(*) as count
    FROM users
    WHERE is_active = 1
    GROUP BY user_type
");
$stmt->execute();
$user_stats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

$stats['total_teachers'] = $user_stats['teacher'] ?? 0;
$stats['total_students'] = $user_stats['student'] ?? 0;
$stats['total_parents'] = $user_stats['parent'] ?? 0;

// Pending approvals
$stmt = $conn->prepare("
    SELECT user_type, COUNT(*) as count
    FROM users
    WHERE is_approved = 0 AND is_active = 1
    GROUP BY user_type
");
$stmt->execute();
$pending_stats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

$stats['pending_teachers'] = $pending_stats['teacher'] ?? 0;
$stats['pending_students'] = $pending_stats['student'] ?? 0;
$stats['pending_parents'] = $pending_stats['parent'] ?? 0;
$stats['total_pending'] = array_sum($pending_stats);

// Fee payment statistics
$stmt = $conn->prepare("
    SELECT status, COUNT(*) as count
    FROM fee_payments
    WHERE session_id = (SELECT id FROM academic_sessions WHERE session_name = ?)
    GROUP BY status
");
$stmt->execute([$current_academic['session']]);
$fee_stats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

$stats['pending_fees'] = $fee_stats['Pending'] ?? 0;
$stats['confirmed_fees'] = $fee_stats['Confirmed'] ?? 0;
$stats['rejected_fees'] = $fee_stats['Rejected'] ?? 0;

// Recent activities
$stmt = $conn->prepare("
    SELECT 'User Registration' as activity_type,
           CONCAT(first_name, ' ', last_name, ' (', user_type, ')') as description,
           created_at as activity_date
    FROM users
    WHERE is_approved = 0
    ORDER BY created_at DESC
    LIMIT 5
");
$stmt->execute();
$recent_activities = $stmt->fetchAll();

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Admin Dashboard - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        body {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }

        .sidebar {
            background: linear-gradient(135deg, #ff8c42 0%, #ff6b35 30%, #ff5722 70%, #e65100 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.2);
            overflow-y: auto;
            padding-bottom: 80px; /* Reduced space for back to home button */
        }

        /* Custom white scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.8);
        }

        /* For Firefox */
        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.6) rgba(255, 255, 255, 0.2);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 60px;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.08) 70%, rgba(255, 255, 255, 0.12) 100%);
            border-radius: 40px 0 0 40px;
            pointer-events: none;
        }

        .sidebar-header {
            padding: 25px 20px; /* Reduced padding */
            text-align: center;
            position: relative;
            background: rgba(255, 255, 255, 0.05); /* Very subtle background */
            border-radius: 0 0 30px 30px;
            margin-bottom: 15px; /* Reduced margin */
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 700;
            font-size: 1.4rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header small {
            opacity: 0.9;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            margin-bottom: 80px; /* Space for back to home button */
        }

        .sidebar-menu li {
            margin: 1px 15px; /* Minimal margin for tight fit */
        }

        .sidebar-menu a {
            color: white;
            text-decoration: none;
            padding: 8px 20px; /* Smaller padding like before */
            display: block;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 25px;
            position: relative;
            font-weight: 500; /* Normal font weight */
            font-size: 0.85rem; /* Smaller font size */
            background: transparent; /* No background for normal items */
            border: none; /* Remove border */
            margin-bottom: 1px; /* Minimal spacing */
        }

        .sidebar-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.03); /* Very subtle overlay */
            border-radius: 25px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover::before, .sidebar-menu a.active::before {
            opacity: 1;
        }

        .sidebar-menu a:hover {
            transform: translateX(5px); /* Smaller movement */
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2); /* Subtle shadow */
            background: rgba(255, 255, 255, 0.08); /* Subtle hover */
        }

        .sidebar-menu a.active {
            background: rgba(255, 255, 255, 0.2); /* More solid active state */
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            transform: translateX(5px);
        }

        .back-to-home {
            position: absolute;
            bottom: 60px; /* Moved higher up */
            left: 15px;
            right: 15px;
            z-index: 1001;
        }

        .back-to-home .btn {
            background: rgba(255, 255, 255, 0.3); /* Even more visible white background */
            border: 3px solid rgba(255, 255, 255, 0.6); /* Thicker, more visible border */
            color: white;
            font-weight: 800;
            padding: 12px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            width: 100%;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            font-size: 0.95rem;
        }

        .back-to-home .btn:hover {
            background: rgba(255, 255, 255, 0.3); /* More visible hover effect */
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
            position: relative;
            height: 100vh;
            overflow-y: auto;
        }

        /* Custom white scrollbar for main content */
        .main-content::-webkit-scrollbar {
            width: 12px;
        }

        .main-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 10px;
        }

        .main-content::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .main-content::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 1);
        }

        /* For Firefox */
        .main-content {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.8) rgba(255, 255, 255, 0.3);
        }

        .top-navbar {
            background: rgba(255, 255, 255, 0.98); /* Increased opacity */
            padding: 20px 30px;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(5px); /* Reduced blur */
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .stats-card {
            background: rgba(255, 255, 255, 0.98); /* Increased opacity */
            border-radius: 25px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            margin-bottom: 25px;
            backdrop-filter: blur(3px); /* Reduced blur */
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff9a56, #ff6b35, #f7931e);
        }

        .stats-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            cursor: pointer;
        }

        .stats-card.clickable {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .stats-card.clickable:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .stats-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: white;
            margin-bottom: 20px;
            position: relative;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .stats-icon.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .stats-icon.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .stats-icon.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }
        .stats-icon.danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        }
        .stats-icon.info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }

        .stats-number {
            font-size: 36px;
            font-weight: 800;
            color: #333;
            margin-bottom: 8px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .stats-label {
            color: #666;
            font-size: 15px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.98); /* Increased opacity */
            border-radius: 25px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
            backdrop-filter: blur(3px); /* Reduced blur */
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff9a56, #ff6b35, #f7931e);
        }

        .activity-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .activity-time {
            color: #666;
            font-size: 12px;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar.show {
                transform: translateX(0);
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-graduation-cap me-2"></i>IGSS Admin</h4>
            <small>Management System</small>
        </div>

        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
            <li><a href="manage-users.php" class="active"><i class="fas fa-users me-2"></i>Manage Users</a></li>
            <li><a href="academic-settings.php"><i class="fas fa-calendar-alt me-2"></i>Academic Settings</a></li>
            <li><a href="assign-subjects.php"><i class="fas fa-book me-2"></i>Assign Subjects</a></li>
            <li><a href="assign-form-teachers.php"><i class="fas fa-chalkboard-teacher me-2"></i>Form Teachers</a></li>
            <li><a href="manage-fees.php"><i class="fas fa-money-bill me-2"></i>Manage Fees</a></li>
            <li><a href="fee-confirmations.php"><i class="fas fa-money-check-alt me-2"></i>Fee Confirmations</a></li>
            <li><a href="messages.php"><i class="fas fa-envelope me-2"></i>Messages</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar me-2"></i>Reports</a></li>
            <li><a href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
            <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
        </ul>

        <!-- Back to Home Button -->
        <div class="back-to-home">
            <a href="../home.php" class="btn">
                <i class="fas fa-home me-2"></i>Back to Home
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar">
            <div>
                <h4 class="mb-0">Admin Dashboard</h4>
                <small class="text-muted">Welcome back, <?php echo htmlspecialchars($_SESSION['full_name']); ?></small>
            </div>
            <div>
                <span class="badge bg-primary me-2">
                    Session: <?php echo $current_academic['session']; ?>
                </span>
                <span class="badge bg-info">
                    Term: <?php echo TERMS[$current_academic['term']]; ?>
                </span>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card primary clickable" onclick="navigateToUsers('teacher')">
                    <div class="stats-icon primary">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['total_teachers']; ?></div>
                    <div class="stats-label">Total Teachers</div>
                    <?php if ($stats['pending_teachers'] > 0): ?>
                        <small class="text-warning">
                            <i class="fas fa-clock me-1"></i><?php echo $stats['pending_teachers']; ?> pending approval
                        </small>
                    <?php endif; ?>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stats-card success clickable" onclick="navigateToUsers('student')">
                    <div class="stats-icon success">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['total_students']; ?></div>
                    <div class="stats-label">Total Students</div>
                    <?php if ($stats['pending_students'] > 0): ?>
                        <small class="text-warning">
                            <i class="fas fa-clock me-1"></i><?php echo $stats['pending_students']; ?> pending approval
                        </small>
                    <?php endif; ?>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stats-card info clickable" onclick="navigateToUsers('parent')">
                    <div class="stats-icon info">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['total_parents']; ?></div>
                    <div class="stats-label">Total Parents</div>
                    <?php if ($stats['pending_parents'] > 0): ?>
                        <small class="text-warning">
                            <i class="fas fa-clock me-1"></i><?php echo $stats['pending_parents']; ?> pending approval
                        </small>
                    <?php endif; ?>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stats-card warning clickable" onclick="navigateToUsers('pending')">
                    <div class="stats-icon warning">
                        <i class="fas fa-user-clock"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['total_pending']; ?></div>
                    <div class="stats-label">Pending Approvals</div>
                    <?php if ($stats['total_pending'] > 0): ?>
                        <small class="text-danger">
                            <i class="fas fa-exclamation-triangle me-1"></i>Requires attention
                        </small>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Fee Payment Statistics -->
        <div class="row">
            <div class="col-lg-4 col-md-6">
                <div class="stats-card success clickable" onclick="navigateToFeeConfirmations('confirmed')">
                    <div class="stats-icon success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['confirmed_fees']; ?></div>
                    <div class="stats-label">Confirmed Payments</div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="stats-card warning clickable" onclick="navigateToFeeConfirmations('pending')">
                    <div class="stats-icon warning">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['pending_fees']; ?></div>
                    <div class="stats-label">Pending Payments</div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="stats-card danger clickable" onclick="navigateToFeeConfirmations('rejected')">
                    <div class="stats-icon danger">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['rejected_fees']; ?></div>
                    <div class="stats-label">Rejected Payments</div>
                </div>
            </div>
        </div>

        <!-- Charts and Recent Activities -->
        <div class="row">
            <div class="col-lg-8">
                <div class="chart-container">
                    <h5 class="mb-4"><i class="fas fa-chart-bar me-2"></i>User Statistics</h5>
                    <div style="position: relative; height: 300px; width: 100%;">
                        <canvas id="userChart"></canvas>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="chart-container">
                    <h5 class="mb-4"><i class="fas fa-bell me-2"></i>Recent Activities</h5>
                    <div class="activity-list">
                        <?php if (empty($recent_activities)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-2x mb-3"></i>
                                <p>No recent activities</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($recent_activities as $activity): ?>
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title"><?php echo htmlspecialchars($activity['activity_type']); ?></div>
                                        <div class="activity-description"><?php echo htmlspecialchars($activity['description']); ?></div>
                                        <div class="activity-time"><?php echo timeAgo($activity['activity_date']); ?></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="chart-container">
                    <h5 class="mb-4"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="manage-users.php" class="btn btn-primary w-100 py-3">
                                <i class="fas fa-users fa-2x mb-2"></i><br>
                                Manage Users
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="manage-fees.php" class="btn btn-warning w-100 py-3">
                                <i class="fas fa-money-bill fa-2x mb-2"></i><br>
                                Manage Fees
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="fee-confirmations.php" class="btn btn-info w-100 py-3">
                                <i class="fas fa-money-check-alt fa-2x mb-2"></i><br>
                                Fee Confirmations
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="academic-settings.php" class="btn btn-secondary w-100 py-3">
                                <i class="fas fa-calendar-alt fa-2x mb-2"></i><br>
                                Academic Settings
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="messages.php" class="btn btn-success w-100 py-3">
                                <i class="fas fa-envelope fa-2x mb-2"></i><br>
                                Messages
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Wait for DOM to be fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            // User Statistics Chart
            const chartCanvas = document.getElementById('userChart');
            if (chartCanvas) {
                const ctx = chartCanvas.getContext('2d');

                // Destroy existing chart if it exists (prevents duplicates)
                if (window.userChart instanceof Chart) {
                    window.userChart.destroy();
                }

                window.userChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Teachers', 'Students', 'Parents'],
                        datasets: [{
                            data: [
                                <?php echo $stats['total_teachers']; ?>,
                                <?php echo $stats['total_students']; ?>,
                                <?php echo $stats['total_parents']; ?>
                            ],
                            backgroundColor: [
                                '#667eea',
                                '#28a745',
                                '#17a2b8'
                            ],
                            borderWidth: 0,
                            hoverBorderWidth: 2,
                            hoverBorderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true,
                                    font: {
                                        size: 12
                                    }
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return context.label + ': ' + context.parsed;
                                    }
                                }
                            }
                        },
                        animation: {
                            animateRotate: true,
                            duration: 1000
                        }
                    }
                });
            }
        });

        // Navigation functions for clickable cards
        function navigateToUsers(userType) {
            let url = 'manage-users.php';
            if (userType && userType !== 'pending') {
                url += '?filter=' + userType;
            } else if (userType === 'pending') {
                url += '?status=pending';
            }
            window.location.href = url;
        }

        function navigateToFeeConfirmations(status) {
            let url = 'fee-confirmations.php';
            if (status) {
                url += '?status=' + status;
            }
            window.location.href = url;
        }

        // Mobile sidebar toggle
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.toggle('show');
            }
        }

        // Add hover effects to clickable cards
        document.addEventListener('DOMContentLoaded', function() {
            const clickableCards = document.querySelectorAll('.stats-card.clickable');
            clickableCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });

        // Clear any potential cache issues
        if ('caches' in window) {
            caches.keys().then(function(names) {
                names.forEach(function(name) {
                    if (name.includes('chart') || name.includes('admin')) {
                        caches.delete(name);
                    }
                });
            });
        }
    </script>
</body>
</html>
