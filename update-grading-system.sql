-- Update Grading System for IGSS Management System
-- Modify student_grades table to support Assessment (40) + Exam (60) = Total (100) scoring system
-- Add teacher remarks functionality

USE igss_management;

-- First, drop the existing computed columns
ALTER TABLE student_grades
DROP COLUMN IF EXISTS total_score,
DROP COLUMN IF EXISTS grade,
DROP COLUMN IF EXISTS remark;

-- Add new columns for the updated scoring system
ALTER TABLE student_grades
ADD COLUMN assessment_score DECIMAL(5,2) DEFAULT 0 COMMENT 'Assessment score out of 40',
ADD COLUMN exam_score_new DECIMAL(5,2) DEFAULT 0 COMMENT 'Exam score out of 60',
ADD COLUMN total_score DECIMAL(5,2) GENERATED ALWAYS AS (assessment_score + exam_score_new) STORED COMMENT 'Total score out of 100',
ADD COLUMN grade CHAR(1) GENERATED ALWAYS AS (
    CASE
        WHEN (assessment_score + exam_score_new) >= 70 THEN 'A'
        WHEN (assessment_score + exam_score_new) >= 60 THEN 'B'
        WHEN (assessment_score + exam_score_new) >= 50 THEN 'C'
        WHEN (assessment_score + exam_score_new) >= 45 THEN 'D'
        WHEN (assessment_score + exam_score_new) >= 40 THEN 'E'
        ELSE 'F'
    END
) STORED COMMENT 'Letter grade based on total score',
ADD COLUMN remark VARCHAR(20) GENERATED ALWAYS AS (
    CASE
        WHEN (assessment_score + exam_score_new) >= 70 THEN 'Excellent'
        WHEN (assessment_score + exam_score_new) >= 60 THEN 'Very Good'
        WHEN (assessment_score + exam_score_new) >= 50 THEN 'Good'
        WHEN (assessment_score + exam_score_new) >= 45 THEN 'Pass'
        WHEN (assessment_score + exam_score_new) >= 40 THEN 'Fair'
        ELSE 'Fail'
    END
) STORED COMMENT 'Grade remark based on total score',
ADD COLUMN teacher_remarks TEXT NULL COMMENT 'Teacher comments and feedback for the student';

-- Create a new table for detailed teacher comments per subject
CREATE TABLE IF NOT EXISTS teacher_comments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    class_id INT NOT NULL,
    session_id INT NOT NULL,
    term_id INT NOT NULL,
    teacher_id INT NOT NULL,
    comment_type ENUM('academic', 'behavioral', 'general') DEFAULT 'academic',
    comment_text TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES academic_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_student_subject_term_comment (student_id, subject_id, term_id, comment_type)
);

-- Create indexes for better performance
CREATE INDEX idx_teacher_comments_student ON teacher_comments(student_id);
CREATE INDEX idx_teacher_comments_teacher ON teacher_comments(teacher_id);
CREATE INDEX idx_teacher_comments_subject ON teacher_comments(subject_id);

-- Update existing data to use the new scoring system (if any exists)
-- Convert first_test + second_test to assessment_score (max 40)
-- Keep exam_score as exam_score_new (max 60)
UPDATE student_grades
SET assessment_score = LEAST(40, COALESCE(first_test, 0) + COALESCE(second_test, 0)),
    exam_score_new = LEAST(60, COALESCE(exam_score, 0))
WHERE assessment_score = 0 AND exam_score_new = 0;

-- Add a settings table entry for the new grading system
INSERT INTO system_settings (setting_key, setting_value, description, updated_by)
VALUES ('grading_system_type', 'assessment_exam', 'Assessment (40) + Exam (60) = Total (100)', 1)
ON DUPLICATE KEY UPDATE
setting_value = 'assessment_exam',
description = 'Assessment (40) + Exam (60) = Total (100)',
updated_at = CURRENT_TIMESTAMP;

-- Add grading scale settings
INSERT INTO system_settings (setting_key, setting_value, description, updated_by)
VALUES
('assessment_max_score', '40', 'Maximum score for assessment component', 1),
('exam_max_score', '60', 'Maximum score for exam component', 1),
('total_max_score', '100', 'Maximum total score', 1)
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;