<?php
/**
 * Initialize Database Script for IGSS Management System
 * Run this script to set up the database with all required tables
 */

require_once 'config/database.php';

echo "Starting database initialization...\n";

try {
    // Initialize database
    if (initializeDatabase()) {
        echo "✅ Database initialized successfully!\n";
        echo "✅ All tables created\n";
        echo "✅ Initial data inserted\n";
        echo "✅ Default admin user created\n";
        echo "\nDefault Admin Credentials:\n";
        echo "User Code: ADMIN001\n";
        echo "Email: <EMAIL>\n";
        echo "Password: password\n";
        echo "\n⚠️  Please change the admin password immediately after login!\n";
    } else {
        echo "❌ Failed to initialize database\n";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\nDatabase initialization complete.\n";
?>
