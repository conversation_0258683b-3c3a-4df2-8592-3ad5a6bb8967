<?php
/**
 * Quick Database Setup Script
 * Run this to initialize the database automatically
 */

require_once 'config/database.php';

echo "🚀 Starting IGSS Database Setup...\n\n";

try {
    // Check if database is already configured
    if (isDatabaseConfigured()) {
        echo "✅ Database is already configured!\n";
        echo "📊 Checking tables...\n";
        
        $conn = getDBConnection();
        $stmt = $conn->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "📋 Found " . count($tables) . " tables:\n";
        foreach ($tables as $table) {
            echo "   - $table\n";
        }
        
        echo "\n✨ Database is ready to use!\n";
        echo "🔑 Default Admin Login:\n";
        echo "   User Code: ADMIN001\n";
        echo "   Password: password\n";
        echo "   ⚠️  Please change the password after first login!\n";
        
    } else {
        echo "🔧 Initializing database...\n";
        
        if (initializeDatabase()) {
            echo "✅ Database initialized successfully!\n";
            echo "✅ All tables created\n";
            echo "✅ Initial data inserted\n";
            echo "✅ Default admin user created\n";
            echo "\n🔑 Default Admin Credentials:\n";
            echo "   User Code: ADMIN001\n";
            echo "   Email: <EMAIL>\n";
            echo "   Password: password\n";
            echo "\n⚠️  IMPORTANT: Please change the admin password immediately!\n";
            echo "🌐 You can now access the system at: http://localhost/IGSS/\n";
        } else {
            echo "❌ Failed to initialize database\n";
            echo "💡 Please check your MySQL connection and try again\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "💡 Make sure MySQL is running and accessible\n";
}

echo "\n🎯 Setup complete!\n";
?>
