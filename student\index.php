<?php
/**
 * Student Dashboard
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a student
if (!isLoggedIn() || $_SESSION['user_type'] !== 'student') {
    header('Location: ../login.php');
    exit();
}

// Check if student account is approved
if (!$_SESSION['is_approved']) {
    header('Location: pending-approval.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get student information
$stmt = $conn->prepare("
    SELECT s.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, 
           u.date_of_birth, u.gender, u.user_code, u.profile_image,
           c.class_name, l.level_name
    FROM students s
    JOIN users u ON s.user_id = u.id
    JOIN classes c ON s.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    WHERE s.user_id = ?
");
$stmt->execute([$user_id]);
$student = $stmt->fetch();

if (!$student) {
    header('Location: ../login.php');
    exit();
}

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();

// Get recent attendance
$stmt = $conn->prepare("
    SELECT a.*, DATE_FORMAT(a.attendance_date, '%M %d, %Y') as formatted_date
    FROM attendance a
    WHERE a.student_id = ? 
    ORDER BY a.attendance_date DESC 
    LIMIT 10
");
$stmt->execute([$student['id']]);
$recent_attendance = $stmt->fetchAll();

// Get attendance summary for current term
$stmt = $conn->prepare("
    SELECT 
        COUNT(*) as total_days,
        SUM(CASE WHEN status = 'Present' THEN 1 ELSE 0 END) as present_days,
        SUM(CASE WHEN status = 'Absent' THEN 1 ELSE 0 END) as absent_days,
        SUM(CASE WHEN status = 'Late' THEN 1 ELSE 0 END) as late_days
    FROM attendance 
    WHERE student_id = ? AND term_id = ?
");
$stmt->execute([$student['id'], $current_academic['current_term_id'] ?? 1]);
$attendance_summary = $stmt->fetch();

// Get recent grades
$stmt = $conn->prepare("
    SELECT sg.*, sub.subject_name, sub.subject_code,
           DATE_FORMAT(sg.created_at, '%M %d, %Y') as grade_date
    FROM student_grades sg
    JOIN subjects sub ON sg.subject_id = sub.id
    WHERE sg.student_id = ?
    ORDER BY sg.created_at DESC
    LIMIT 5
");
$stmt->execute([$student['id']]);
$recent_grades = $stmt->fetchAll();

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Dashboard - <?php echo $school_info['name']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .dashboard-container {
            padding: 20px;
        }
        
        .student-header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .student-code-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }
        
        .student-code {
            font-size: 2.5rem;
            font-weight: bold;
            letter-spacing: 2px;
            margin: 10px 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        
        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 15px;
        }
        
        .attendance-icon { background: linear-gradient(135deg, #17a2b8, #138496); }
        .grades-icon { background: linear-gradient(135deg, #ffc107, #e0a800); }
        .profile-icon { background: linear-gradient(135deg, #6f42c1, #5a32a3); }
        
        .stat-card {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 15px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .present-stat { background: rgba(40, 167, 69, 0.1); color: #28a745; }
        .absent-stat { background: rgba(220, 53, 69, 0.1); color: #dc3545; }
        .late-stat { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
        
        .grade-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .grade-item:last-child {
            border-bottom: none;
        }
        
        .grade-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-weight: bold;
            color: white;
        }
        
        .grade-A { background: #28a745; }
        .grade-B { background: #17a2b8; }
        .grade-C { background: #ffc107; color: #333; }
        .grade-D { background: #fd7e14; }
        .grade-F { background: #dc3545; }
        
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .profile-img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #fff;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-graduation-cap me-2"></i>
                Student Portal
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($student['first_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                        <li><a class="dropdown-item" href="grades.php"><i class="fas fa-chart-line me-2"></i>Grades</a></li>
                        <li><a class="dropdown-item" href="attendance.php"><i class="fas fa-calendar-check me-2"></i>Attendance</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="dashboard-container">
        <div class="container">
            <!-- Student Header -->
            <div class="student-header">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        <?php if ($student['profile_image']): ?>
                            <img src="../<?php echo htmlspecialchars($student['profile_image']); ?>" 
                                 alt="Profile" class="profile-img">
                        <?php else: ?>
                            <div class="profile-img d-flex align-items-center justify-content-center bg-primary text-white">
                                <i class="fas fa-user fa-2x"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                        <h2 class="mb-1">
                            <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                        </h2>
                        <p class="text-muted mb-1">
                            <i class="fas fa-graduation-cap me-2"></i>
                            <?php echo htmlspecialchars($student['class_name'] . ' - ' . $student['level_name']); ?>
                        </p>
                        <p class="text-muted mb-0">
                            <i class="fas fa-id-card me-2"></i>
                            Admission: <?php echo htmlspecialchars($student['admission_number']); ?>
                        </p>
                    </div>
                    <div class="col-md-4">
                        <div class="student-code-card">
                            <h5 class="mb-2">
                                <i class="fas fa-qrcode me-2"></i>
                                Your Student Code
                            </h5>
                            <div class="student-code"><?php echo htmlspecialchars($student['user_code']); ?></div>
                            <small>Share this code with your parents to link their account</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Attendance Summary -->
                <div class="col-md-4">
                    <div class="dashboard-card">
                        <div class="card-icon attendance-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <h5>Attendance Summary</h5>
                        <p class="text-muted">Current Term Performance</p>
                        
                        <div class="stat-card present-stat">
                            <div class="stat-number"><?php echo $attendance_summary['present_days'] ?? 0; ?></div>
                            <small>Present Days</small>
                        </div>
                        
                        <div class="stat-card absent-stat">
                            <div class="stat-number"><?php echo $attendance_summary['absent_days'] ?? 0; ?></div>
                            <small>Absent Days</small>
                        </div>
                        
                        <div class="stat-card late-stat">
                            <div class="stat-number"><?php echo $attendance_summary['late_days'] ?? 0; ?></div>
                            <small>Late Days</small>
                        </div>
                        
                        <a href="attendance.php" class="btn btn-outline-primary btn-sm mt-2">
                            <i class="fas fa-eye me-1"></i>View Details
                        </a>
                    </div>
                </div>

                <!-- Recent Grades -->
                <div class="col-md-4">
                    <div class="dashboard-card">
                        <div class="card-icon grades-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h5>Recent Grades</h5>
                        <p class="text-muted">Latest Assessment Results</p>
                        
                        <?php if (!empty($recent_grades)): ?>
                            <?php foreach ($recent_grades as $grade): ?>
                                <div class="grade-item">
                                    <div>
                                        <strong><?php echo htmlspecialchars($grade['subject_name']); ?></strong>
                                        <br><small class="text-muted"><?php echo $grade['grade_date']; ?></small>
                                    </div>
                                    <span class="grade-badge grade-<?php echo $grade['grade']; ?>">
                                        <?php echo $grade['grade']; ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-muted">No grades available yet.</p>
                        <?php endif; ?>
                        
                        <a href="grades.php" class="btn btn-outline-warning btn-sm mt-2">
                            <i class="fas fa-eye me-1"></i>View All Grades
                        </a>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="col-md-4">
                    <div class="dashboard-card">
                        <div class="card-icon profile-icon">
                            <i class="fas fa-user-cog"></i>
                        </div>
                        <h5>Quick Actions</h5>
                        <p class="text-muted">Student Services</p>
                        
                        <div class="d-grid gap-2">
                            <a href="attendance.php" class="btn btn-outline-info">
                                <i class="fas fa-calendar-alt me-2"></i>Attendance History
                            </a>
                            <a href="grades.php" class="btn btn-outline-success">
                                <i class="fas fa-chart-bar me-2"></i>Academic Records
                            </a>
                            <a href="fees.php" class="btn btn-outline-warning">
                                <i class="fas fa-money-bill me-2"></i>Fee Status
                            </a>
                            <a href="profile.php" class="btn btn-outline-primary">
                                <i class="fas fa-user me-2"></i>View Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
