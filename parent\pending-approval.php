<?php
/**
 * Pending Approval Page for Parents
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a parent
if (!isLoggedIn() || $_SESSION['user_type'] !== 'parent') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get parent information including approval status
$stmt = $conn->prepare("
    SELECT p.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, u.user_code, u.is_approved, u.created_at
    FROM parents p
    JOIN users u ON p.user_id = u.id
    WHERE p.user_id = ?
");
$stmt->execute([$user_id]);
$parent = $stmt->fetch();

if (!$parent) {
    header('Location: ../login.php');
    exit();
}

// If already approved, redirect to dashboard
if ($parent['is_approved']) {
    header('Location: index.php');
    exit();
}

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Pending Approval - <?php echo $school_info['name']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 50%, #ff4500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }
        
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .main-container {
            padding: 20px;
            position: relative;
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: calc(100vh - 80px);
        }
        
        .pending-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        .pending-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff8c00, #ff6b35);
        }

        .pending-icon {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            margin: 0 auto 30px;
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
            }
            50% {
                box-shadow: 0 8px 35px rgba(255, 193, 7, 0.5);
            }
            100% {
                box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
            }
        }

        .btn-orange {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            border: none;
            color: white;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 140, 0, 0.3);
        }
        
        .btn-orange:hover {
            background: linear-gradient(135deg, #ff9500 0%, #ff7635 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 140, 0, 0.4);
        }

        .info-card {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            border-left: 4px solid #17a2b8;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            text-align: left;
        }

        .timeline-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #28a745;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 0.9rem;
        }

        .timeline-icon.pending {
            background: #ffc107;
        }

        .timeline-icon.future {
            background: #6c757d;
        }

        .contact-info {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand fw-bold" href="../index.php">
                <i class="fas fa-graduation-cap me-2 text-orange"></i>
                <?php echo htmlspecialchars($school_info['name']); ?>
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle fw-semibold" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($parent['first_name'] . ' ' . $parent['last_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <div class="pending-card">
            <div class="pending-icon">
                <i class="fas fa-hourglass-half"></i>
            </div>
            
            <h2 class="mb-3">Account Pending Approval</h2>
            <p class="lead text-muted mb-4">
                Welcome, <?php echo htmlspecialchars($parent['first_name']); ?>! Your parent account has been successfully created and is currently awaiting administrative approval.
            </p>

            <div class="info-card">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>Account Information
                </h5>
                <div class="row text-start">
                    <div class="col-md-6">
                        <p><strong>Name:</strong> <?php echo htmlspecialchars($parent['first_name'] . ' ' . $parent['last_name']); ?></p>
                        <p><strong>Email:</strong> <?php echo htmlspecialchars($parent['email']); ?></p>
                        <p><strong>User Code:</strong> <code><?php echo htmlspecialchars($parent['user_code']); ?></code></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Registration Date:</strong> <?php echo date('M d, Y', strtotime($parent['created_at'])); ?></p>
                        <p><strong>Status:</strong> <span class="badge bg-warning">Pending Approval</span></p>
                        <p><strong>Occupation:</strong> <?php echo htmlspecialchars($parent['occupation'] ?? 'Not specified'); ?></p>
                    </div>
                </div>
            </div>

            <div class="info-card">
                <h5 class="mb-3">
                    <i class="fas fa-list-ol me-2"></i>Approval Process
                </h5>
                <div class="timeline-item">
                    <div class="timeline-icon">
                        <i class="fas fa-check"></i>
                    </div>
                    <div>
                        <strong>Account Created</strong>
                        <br><small class="text-muted">Your account has been successfully registered</small>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-icon pending">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div>
                        <strong>Administrative Review</strong>
                        <br><small class="text-muted">Your account is being reviewed by school administrators</small>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-icon future">
                        <i class="fas fa-unlock"></i>
                    </div>
                    <div>
                        <strong>Account Activation</strong>
                        <br><small class="text-muted">You will receive notification once approved</small>
                    </div>
                </div>
            </div>

            <div class="contact-info">
                <h6 class="mb-2">
                    <i class="fas fa-phone me-2"></i>Need Help?
                </h6>
                <p class="mb-2">
                    If you have any questions about your account status, please contact the school administration:
                </p>
                <p class="mb-1">
                    <i class="fas fa-envelope me-2"></i>
                    <a href="mailto:<?php echo htmlspecialchars($school_info['email']); ?>">
                        <?php echo htmlspecialchars($school_info['email']); ?>
                    </a>
                </p>
                <p class="mb-0">
                    <i class="fas fa-phone me-2"></i>
                    <?php echo htmlspecialchars($school_info['phone']); ?>
                </p>
            </div>

            <div class="mt-4">
                <button class="btn btn-orange me-2" onclick="location.reload()">
                    <i class="fas fa-sync-alt me-2"></i>Check Status
                </button>
                <a href="../logout.php" class="btn btn-outline-secondary">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
            </div>

            <div class="mt-3">
                <small class="text-muted">
                    <i class="fas fa-shield-alt me-1"></i>
                    Your account information is secure and will only be used for school-related communications.
                </small>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Auto-refresh every 30 seconds to check for approval status
        setInterval(function() {
            // Silent check for approval status
            fetch('check-approval-status.php')
                .then(response => response.json())
                .then(data => {
                    if (data.approved) {
                        // Show success message and redirect
                        alert('Great news! Your account has been approved. You will now be redirected to your dashboard.');
                        window.location.href = 'index.php';
                    }
                })
                .catch(error => {
                    console.log('Status check failed:', error);
                });
        }, 30000); // Check every 30 seconds

        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const pendingCard = document.querySelector('.pending-card');
            
            pendingCard.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.01)';
            });
            
            pendingCard.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
