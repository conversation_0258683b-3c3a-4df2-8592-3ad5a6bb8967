<?php
/**
 * Parent Messages & Communication
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/messaging-functions.php';

// Check if user is logged in and is a parent
if (!isLoggedIn() || $_SESSION['user_type'] !== 'parent') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get parent information
$stmt = $conn->prepare("
    SELECT p.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, u.user_code, u.is_approved
    FROM parents p
    JOIN users u ON p.user_id = u.id
    WHERE p.user_id = ?
");
$stmt->execute([$user_id]);
$parent = $stmt->fetch();

if (!$parent) {
    header('Location: ../login.php');
    exit();
}

// Check if parent account is approved
if (!$parent['is_approved']) {
    header('Location: pending-approval.php');
    exit();
}

// Get linked wards for message filtering
$stmt = $conn->prepare("
    SELECT s.id as student_id, u.first_name, u.last_name, s.admission_number
    FROM parent_student_relationships psr
    JOIN students s ON psr.student_id = s.id
    JOIN users u ON s.user_id = u.id
    WHERE psr.parent_id = ?
    ORDER BY u.first_name ASC
");
$stmt->execute([$parent['id']]);
$wards = $stmt->fetchAll();

$message = '';
$error = '';

// Handle new message submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_message'])) {
    $recipient_type = sanitizeInput($_POST['recipient_type']);
    $subject = sanitizeInput($_POST['subject']);
    $message_content = sanitizeInput($_POST['message_content']);
    $ward_id = isset($_POST['ward_id']) && $_POST['ward_id'] !== '' ? (int)$_POST['ward_id'] : null;
    $message_type = sanitizeInput($_POST['message_type'] ?? 'general');
    $priority = sanitizeInput($_POST['priority'] ?? 'normal');

    if (empty($subject) || empty($message_content)) {
        $error = 'Please fill in all required fields.';
    } else {
        // Use the new messaging system
        $result = sendMessage(
            $user_id,
            'parent',
            $subject,
            $message_content,
            $message_type,
            $priority,
            $ward_id,
            $recipient_type
        );

        if ($result['success']) {
            $message = 'Message sent successfully! Your message has been routed to the appropriate recipient(s).';
        } else {
            $error = 'Failed to send message: ' . $result['error'];
        }
    }
}

// Get recent messages using new system
try {
    $recent_messages = getUserMessages($user_id, 'parent', 10, 0);
} catch (Exception $e) {
    $recent_messages = [];
}

// Get message templates for quick communication
$message_templates = getMessageTemplates();

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Messages & Communication - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 50%, #ff4500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }

        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 1050;
        }

        .navbar-custom .dropdown-menu {
            z-index: 1060;
            position: absolute;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            margin-top: 8px;
            min-width: 220px;
        }

        .navbar-custom .dropdown-item {
            padding: 12px 20px;
            border-radius: 10px;
            margin: 4px 8px;
            transition: all 0.3s ease;
        }

        .navbar-custom .dropdown-item:hover {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            color: white;
            transform: translateX(5px);
        }

        .navbar-custom .dropdown-item i {
            width: 20px;
            text-align: center;
        }

        .main-container {
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .messages-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .messages-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff8c00, #ff6b35);
        }

        .btn-orange {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            border: none;
            color: white;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 140, 0, 0.3);
        }

        .btn-orange:hover {
            background: linear-gradient(135deg, #ff9500 0%, #ff7635 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 140, 0, 0.4);
        }

        .form-control:focus, .form-select:focus {
            border-color: #ff8c00;
            box-shadow: 0 0 0 0.2rem rgba(255, 140, 0, 0.25);
        }

        .breadcrumb {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 15px 20px;
            margin-bottom: 20px;
        }

        .breadcrumb-item a {
            color: white;
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: rgba(255, 255, 255, 0.8);
        }

        .message-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #ff8c00;
            transition: all 0.3s ease;
        }

        .message-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .message-status {
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 10px;
            font-weight: 600;
        }

        .status-sent {
            background: #e3f2fd;
            color: #1976d2;
        }

        .status-read {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status-replied {
            background: #fff3e0;
            color: #f57c00;
        }

        .quick-actions {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .quick-action-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            border-radius: 10px;
            padding: 15px;
            text-decoration: none;
            display: block;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .quick-action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            transform: translateX(5px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-users me-2 text-orange"></i>
                IGSS Parent Portal
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle fw-semibold" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($parent['first_name'] . ' ' . $parent['last_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="index.php"><i class="fas fa-home me-2"></i>Dashboard</a></li>
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
                        <li><a class="dropdown-item" href="ward-management.php"><i class="fas fa-users me-2"></i>Ward Management</a></li>
                        <li><a class="dropdown-item" href="payments.php"><i class="fas fa-credit-card me-2"></i>Fee Payments</a></li>
                        <li><a class="dropdown-item" href="messages.php"><i class="fas fa-envelope me-2"></i>Messages</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <div class="container">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php"><i class="fas fa-home me-1"></i>Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page"><i class="fas fa-envelope me-1"></i>Messages & Communication</li>
                </ol>
            </nav>

            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Send New Message -->
                <div class="col-lg-8">
                    <div class="messages-card">
                        <h4 class="mb-4">
                            <i class="fas fa-paper-plane me-2"></i>
                            Send New Message
                        </h4>
                        
                        <form method="POST" action="">
                            <input type="hidden" name="send_message" value="1">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="recipient_type" class="form-label">
                                            <i class="fas fa-users me-1"></i>Send To *
                                        </label>
                                        <select class="form-select" id="recipient_type" name="recipient_type" required onchange="updateRecipientInfo()">
                                            <option value="">Select Recipient</option>
                                            <option value="admin">School Administration</option>
                                            <option value="form_teacher">Form Teacher</option>
                                            <option value="class_teacher">Subject Teacher</option>
                                        </select>
                                        <small class="form-text text-muted" id="recipient_info"></small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="ward_id" class="form-label">
                                            <i class="fas fa-child me-1"></i>Regarding Ward
                                        </label>
                                        <select class="form-select" id="ward_id" name="ward_id" onchange="updateFormTeacherInfo()">
                                            <option value="">General Inquiry</option>
                                            <?php foreach ($wards as $ward): ?>
                                                <option value="<?php echo $ward['student_id']; ?>"
                                                        data-name="<?php echo htmlspecialchars($ward['first_name'] . ' ' . $ward['last_name']); ?>"
                                                        data-admission="<?php echo htmlspecialchars($ward['admission_number']); ?>">
                                                    <?php echo htmlspecialchars($ward['first_name'] . ' ' . $ward['last_name'] . ' (' . $ward['admission_number'] . ')'); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <small class="form-text text-muted" id="form_teacher_info"></small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="message_type" class="form-label">
                                            <i class="fas fa-tag me-1"></i>Message Category
                                        </label>
                                        <select class="form-select" id="message_type" name="message_type">
                                            <option value="general">General Inquiry</option>
                                            <option value="academic">Academic Concern</option>
                                            <option value="attendance">Attendance Issue</option>
                                            <option value="disciplinary">Disciplinary Matter</option>
                                            <option value="fee_inquiry">Fee Payment Issue</option>
                                            <option value="emergency">Emergency</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="priority" class="form-label">
                                            <i class="fas fa-exclamation-circle me-1"></i>Priority Level
                                        </label>
                                        <select class="form-select" id="priority" name="priority">
                                            <option value="normal">Normal</option>
                                            <option value="high">High</option>
                                            <option value="urgent">Urgent</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="subject" class="form-label">
                                    <i class="fas fa-tag me-1"></i>Subject *
                                </label>
                                <input type="text" class="form-control" id="subject" name="subject" 
                                       placeholder="Enter message subject" required>
                            </div>

                            <div class="mb-3">
                                <label for="message_content" class="form-label">
                                    <i class="fas fa-comment me-1"></i>Message *
                                </label>
                                <textarea class="form-control" id="message_content" name="message_content" 
                                          rows="6" placeholder="Type your message here..." required></textarea>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                                </a>
                                <button type="submit" class="btn btn-orange">
                                    <i class="fas fa-paper-plane me-2"></i>Send Message
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Recent Messages -->
                    <div class="messages-card">
                        <h4 class="mb-4">
                            <i class="fas fa-history me-2"></i>
                            Recent Messages
                        </h4>
                        
                        <?php if (!empty($recent_messages)): ?>
                            <?php foreach ($recent_messages as $msg): ?>
                                <div class="message-item">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="mb-0"><?php echo htmlspecialchars($msg['subject']); ?></h6>
                                        <div class="d-flex align-items-center">
                                            <?php if ($msg['priority'] === 'urgent'): ?>
                                                <span class="badge bg-danger me-2">Urgent</span>
                                            <?php elseif ($msg['priority'] === 'high'): ?>
                                                <span class="badge bg-warning me-2">High</span>
                                            <?php endif; ?>
                                            <span class="message-status status-<?php echo $msg['is_read'] ? 'read' : 'sent'; ?>">
                                                <?php echo $msg['is_read'] ? 'Read' : 'Sent'; ?>
                                            </span>
                                        </div>
                                    </div>
                                    <p class="text-muted mb-2">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        <?php if ($msg['sender_type'] === 'parent'): ?>
                                            To: <?php echo ucwords(str_replace('_', ' ', $msg['message_type'])); ?> Recipients
                                        <?php else: ?>
                                            From: <?php echo htmlspecialchars($msg['sender_first_name'] . ' ' . $msg['sender_last_name']); ?>
                                            (<?php echo ucfirst($msg['sender_user_type']); ?>)
                                        <?php endif; ?>
                                        <?php if ($msg['ward_first_name']): ?>
                                            <br><i class="fas fa-child me-1"></i>
                                            Ward: <?php echo htmlspecialchars($msg['ward_first_name'] . ' ' . $msg['ward_last_name']); ?>
                                        <?php endif; ?>
                                    </p>
                                    <p class="mb-2"><?php echo htmlspecialchars(substr($msg['message_content'], 0, 150)) . (strlen($msg['message_content']) > 150 ? '...' : ''); ?></p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo date('M j, Y g:i A', strtotime($msg['created_at'])); ?>
                                        </small>
                                        <?php if ($msg['sender_type'] !== 'parent' && !$msg['is_replied']): ?>
                                            <button class="btn btn-sm btn-outline-primary" onclick="replyToMessage(<?php echo $msg['id']; ?>, '<?php echo htmlspecialchars($msg['subject']); ?>')">
                                                <i class="fas fa-reply me-1"></i>Reply
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No messages yet</h5>
                                <p class="text-muted">Your sent messages will appear here.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions & Information -->
                <div class="col-lg-4">
                    <div class="quick-actions">
                        <h5 class="text-white mb-3">
                            <i class="fas fa-bolt me-2"></i>
                            Quick Communication
                        </h5>
                        
                        <a href="#" class="quick-action-btn" onclick="fillQuickMessage('Emergency Contact', 'administration')">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Emergency Contact
                        </a>
                        
                        <a href="#" class="quick-action-btn" onclick="fillQuickMessage('Request Meeting', 'class_teacher')">
                            <i class="fas fa-calendar me-2"></i>
                            Request Parent-Teacher Meeting
                        </a>
                        
                        <a href="#" class="quick-action-btn" onclick="fillQuickMessage('Academic Concern', 'class_teacher')">
                            <i class="fas fa-graduation-cap me-2"></i>
                            Academic Concern
                        </a>
                        
                        <a href="#" class="quick-action-btn" onclick="fillQuickMessage('Attendance Inquiry', 'administration')">
                            <i class="fas fa-calendar-check me-2"></i>
                            Attendance Inquiry
                        </a>
                        
                        <a href="#" class="quick-action-btn" onclick="fillQuickMessage('Fee Payment Issue', 'administration')">
                            <i class="fas fa-credit-card me-2"></i>
                            Fee Payment Issue
                        </a>
                    </div>

                    <div class="messages-card">
                        <h5 class="mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            Communication Guidelines
                        </h5>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Be clear and specific in your messages</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Include your ward's name and class</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Allow 24-48 hours for response</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Use appropriate tone and language</small>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function fillQuickMessage(subject, recipient) {
            document.getElementById('subject').value = subject;
            document.getElementById('recipient_type').value = recipient;

            // Set appropriate message type based on subject
            const messageTypeSelect = document.getElementById('message_type');
            if (subject.includes('Emergency')) {
                messageTypeSelect.value = 'emergency';
                document.getElementById('priority').value = 'urgent';
            } else if (subject.includes('Academic')) {
                messageTypeSelect.value = 'academic';
            } else if (subject.includes('Attendance')) {
                messageTypeSelect.value = 'attendance';
            } else if (subject.includes('Fee')) {
                messageTypeSelect.value = 'fee_inquiry';
            }

            updateRecipientInfo();
            document.getElementById('subject').focus();
        }

        function updateRecipientInfo() {
            const recipientType = document.getElementById('recipient_type').value;
            const infoElement = document.getElementById('recipient_info');

            switch(recipientType) {
                case 'admin':
                    infoElement.textContent = 'Message will be sent to school administration for general inquiries, fee issues, and administrative matters';
                    infoElement.className = 'form-text text-info';
                    break;
                case 'form_teacher':
                    infoElement.textContent = 'Message will be sent to your ward\'s form teacher (class teacher) for academic and behavioral matters';
                    infoElement.className = 'form-text text-info';
                    break;
                case 'class_teacher':
                    infoElement.textContent = 'Message will be sent to subject teachers for specific subject-related inquiries';
                    infoElement.className = 'form-text text-info';
                    break;
                default:
                    infoElement.textContent = '';
            }
        }

        function updateFormTeacherInfo() {
            const wardSelect = document.getElementById('ward_id');
            const selectedOption = wardSelect.options[wardSelect.selectedIndex];
            const infoElement = document.getElementById('form_teacher_info');

            if (wardSelect.value && selectedOption.dataset.name) {
                // In a real implementation, you'd make an AJAX call to get form teacher info
                infoElement.textContent = `Form teacher information will be loaded for ${selectedOption.dataset.name}`;
                infoElement.className = 'form-text text-success';
            } else {
                infoElement.textContent = '';
            }
        }

        function replyToMessage(messageId, subject) {
            // Scroll to form
            document.querySelector('.messages-card').scrollIntoView({ behavior: 'smooth' });

            // Fill form for reply
            document.getElementById('subject').value = 'Re: ' + subject;
            document.getElementById('message_content').focus();

            // You could add a hidden field to track the original message ID for threading
            let replyField = document.getElementById('reply_to_message_id');
            if (!replyField) {
                replyField = document.createElement('input');
                replyField.type = 'hidden';
                replyField.id = 'reply_to_message_id';
                replyField.name = 'reply_to_message_id';
                document.querySelector('form').appendChild(replyField);
            }
            replyField.value = messageId;
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateRecipientInfo();
            updateFormTeacherInfo();
        });
    </script>
</body>
</html>
