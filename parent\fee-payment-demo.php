<?php
/**
 * Demo Parent Fee Payment Page
 * Shows the simplified fee structure
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Demo data - in real implementation, this would come from session
$studentId = 1; // Demo student ID
$currentTerm = 3; // Demo current term (3rd term to show exam fees)

$message = '';
$error = '';

// Handle payment upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $_POST['action'] === 'upload_payment') {
    try {
        $conn = getDBConnection();

        $depositorName = sanitizeInput($_POST['depositor_name']);
        $paymentDate = $_POST['payment_date'];
        $amountPaid = floatval($_POST['amount_paid']);
        $paymentMethod = $_POST['payment_method'];
        $paymentNotes = sanitizeInput($_POST['payment_notes'] ?? '');

        // Handle file upload
        $receiptPath = '';
        if (isset($_FILES['payment_receipt']) && $_FILES['payment_receipt']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = uploadFile($_FILES['payment_receipt'], 'uploads/receipts/');
            if ($uploadResult['success']) {
                $receiptPath = $uploadResult['filename'];
            } else {
                throw new Exception($uploadResult['message']);
            }
        }

        // Insert payment record
        $stmt = $conn->prepare("
            INSERT INTO fee_payments (student_id, session_id, term_id, amount_paid, payment_date,
                                    payment_method, depositor_name, receipt_path, payment_notes,
                                    status, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())
        ");

        $stmt->execute([
            $studentId,
            $currentAcademicInfo['session'],
            $currentAcademicInfo['term'],
            $amountPaid,
            $paymentDate,
            $paymentMethod,
            $depositorName,
            $receiptPath,
            $paymentNotes
        ]);

        $message = 'Payment details uploaded successfully! Your payment is now pending admin verification.';

    } catch (Exception $e) {
        $error = 'Failed to upload payment details: ' . $e->getMessage();
    }
}

// Get fee structure for this student
$feeStructure = getParentFeeStructure($studentId, $currentTerm);
$currentAcademicInfo = getCurrentAcademicInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fee Payment - IGSS Parent Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .fee-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .fee-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .fee-item {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .fee-item:last-child {
            border-bottom: none;
        }

        .fee-amount {
            font-weight: bold;
            color: #28a745;
            font-size: 1.1rem;
        }

        .total-section {
            background: #f8f9fa;
            padding: 20px;
            border-top: 2px solid #667eea;
        }

        .exam-fee-badge {
            background: #ff6b35;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            margin-left: 10px;
        }

        .pay-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            color: white;
            font-weight: bold;
            font-size: 1.1rem;
            width: 100%;
            margin-top: 20px;
        }

        .pay-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <!-- Alerts -->
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Header -->
                <div class="fee-card">
                    <div class="fee-header">
                        <h4><i class="fas fa-money-bill-wave me-2"></i>Fee Payment</h4>
                        <p class="mb-0">Academic Session: <?php echo $currentAcademicInfo['session']; ?> - Term <?php echo $currentAcademicInfo['term']; ?></p>
                        <?php if (!empty($feeStructure['student_class'])): ?>
                            <small>Student Class: <?php echo $feeStructure['student_class']['class_name']; ?></small>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Regular Fees -->
                <div class="fee-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>School Fees</h5>
                    </div>
                    <div class="card-body p-0">
                        <?php foreach ($feeStructure['regular_fees'] as $fee): ?>
                            <div class="fee-item">
                                <div>
                                    <strong><?php echo htmlspecialchars($fee['fee_name']); ?></strong>
                                    <?php if (!empty($fee['fee_description'])): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($fee['fee_description']); ?></small>
                                    <?php endif; ?>
                                </div>
                                <div class="fee-amount">₦<?php echo number_format($fee['amount'], 2); ?></div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Exam Fees (if applicable) -->
                <?php if (!empty($feeStructure['exam_fees'])): ?>
                    <div class="fee-card">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Examination Fees</h5>
                            <small>Special fees for final year students</small>
                        </div>
                        <div class="card-body p-0">
                            <?php foreach ($feeStructure['exam_fees'] as $fee): ?>
                                <div class="fee-item">
                                    <div>
                                        <strong><?php echo htmlspecialchars($fee['fee_name']); ?></strong>
                                        <span class="exam-fee-badge">EXAM FEE</span>
                                        <?php if (!empty($fee['fee_description'])): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($fee['fee_description']); ?></small>
                                        <?php endif; ?>
                                    </div>
                                    <div class="fee-amount">₦<?php echo number_format($fee['amount'], 2); ?></div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Total and Payment -->
                <div class="fee-card">
                    <div class="total-section">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 class="mb-0">Total Amount</h4>
                            <h3 class="mb-0 text-success">₦<?php echo number_format($feeStructure['total'], 2); ?></h3>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Payment Instructions:</strong>
                            <ol class="mb-0 mt-2">
                                <li>Make payment to the school's bank account</li>
                                <li>Upload payment details and receipt/teller below</li>
                                <li>Wait for admin confirmation</li>
                            </ol>
                            <?php if (!empty($feeStructure['exam_fees'])): ?>
                                <br><strong>Note:</strong> Exam fees are required for final year students during the third term.
                            <?php endif; ?>
                        </div>

                        <button class="pay-button" onclick="showPaymentForm()">
                            <i class="fas fa-upload me-2"></i>Upload Payment Details
                        </button>
                    </div>
                </div>

                <!-- Bank Account Information -->
                <div class="fee-card" id="bankInfo" style="display: none;">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-university me-2"></i>School Bank Account Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label"><strong>Bank Name:</strong></label>
                                    <p class="mb-0">First Bank Nigeria Limited</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label"><strong>Account Name:</strong></label>
                                    <p class="mb-0">Intellectual Giant Secondary School</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label"><strong>Account Number:</strong></label>
                                    <p class="mb-0 h5 text-primary">**********</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label"><strong>Sort Code:</strong></label>
                                    <p class="mb-0">*********</p>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Important:</strong> Always use your child's name or admission number as payment reference/narration.
                        </div>
                    </div>
                </div>

                <!-- Payment Upload Form (Hidden by default) -->
                <div class="fee-card" id="paymentForm" style="display: none;">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-upload me-2"></i>Upload Payment Details</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="upload_payment">
                            <input type="hidden" name="total_amount" value="<?php echo $feeStructure['total']; ?>">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-user me-1"></i>Depositor Name</label>
                                        <input type="text" name="depositor_name" class="form-control"
                                               placeholder="Name of person who made the payment" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-calendar me-1"></i>Payment Date</label>
                                        <input type="date" name="payment_date" class="form-control" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-money-bill me-1"></i>Amount Paid</label>
                                        <input type="number" name="amount_paid" class="form-control"
                                               value="<?php echo $feeStructure['total']; ?>"
                                               step="0.01" min="<?php echo $feeStructure['total']; ?>"
                                               max="<?php echo $feeStructure['total']; ?>" readonly>
                                        <small class="text-muted">Amount must match the total fee</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-university me-1"></i>Payment Method</label>
                                        <select name="payment_method" class="form-select" required>
                                            <option value="">Select Payment Method</option>
                                            <option value="bank_transfer">Bank Transfer</option>
                                            <option value="bank_deposit">Bank Deposit</option>
                                            <option value="pos">POS Payment</option>
                                            <option value="mobile_money">Mobile Money</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label"><i class="fas fa-receipt me-1"></i>Upload Receipt/Teller</label>
                                <input type="file" name="payment_receipt" class="form-control"
                                       accept=".jpg,.jpeg,.png,.pdf" required>
                                <small class="text-muted">Upload clear image of bank teller, receipt, or transaction proof (JPG, PNG, PDF only)</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label"><i class="fas fa-comment me-1"></i>Additional Notes (Optional)</label>
                                <textarea name="payment_notes" class="form-control" rows="3"
                                          placeholder="Any additional information about the payment"></textarea>
                            </div>

                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Important:</strong> Ensure all details are correct before submitting.
                                Your payment will be reviewed by the admin and you will be notified of the status.
                            </div>

                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-success flex-fill">
                                    <i class="fas fa-paper-plane me-2"></i>Submit Payment Details
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="hidePaymentForm()">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showPaymentForm() {
            document.getElementById('bankInfo').style.display = 'block';
            document.getElementById('paymentForm').style.display = 'block';
            document.getElementById('bankInfo').scrollIntoView({ behavior: 'smooth' });
        }

        function hidePaymentForm() {
            document.getElementById('bankInfo').style.display = 'none';
            document.getElementById('paymentForm').style.display = 'none';
        }

        // Set today's date as default for payment date
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            const paymentDateInput = document.querySelector('input[name="payment_date"]');
            if (paymentDateInput) {
                paymentDateInput.value = today;
                paymentDateInput.max = today; // Prevent future dates
            }
        });

        // Validate file upload
        document.addEventListener('change', function(e) {
            if (e.target.name === 'payment_receipt') {
                const file = e.target.files[0];
                if (file) {
                    const maxSize = 5 * 1024 * 1024; // 5MB
                    if (file.size > maxSize) {
                        alert('File size must be less than 5MB');
                        e.target.value = '';
                        return;
                    }

                    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
                    if (!allowedTypes.includes(file.type)) {
                        alert('Only JPG, PNG, and PDF files are allowed');
                        e.target.value = '';
                        return;
                    }
                }
            }
        });
    </script>
</body>
</html>
