<?php
/**
 * Parent Approval Notification System
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

/**
 * Send approval notification email to parent
 */
function sendParentApprovalNotification($user_id, $action, $admin_notes = '') {
    try {
        $conn = getDBConnection();
        
        // Get parent and school information
        $stmt = $conn->prepare("
            SELECT u.first_name, u.last_name, u.email, u.user_code
            FROM users u
            WHERE u.id = ? AND u.user_type = 'parent'
        ");
        $stmt->execute([$user_id]);
        $parent = $stmt->fetch();
        
        if (!$parent) {
            return false;
        }
        
        $school_info = getSchoolInfo();
        
        // Prepare email content based on action
        $subject = '';
        $message = '';
        
        switch ($action) {
            case 'approved':
                $subject = "Account Approved - {$school_info['name']} Parent Portal";
                $message = generateApprovalEmailContent($parent, $school_info, $admin_notes);
                break;
                
            case 'rejected':
                $subject = "Account Registration Update - {$school_info['name']}";
                $message = generateRejectionEmailContent($parent, $school_info, $admin_notes);
                break;
                
            case 'reactivated':
                $subject = "Account Reactivated - {$school_info['name']} Parent Portal";
                $message = generateReactivationEmailContent($parent, $school_info, $admin_notes);
                break;
                
            default:
                return false;
        }
        
        // Send email (using PHP's mail function - in production, use a proper email service)
        $headers = [
            'From' => $school_info['email'],
            'Reply-To' => $school_info['email'],
            'Content-Type' => 'text/html; charset=UTF-8',
            'X-Mailer' => 'IGSS Management System'
        ];
        
        $success = mail($parent['email'], $subject, $message, implode("\r\n", $headers));
        
        // Log the notification attempt
        $stmt = $conn->prepare("
            INSERT INTO parent_approval_notifications (user_id, notification_type, is_sent, sent_at)
            VALUES (?, 'email', ?, ?)
        ");
        $stmt->execute([$user_id, $success ? 1 : 0, $success ? date('Y-m-d H:i:s') : null]);
        
        return $success;
        
    } catch (Exception $e) {
        error_log("Failed to send parent approval notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate approval email content
 */
function generateApprovalEmailContent($parent, $school_info, $admin_notes) {
    $login_url = getBaseUrl() . '/login.php';
    
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .success-badge { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 20px 0; text-align: center; }
            .button { display: inline-block; background: #ff8c00; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 0.9em; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>{$school_info['name']}</h1>
                <p>Parent Portal Access Approved</p>
            </div>
            
            <div class='content'>
                <div class='success-badge'>
                    <strong>🎉 Congratulations! Your account has been approved.</strong>
                </div>
                
                <p>Dear {$parent['first_name']} {$parent['last_name']},</p>
                
                <p>We are pleased to inform you that your parent account registration for the {$school_info['name']} Parent Portal has been <strong>approved</strong>.</p>
                
                <p><strong>Your Account Details:</strong></p>
                <ul>
                    <li><strong>Name:</strong> {$parent['first_name']} {$parent['last_name']}</li>
                    <li><strong>Email:</strong> {$parent['email']}</li>
                    <li><strong>User Code:</strong> {$parent['user_code']}</li>
                </ul>
                
                <p>You can now access the parent portal to:</p>
                <ul>
                    <li>Link your children's accounts using their admission numbers</li>
                    <li>Monitor academic progress and grades</li>
                    <li>Track attendance records</li>
                    <li>Make fee payments online</li>
                    <li>Communicate with teachers and school administration</li>
                </ul>
                
                " . ($admin_notes ? "<p><strong>Admin Notes:</strong> " . htmlspecialchars($admin_notes) . "</p>" : "") . "
                
                <div style='text-align: center;'>
                    <a href='{$login_url}' class='button'>Access Parent Portal</a>
                </div>
                
                <p>If you have any questions or need assistance, please contact us:</p>
                <ul>
                    <li><strong>Email:</strong> {$school_info['email']}</li>
                    <li><strong>Phone:</strong> {$school_info['phone']}</li>
                </ul>
                
                <p>Thank you for choosing {$school_info['name']}. We look forward to partnering with you in your child's educational journey.</p>
                
                <p>Best regards,<br>
                <strong>{$school_info['name']} Administration</strong></p>
            </div>
            
            <div class='footer'>
                <p>This is an automated message from {$school_info['name']} Management System.</p>
                <p>Please do not reply to this email. For support, contact {$school_info['email']}</p>
            </div>
        </div>
    </body>
    </html>";
}

/**
 * Generate rejection email content
 */
function generateRejectionEmailContent($parent, $school_info, $admin_notes) {
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .warning-badge { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 20px 0; text-align: center; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 0.9em; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>{$school_info['name']}</h1>
                <p>Account Registration Update</p>
            </div>
            
            <div class='content'>
                <div class='warning-badge'>
                    <strong>Account Registration Not Approved</strong>
                </div>
                
                <p>Dear {$parent['first_name']} {$parent['last_name']},</p>
                
                <p>Thank you for your interest in the {$school_info['name']} Parent Portal. After reviewing your registration, we are unable to approve your account at this time.</p>
                
                " . ($admin_notes ? "<p><strong>Reason:</strong> " . htmlspecialchars($admin_notes) . "</p>" : "") . "
                
                <p>If you believe this is an error or would like to discuss your registration, please contact our administration office:</p>
                <ul>
                    <li><strong>Email:</strong> {$school_info['email']}</li>
                    <li><strong>Phone:</strong> {$school_info['phone']}</li>
                </ul>
                
                <p>We appreciate your understanding and look forward to assisting you.</p>
                
                <p>Best regards,<br>
                <strong>{$school_info['name']} Administration</strong></p>
            </div>
            
            <div class='footer'>
                <p>This is an automated message from {$school_info['name']} Management System.</p>
                <p>Please do not reply to this email. For support, contact {$school_info['email']}</p>
            </div>
        </div>
    </body>
    </html>";
}

/**
 * Generate reactivation email content
 */
function generateReactivationEmailContent($parent, $school_info, $admin_notes) {
    $login_url = getBaseUrl() . '/login.php';
    
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .info-badge { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 5px; margin: 20px 0; text-align: center; }
            .button { display: inline-block; background: #ff8c00; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 0.9em; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>{$school_info['name']}</h1>
                <p>Account Reactivated</p>
            </div>
            
            <div class='content'>
                <div class='info-badge'>
                    <strong>Your account has been reactivated</strong>
                </div>
                
                <p>Dear {$parent['first_name']} {$parent['last_name']},</p>
                
                <p>Your parent account for the {$school_info['name']} Parent Portal has been <strong>reactivated</strong>.</p>
                
                <p>You can now log in and access all parent portal features.</p>
                
                " . ($admin_notes ? "<p><strong>Admin Notes:</strong> " . htmlspecialchars($admin_notes) . "</p>" : "") . "
                
                <div style='text-align: center;'>
                    <a href='{$login_url}' class='button'>Access Parent Portal</a>
                </div>
                
                <p>If you have any questions, please contact us:</p>
                <ul>
                    <li><strong>Email:</strong> {$school_info['email']}</li>
                    <li><strong>Phone:</strong> {$school_info['phone']}</li>
                </ul>
                
                <p>Best regards,<br>
                <strong>{$school_info['name']} Administration</strong></p>
            </div>
            
            <div class='footer'>
                <p>This is an automated message from {$school_info['name']} Management System.</p>
                <p>Please do not reply to this email. For support, contact {$school_info['email']}</p>
            </div>
        </div>
    </body>
    </html>";
}

/**
 * Get base URL for the application
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $path = dirname($_SERVER['SCRIPT_NAME'] ?? '');
    return $protocol . '://' . $host . $path;
}

/**
 * Send bulk approval notifications
 */
function sendBulkApprovalNotifications($user_ids, $action, $admin_notes = '') {
    $success_count = 0;
    $total_count = count($user_ids);
    
    foreach ($user_ids as $user_id) {
        if (sendParentApprovalNotification($user_id, $action, $admin_notes)) {
            $success_count++;
        }
    }
    
    return [
        'total' => $total_count,
        'success' => $success_count,
        'failed' => $total_count - $success_count
    ];
}

/**
 * Get pending notification queue
 */
function getPendingNotifications() {
    try {
        $conn = getDBConnection();
        
        $stmt = $conn->prepare("
            SELECT 
                pan.id,
                pan.user_id,
                u.first_name,
                u.last_name,
                u.email,
                pan.notification_type,
                pan.created_at
            FROM parent_approval_notifications pan
            JOIN users u ON pan.user_id = u.id
            WHERE pan.is_sent = FALSE
            ORDER BY pan.created_at ASC
        ");
        $stmt->execute();
        
        return $stmt->fetchAll();
        
    } catch (Exception $e) {
        error_log("Failed to get pending notifications: " . $e->getMessage());
        return [];
    }
}

/**
 * Mark notification as sent
 */
function markNotificationAsSent($notification_id) {
    try {
        $conn = getDBConnection();
        
        $stmt = $conn->prepare("
            UPDATE parent_approval_notifications 
            SET is_sent = TRUE, sent_at = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$notification_id]);
        
        return true;
        
    } catch (Exception $e) {
        error_log("Failed to mark notification as sent: " . $e->getMessage());
        return false;
    }
}
?>
