<?php
/**
 * Teacher Profile Page
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a teacher
if (!isLoggedIn() || $_SESSION['user_type'] !== 'teacher') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];
$message = '';
$error = '';

// Get teacher information
$stmt = $conn->prepare("
    SELECT t.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, u.user_code, u.address, u.gender, u.created_at
    FROM teachers t
    JOIN users u ON t.user_id = u.id
    WHERE t.user_id = ?
");
$stmt->execute([$user_id]);
$teacher = $stmt->fetch();

if (!$teacher) {
    header('Location: ../login.php');
    exit();
}

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $first_name = sanitizeInput($_POST['first_name']);
    $last_name = sanitizeInput($_POST['last_name']);
    $middle_name = sanitizeInput($_POST['middle_name']);
    $email = sanitizeInput($_POST['email']);
    $phone = sanitizeInput($_POST['phone']);
    $address = sanitizeInput($_POST['address']);
    $qualification = sanitizeInput($_POST['qualification']);
    $specialization = sanitizeInput($_POST['specialization']);

    if (empty($first_name) || empty($last_name) || empty($email)) {
        $error = 'Please fill in all required fields.';
    } else {
        try {
            $conn->beginTransaction();

            // Update users table
            $stmt = $conn->prepare("
                UPDATE users
                SET first_name = ?, last_name = ?, middle_name = ?, email = ?, phone = ?, address = ?
                WHERE id = ?
            ");
            $stmt->execute([$first_name, $last_name, $middle_name, $email, $phone, $address, $user_id]);

            // Update teachers table
            $stmt = $conn->prepare("
                UPDATE teachers
                SET qualification = ?, specialization = ?
                WHERE user_id = ?
            ");
            $stmt->execute([$qualification, $specialization, $user_id]);

            $conn->commit();
            $message = 'Profile updated successfully!';

            // Update session data
            $_SESSION['full_name'] = $first_name . ' ' . $last_name;

            // Refresh teacher data
            $stmt = $conn->prepare("
                SELECT t.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, u.user_code, u.address, u.gender, u.created_at
                FROM teachers t
                JOIN users u ON t.user_id = u.id
                WHERE t.user_id = ?
            ");
            $stmt->execute([$user_id]);
            $teacher = $stmt->fetch();

        } catch (Exception $e) {
            $conn->rollBack();
            $error = 'Failed to update profile: ' . $e->getMessage();
        }
    }
}

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();

// Get teacher statistics
$stats = [];

// Get assigned subjects count
$stmt = $conn->prepare("
    SELECT COUNT(DISTINCT sa.subject_id) as subject_count
    FROM subject_assignments sa
    WHERE sa.teacher_id = ? AND sa.is_active = 1
");
$stmt->execute([$teacher['id']]);
$stats['subjects'] = $stmt->fetchColumn() ?: 0;

// Get assigned classes count
$stmt = $conn->prepare("
    SELECT COUNT(DISTINCT sa.class_id) as class_count
    FROM subject_assignments sa
    WHERE sa.teacher_id = ? AND sa.is_active = 1
");
$stmt->execute([$teacher['id']]);
$stats['classes'] = $stmt->fetchColumn() ?: 0;

// Check if form teacher
$stmt = $conn->prepare("
    SELECT c.class_name
    FROM form_teachers ft
    JOIN classes c ON ft.class_id = c.id
    WHERE ft.teacher_id = ? AND ft.is_active = 1
    LIMIT 1
");
$stmt->execute([$teacher['id']]);
$form_class = $stmt->fetchColumn();

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - <?php echo $school_info['name']; ?></title>

    <!-- Favicon -->
    <link rel="icon" type="image/jpeg" href="../images/logo.jpg?v=<?php echo time(); ?>">
    <link rel="shortcut icon" type="image/jpeg" href="../images/logo.jpg?v=<?php echo time(); ?>">
    <link rel="apple-touch-icon" href="../images/logo.jpg?v=<?php echo time(); ?>">
    <meta name="msapplication-TileImage" content="../images/logo.jpg?v=<?php echo time(); ?>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #ff7b00 0%, #ff9500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }

        .sidebar {
            background: linear-gradient(135deg, #ff7b00 0%, #ff9500 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        /* Custom white scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.8);
        }

        /* For Firefox */
        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.6) rgba(255, 255, 255, 0.2);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 80px;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.25) 100%);
            border-radius: 60px 0 0 60px;
        }

        .sidebar-header {
            padding: 25px 20px;
            text-align: center;
            position: relative;
            background: rgba(255, 255, 255, 0.01);
            border-radius: 0 0 30px 30px;
            margin-bottom: 15px;
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 700;
            font-size: 1.4rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header small {
            opacity: 0.9;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            margin-bottom: 80px;
        }

        .sidebar-menu li {
            margin: 1px 15px;
        }

        .sidebar-menu a {
            color: white;
            text-decoration: none;
            padding: 8px 20px;
            display: block;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 25px;
            position: relative;
            font-weight: 500;
            font-size: 0.85rem;
            background: rgba(255, 255, 255, 0.05);
            border: none;
            margin-bottom: 1px;
        }

        .sidebar-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover::before, .sidebar-menu a.active::before {
            opacity: 1;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            transform: translateX(5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .sidebar-menu a.active {
            background: rgba(255, 255, 255, 0.2);
        }

        .back-to-home {
            position: absolute;
            bottom: 60px;
            left: 15px;
            right: 15px;
            z-index: 1001;
        }

        .back-to-home .btn {
            background: rgba(255, 255, 255, 0.3);
            border: 3px solid rgba(255, 255, 255, 0.6);
            color: white;
            font-weight: 800;
            padding: 12px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            width: 100%;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            font-size: 0.95rem;
        }

        .back-to-home .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
            position: relative;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px 30px;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 25px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            font-weight: bold;
            margin: 0 auto 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .profile-info {
            text-align: center;
            margin-bottom: 30px;
        }

        .profile-name {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .profile-code {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 15px;
        }

        .profile-stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #6c757d;
        }

        .info-value {
            color: #2c3e50;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-chalkboard-teacher me-2"></i>IGSS Teacher</h4>
            <small>Management Portal</small>
        </div>

        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
            <li><a href="my-classes.php"><i class="fas fa-users me-2"></i>My Classes</a></li>
            <li><a href="my-subjects.php"><i class="fas fa-book me-2"></i>My Subjects</a></li>
            <li><a href="attendance.php"><i class="fas fa-calendar-check me-2"></i>Attendance</a></li>
            <li><a href="grades.php"><i class="fas fa-graduation-cap me-2"></i>Grades</a></li>
            <li><a href="profile.php" class="active"><i class="fas fa-user me-2"></i>My Profile</a></li>
            <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
        </ul>

        <!-- Back to Home Button -->
        <div class="back-to-home">
            <a href="../index.php" class="btn">
                <i class="fas fa-home me-2"></i>Back to Home
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><i class="fas fa-user me-2"></i>My Profile</h4>
                    <small class="text-muted">Manage your personal information</small>
                </div>
                <div>
                    <span class="badge bg-primary me-2">
                        Session: <?php echo $current_academic['session']; ?>
                    </span>
                    <span class="badge bg-info">
                        Term: <?php echo TERMS[$current_academic['term']]; ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Alerts -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Profile Overview -->
            <div class="col-lg-4">
                <div class="content-card">
                    <div class="profile-info">
                        <div class="profile-avatar">
                            <?php echo strtoupper(substr($teacher['first_name'], 0, 1) . substr($teacher['last_name'], 0, 1)); ?>
                        </div>
                        <div class="profile-name">
                            <?php echo htmlspecialchars($teacher['first_name'] . ' ' . $teacher['last_name']); ?>
                        </div>
                        <div class="profile-code">
                            <?php echo htmlspecialchars($teacher['user_code']); ?>
                        </div>
                        <div class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            Joined <?php echo date('M Y', strtotime($teacher['created_at'])); ?>
                        </div>
                        <?php if ($form_class): ?>
                            <div class="mt-2">
                                <span class="badge bg-success">
                                    <i class="fas fa-star me-1"></i>Form Teacher - <?php echo htmlspecialchars($form_class); ?>
                                </span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="profile-stats">
                        <div class="stat-item">
                            <div class="stat-number"><?php echo $stats['subjects']; ?></div>
                            <div class="stat-label">Subjects</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo $stats['classes']; ?></div>
                            <div class="stat-label">Classes</div>
                        </div>
                    </div>
                </div>

                <!-- Quick Info -->
                <div class="content-card">
                    <h6 class="mb-3"><i class="fas fa-info-circle me-2"></i>Quick Information</h6>
                    <div class="info-item">
                        <span class="info-label">Employee ID:</span>
                        <span class="info-value"><?php echo htmlspecialchars($teacher['user_code']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Gender:</span>
                        <span class="info-value"><?php echo htmlspecialchars($teacher['gender']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Hire Date:</span>
                        <span class="info-value"><?php echo $teacher['hire_date'] ? date('M d, Y', strtotime($teacher['hire_date'])) : 'Not set'; ?></span>
                    </div>
                </div>
            </div>

            <!-- Profile Form -->
            <div class="col-lg-8">
                <div class="content-card">
                    <h5 class="mb-4"><i class="fas fa-edit me-2"></i>Edit Profile</h5>

                    <form method="POST" action="">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="first_name" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name"
                                           value="<?php echo htmlspecialchars($teacher['first_name']); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="last_name" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name"
                                           value="<?php echo htmlspecialchars($teacher['last_name']); ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="middle_name" class="form-label">Middle Name</label>
                                    <input type="text" class="form-control" id="middle_name" name="middle_name"
                                           value="<?php echo htmlspecialchars($teacher['middle_name']); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="<?php echo htmlspecialchars($teacher['email']); ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           value="<?php echo htmlspecialchars($teacher['phone']); ?>" maxlength="11">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="qualification" class="form-label">Qualification</label>
                                    <input type="text" class="form-control" id="qualification" name="qualification"
                                           value="<?php echo htmlspecialchars($teacher['qualification']); ?>"
                                           placeholder="e.g., B.Ed, M.Sc, etc.">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="specialization" class="form-label">Specialization</label>
                                    <input type="text" class="form-control" id="specialization" name="specialization"
                                           value="<?php echo htmlspecialchars($teacher['specialization']); ?>"
                                           placeholder="e.g., Mathematics, English, etc.">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($teacher['address']); ?></textarea>
                        </div>

                        <div class="text-end">
                            <button type="submit" name="update_profile" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Phone number validation (only digits, max 11)
        document.getElementById('phone').addEventListener('input', function() {
            // Remove any non-digit characters
            this.value = this.value.replace(/\D/g, '');

            // Limit to 11 digits
            if (this.value.length > 11) {
                this.value = this.value.slice(0, 11);
            }
        });
    </script>
</body>
</html>
