<?php
/**
 * AJAX handler for deleting fee types
 * Professional fee management system
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !hasRole('admin')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

header('Content-Type: application/json');

try {
    $conn = getDBConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $feeId = intval($_POST['fee_id']);
        
        // Get fee type name for logging
        $stmt = $conn->prepare("SELECT fee_name FROM fee_types WHERE id = ?");
        $stmt->execute([$feeId]);
        $feeType = $stmt->fetch();
        
        if (!$feeType) {
            echo json_encode(['success' => false, 'message' => 'Fee type not found']);
            exit();
        }
        
        // Check if fee type is being used in any payments
        $stmt = $conn->prepare("
            SELECT COUNT(*) as payment_count 
            FROM fee_payments fp
            JOIN fee_types ft ON fp.fee_type_id = ft.id
            WHERE ft.id = ?
        ");
        $stmt->execute([$feeId]);
        $paymentCount = $stmt->fetch()['payment_count'];
        
        if ($paymentCount > 0) {
            echo json_encode([
                'success' => false, 
                'message' => 'Cannot delete fee type that has been used in payments. You can deactivate it instead.'
            ]);
            exit();
        }
        
        // Soft delete (set is_active to 0)
        $stmt = $conn->prepare("
            UPDATE fee_types 
            SET is_active = 0, updated_at = NOW()
            WHERE id = ?
        ");
        
        $result = $stmt->execute([$feeId]);
        
        if ($result) {
            // Log the activity
            $stmt = $conn->prepare("
                INSERT INTO activity_logs (user_id, activity_type, description, created_at)
                VALUES (?, 'fee_delete', ?, NOW())
            ");
            $stmt->execute([
                $_SESSION['user_id'],
                "Deleted fee type: {$feeType['fee_name']}"
            ]);
            
            echo json_encode(['success' => true, 'message' => 'Fee type deleted successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to delete fee type']);
        }
        
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    }
    
} catch (Exception $e) {
    error_log("Delete fee type error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while processing your request']);
}
?>
