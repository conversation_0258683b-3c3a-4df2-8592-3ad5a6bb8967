<?php
/**
 * Update fee_types table to add exam fee functionality
 * Run this script to add new columns for exam fees
 */

require_once 'config/database.php';

try {
    $conn = getDBConnection();
    
    echo "🔧 Updating fee_types table...\n";
    
    // Check if columns already exist
    $stmt = $conn->query("DESCRIBE fee_types");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $columnsToAdd = [];
    
    if (!in_array('is_exam_fee', $columns)) {
        $columnsToAdd[] = "ADD COLUMN is_exam_fee BOOLEAN DEFAULT FALSE";
    }
    
    if (!in_array('target_class', $columns)) {
        $columnsToAdd[] = "ADD COLUMN target_class VARCHAR(10) NULL";
    }
    
    if (!in_array('payment_term', $columns)) {
        $columnsToAdd[] = "ADD COLUMN payment_term VARCHAR(1) NULL";
    }
    
    if (!empty($columnsToAdd)) {
        $sql = "ALTER TABLE fee_types " . implode(', ', $columnsToAdd);
        $conn->exec($sql);
        echo "✅ Added new columns to fee_types table\n";
    } else {
        echo "✅ All columns already exist\n";
    }
    
    // Clear existing fee types and add the simplified structure
    echo "🔧 Setting up simplified fee structure...\n";
    
    // Deactivate all existing fee types
    $conn->exec("UPDATE fee_types SET is_active = 0");
    
    // Insert the 3 main fees
    $mainFees = [
        ['School Fee', 'Main tuition and academic fee', 80000.00],
        ['Development Levy', 'School infrastructure and development', 15000.00],
        ['Other Fees', 'Sports, library, laboratory and other miscellaneous fees', 10000.00]
    ];
    
    $stmt = $conn->prepare("
        INSERT INTO fee_types (fee_name, fee_description, amount_js, amount_ss, is_mandatory, is_exam_fee, created_by)
        VALUES (?, ?, ?, ?, TRUE, FALSE, 1)
    ");
    
    foreach ($mainFees as $fee) {
        $stmt->execute([$fee[0], $fee[1], $fee[2], $fee[2]]);
    }
    
    // Insert exam fees
    $examFees = [
        ['Junior WAEC Fee', 'Junior Secondary School Certificate Examination fee', 25000.00, 'JSS3', '3'],
        ['Senior WAEC Fee', 'West African Senior School Certificate Examination fee', 35000.00, 'SS3', '3']
    ];
    
    $stmt = $conn->prepare("
        INSERT INTO fee_types (fee_name, fee_description, amount_js, amount_ss, is_mandatory, is_exam_fee, target_class, payment_term, created_by)
        VALUES (?, ?, ?, ?, TRUE, TRUE, ?, ?, 1)
    ");
    
    foreach ($examFees as $fee) {
        $stmt->execute([$fee[0], $fee[1], $fee[2], $fee[2], $fee[3], $fee[4]]);
    }
    
    echo "✅ Fee structure updated successfully!\n";
    echo "\n📋 Current Fee Structure:\n";
    echo "   1. School Fee: ₦80,000\n";
    echo "   2. Development Levy: ₦15,000\n";
    echo "   3. Other Fees: ₦10,000\n";
    echo "   4. Junior WAEC Fee: ₦25,000 (JSS3 - Term 3)\n";
    echo "   5. Senior WAEC Fee: ₦35,000 (SS3 - Term 3)\n";
    echo "\n💡 Total Regular Fees: ₦105,000\n";
    echo "💡 Exam fees will only show for relevant classes during the specified term\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
