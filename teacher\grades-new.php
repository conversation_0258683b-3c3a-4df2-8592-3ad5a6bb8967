<?php
/**
 * Enhanced Grades Management for Teachers
 * Intellectual Giant Secondary School Electronic Management System
 * Assessment (40) + Exam (60) = Total (100) Scoring System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a teacher
if (!isLoggedIn() || $_SESSION['user_type'] !== 'teacher') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get teacher information
$stmt = $conn->prepare("
    SELECT t.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, u.user_code
    FROM teachers t
    JOIN users u ON t.user_id = u.id
    WHERE t.user_id = ?
");
$stmt->execute([$user_id]);
$teacher = $stmt->fetch();

if (!$teacher) {
    header('Location: ../login.php');
    exit();
}

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();

$message = '';
$error = '';

// Handle grade submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_grades'])) {
    $subject_id = (int)$_POST['subject_id'];
    $class_id = (int)$_POST['class_id'];
    $session_id = 1; // Current session
    $term_id = (int)$current_academic['term'];

    try {
        $conn->beginTransaction();

        foreach ($_POST['grades'] as $student_id => $grade_data) {
            $student_id = (int)$student_id;
            $assessment_score = min(40, max(0, (float)$grade_data['assessment']));
            $exam_score = min(60, max(0, (float)$grade_data['exam']));
            $teacher_remarks = sanitizeInput($grade_data['remarks'] ?? '');

            // Insert or update grade
            $stmt = $conn->prepare("
                INSERT INTO student_grades
                (student_id, subject_id, class_id, session_id, term_id, assessment_score, exam_score_new, teacher_remarks, teacher_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                assessment_score = VALUES(assessment_score),
                exam_score_new = VALUES(exam_score_new),
                teacher_remarks = VALUES(teacher_remarks),
                teacher_id = VALUES(teacher_id),
                updated_at = CURRENT_TIMESTAMP
            ");

            $stmt->execute([
                $student_id, $subject_id, $class_id, $session_id, $term_id,
                $assessment_score, $exam_score, $teacher_remarks, $teacher['id']
            ]);
        }

        $conn->commit();
        $message = 'Grades saved successfully!';

    } catch (Exception $e) {
        $conn->rollback();
        $error = 'Failed to save grades: ' . $e->getMessage();
    }
}

// Get teacher's assigned subjects with classes
$stmt = $conn->prepare("
    SELECT
        s.id as subject_id,
        s.subject_name,
        s.subject_code,
        c.id as class_id,
        c.class_name,
        l.level_name,
        COUNT(DISTINCT st.id) as student_count
    FROM subject_assignments sa
    JOIN subjects s ON sa.subject_id = s.id
    JOIN classes c ON sa.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    LEFT JOIN students st ON c.id = st.class_id AND st.current_session_id = ?
    WHERE sa.teacher_id = ? AND sa.is_active = 1
    GROUP BY s.id, s.subject_name, s.subject_code, c.id, c.class_name, l.level_name
    ORDER BY l.id, c.class_name, s.subject_name
");
$stmt->execute([1, $teacher['id']]); // Using session_id = 1 for now
$subject_classes = $stmt->fetchAll();

// Get selected subject and class for grade entry
$selected_subject_id = $_GET['subject_id'] ?? null;
$selected_class_id = $_GET['class_id'] ?? null;
$students = [];
$selected_subject = null;
$selected_class = null;

if ($selected_subject_id && $selected_class_id) {
    // Get subject and class details
    $stmt = $conn->prepare("
        SELECT s.subject_name, s.subject_code, c.class_name, l.level_name
        FROM subjects s, classes c, levels l
        WHERE s.id = ? AND c.id = ? AND c.level_id = l.id
    ");
    $stmt->execute([$selected_subject_id, $selected_class_id]);
    $selection_info = $stmt->fetch();

    if ($selection_info) {
        $selected_subject = $selection_info;
        $selected_class = $selection_info;

        // Get students in the class with their current grades
        $stmt = $conn->prepare("
            SELECT
                st.id as student_id,
                u.first_name,
                u.last_name,
                u.middle_name,
                st.admission_number,
                sg.assessment_score,
                sg.exam_score_new as exam_score,
                sg.total_score,
                sg.grade,
                sg.remark,
                sg.teacher_remarks
            FROM students st
            JOIN users u ON st.user_id = u.id
            LEFT JOIN student_grades sg ON st.id = sg.student_id
                AND sg.subject_id = ?
                AND sg.class_id = ?
                AND sg.term_id = ?
            WHERE st.class_id = ? AND u.is_active = 1
            ORDER BY u.first_name, u.last_name
        ");
        $stmt->execute([$selected_subject_id, $selected_class_id, $current_academic['term'], $selected_class_id]);
        $students = $stmt->fetchAll();
    }
}

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Grades - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            padding: 20px;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 25px;
            backdrop-filter: blur(20px);
        }

        .subject-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .subject-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .scoring-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .grade-input {
            max-width: 80px;
            text-align: center;
        }

        .student-row {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }

        .btn-save-grades {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 10px;
            font-weight: 600;
        }

        .btn-save-grades:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            color: white;
        }

        .grade-preview {
            background: #e9ecef;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            font-weight: bold;
        }

        .grade-A { background-color: #d4edda; color: #155724; }
        .grade-B { background-color: #cce7ff; color: #004085; }
        .grade-C { background-color: #fff3cd; color: #856404; }
        .grade-D { background-color: #f8d7da; color: #721c24; }
        .grade-E { background-color: #f8d7da; color: #721c24; }
        .grade-F { background-color: #f5c6cb; color: #721c24; }

        .remarks-input {
            min-height: 60px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Enhanced Grades Management</h4>
                    <small class="text-muted">Assessment (40) + Exam (60) = Total (100) Scoring System</small>
                </div>
                <div>
                    <a href="grades.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Original
                    </a>
                    <span class="badge bg-primary me-2">
                        Session: <?php echo $current_academic['session']; ?>
                    </span>
                    <span class="badge bg-info">
                        Term: <?php echo TERMS[$current_academic['term']]; ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Scoring System Info -->
        <div class="scoring-info">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h5><i class="fas fa-calculator me-2"></i>IGSS Scoring System</h5>
                    <p class="mb-0">Assessment Component: <strong>40 marks</strong> | Exam Component: <strong>60 marks</strong> | Total: <strong>100 marks</strong></p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex justify-content-end gap-2">
                        <span class="badge bg-light text-dark">A: 70-100</span>
                        <span class="badge bg-light text-dark">B: 60-69</span>
                        <span class="badge bg-light text-dark">C: 50-59</span>
                        <span class="badge bg-light text-dark">D: 45-49</span>
                        <span class="badge bg-light text-dark">E: 40-44</span>
                        <span class="badge bg-light text-dark">F: 0-39</span>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!$selected_subject_id || !$selected_class_id): ?>
        <!-- Subject Selection -->
        <div class="content-card">
            <h5 class="mb-4"><i class="fas fa-book me-2"></i>Select Subject & Class for Grading</h5>

            <?php if (count($subject_classes) > 0): ?>
                <div class="row">
                    <?php foreach ($subject_classes as $sc): ?>
                        <div class="col-lg-4 col-md-6">
                            <div class="subject-card" onclick="selectSubjectClass(<?php echo $sc['subject_id']; ?>, <?php echo $sc['class_id']; ?>)">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div>
                                        <h6 class="fw-bold text-primary"><?php echo htmlspecialchars($sc['subject_name']); ?></h6>
                                        <small class="text-muted"><?php echo htmlspecialchars($sc['subject_code']); ?></small>
                                    </div>
                                    <span class="badge bg-primary"><?php echo htmlspecialchars($sc['subject_code']); ?></span>
                                </div>

                                <div class="mb-3">
                                    <i class="fas fa-users me-1 text-muted"></i>
                                    <span class="text-muted"><?php echo htmlspecialchars($sc['class_name']); ?> - <?php echo htmlspecialchars($sc['level_name']); ?></span>
                                </div>

                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge bg-success">
                                        <i class="fas fa-user-graduate me-1"></i>
                                        <?php echo $sc['student_count']; ?> Students
                                    </span>
                                    <i class="fas fa-arrow-right text-primary"></i>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-book fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Subjects Assigned</h5>
                    <p class="text-muted">You don't have any subjects assigned for grading.</p>
                </div>
            <?php endif; ?>
        </div>

        <?php else: ?>
        <!-- Grade Entry Form -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h5><i class="fas fa-edit me-2"></i>Enter Grades</h5>
                    <p class="mb-0 text-muted">
                        <strong><?php echo htmlspecialchars($selected_subject['subject_name']); ?></strong> -
                        <?php echo htmlspecialchars($selected_class['class_name']); ?> (<?php echo htmlspecialchars($selected_class['level_name']); ?>)
                    </p>
                </div>
                <a href="grades-new.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Selection
                </a>
            </div>

            <?php if (count($students) > 0): ?>
            <form method="POST" action="" id="gradesForm">
                <input type="hidden" name="subject_id" value="<?php echo $selected_subject_id; ?>">
                <input type="hidden" name="class_id" value="<?php echo $selected_class_id; ?>">

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Student</th>
                                <th>Admission No.</th>
                                <th>Assessment (40)</th>
                                <th>Exam (60)</th>
                                <th>Total (100)</th>
                                <th>Grade</th>
                                <th>Remarks</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($students as $student): ?>
                            <tr class="student-row">
                                <td>
                                    <strong><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></strong>
                                    <?php if ($student['middle_name']): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($student['middle_name']); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($student['admission_number']); ?></td>
                                <td>
                                    <input type="number"
                                           class="form-control grade-input assessment-input"
                                           name="grades[<?php echo $student['student_id']; ?>][assessment]"
                                           value="<?php echo $student['assessment_score'] ?? ''; ?>"
                                           min="0" max="40" step="0.01"
                                           data-student="<?php echo $student['student_id']; ?>"
                                           placeholder="0-40">
                                </td>
                                <td>
                                    <input type="number"
                                           class="form-control grade-input exam-input"
                                           name="grades[<?php echo $student['student_id']; ?>][exam]"
                                           value="<?php echo $student['exam_score'] ?? ''; ?>"
                                           min="0" max="60" step="0.01"
                                           data-student="<?php echo $student['student_id']; ?>"
                                           placeholder="0-60">
                                </td>
                                <td>
                                    <div class="grade-preview" id="total-<?php echo $student['student_id']; ?>">
                                        <?php echo $student['total_score'] ?? '0'; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="grade-preview" id="grade-<?php echo $student['student_id']; ?>">
                                        <?php echo $student['grade'] ?? 'F'; ?>
                                    </div>
                                </td>
                                <td>
                                    <textarea class="form-control remarks-input"
                                              name="grades[<?php echo $student['student_id']; ?>][remarks]"
                                              placeholder="Teacher's remarks..."><?php echo htmlspecialchars($student['teacher_remarks'] ?? ''); ?></textarea>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <div class="text-center mt-4">
                    <button type="submit" name="submit_grades" class="btn btn-save-grades btn-lg">
                        <i class="fas fa-save me-2"></i>Save All Grades
                    </button>
                </div>
            </form>
            <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Students Found</h5>
                <p class="text-muted">There are no students in this class.</p>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function selectSubjectClass(subjectId, classId) {
            window.location.href = `grades-new.php?subject_id=${subjectId}&class_id=${classId}`;
        }

        // Auto-calculate totals and grades
        document.addEventListener('DOMContentLoaded', function() {
            const assessmentInputs = document.querySelectorAll('.assessment-input');
            const examInputs = document.querySelectorAll('.exam-input');

            function calculateGrade(total) {
                if (total >= 70) return { grade: 'A', class: 'grade-A', remark: 'Excellent' };
                if (total >= 60) return { grade: 'B', class: 'grade-B', remark: 'Very Good' };
                if (total >= 50) return { grade: 'C', class: 'grade-C', remark: 'Good' };
                if (total >= 45) return { grade: 'D', class: 'grade-D', remark: 'Pass' };
                if (total >= 40) return { grade: 'E', class: 'grade-E', remark: 'Fair' };
                return { grade: 'F', class: 'grade-F', remark: 'Fail' };
            }

            function updateGrade(studentId) {
                const assessmentInput = document.querySelector(`input[name="grades[${studentId}][assessment]"]`);
                const examInput = document.querySelector(`input[name="grades[${studentId}][exam]"]`);
                const totalDiv = document.getElementById(`total-${studentId}`);
                const gradeDiv = document.getElementById(`grade-${studentId}`);

                const assessment = parseFloat(assessmentInput.value) || 0;
                const exam = parseFloat(examInput.value) || 0;
                const total = Math.min(100, assessment + exam);

                const gradeInfo = calculateGrade(total);

                totalDiv.textContent = total.toFixed(1);
                gradeDiv.textContent = gradeInfo.grade;
                gradeDiv.className = `grade-preview ${gradeInfo.class}`;
            }

            // Add event listeners
            [...assessmentInputs, ...examInputs].forEach(input => {
                input.addEventListener('input', function() {
                    const studentId = this.dataset.student;
                    updateGrade(studentId);
                });

                // Initial calculation
                const studentId = input.dataset.student;
                updateGrade(studentId);
            });

            // Form validation
            document.getElementById('gradesForm').addEventListener('submit', function(e) {
                let hasErrors = false;
                const inputs = this.querySelectorAll('input[type="number"]');

                inputs.forEach(input => {
                    const value = parseFloat(input.value);
                    const max = parseFloat(input.max);

                    if (value > max) {
                        input.classList.add('is-invalid');
                        hasErrors = true;
                    } else {
                        input.classList.remove('is-invalid');
                    }
                });

                if (hasErrors) {
                    e.preventDefault();
                    alert('Please check the scores. Some values exceed the maximum allowed.');
                }
            });
        });
    </script>
</body>
</html>