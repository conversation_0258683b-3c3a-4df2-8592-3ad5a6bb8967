<?php
// Simple database connection test
echo "Testing database connection...\n";

try {
    $pdo = new PDO('mysql:host=localhost', 'root', '');
    echo "✅ MySQL connection successful\n";
    
    // Create database
    $pdo->exec('CREATE DATABASE IF NOT EXISTS igss_management');
    echo "✅ Database created/exists\n";
    
    // Connect to the database
    $pdo = new PDO('mysql:host=localhost;dbname=igss_management', 'root', '');
    echo "✅ Connected to igss_management database\n";
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Users table exists\n";
    } else {
        echo "⚠️  Users table does not exist - need to run setup\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
