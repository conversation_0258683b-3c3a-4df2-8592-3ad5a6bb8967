# 🚀 IGSS Management System - Deployment Guide

## Quick Deployment Steps

### 1. **Setup Database**
```
1. Copy all files to your web server (e.g., C:\xampp\htdocs\IGSS)
2. Start MySQL/MariaDB service
3. Open browser and go to: http://localhost/IGSS/importdb.php
4. Click "Import Database" button
```

### 2. **Setup Admin Account**
```
1. After successful import, click "Set Up Admin Password"
2. Fill in your admin details:
   - First Name: Your first name
   - Last Name: Your last name  
   - Email: Your admin email
   - Password: Choose a strong password
3. Click "Set Up Admin Account"
```

### 3. **Login**
```
1. Go to: http://localhost/IGSS/login.php
2. Select "Administrator" role
3. Login with:
   - User Code: ADMIN001
   - Password: [your chosen password]
```

## 🔧 Troubleshooting

### Problem: "Initialization Failed" Error

**Solution 1: Use Verification Tool**
```
1. Go to: http://localhost/IGSS/verify-setup.php
2. Check the status and click "Create/Reset Admin User"
3. Try admin setup again
```

**Solution 2: Direct Password Reset**
```
1. Go to: http://localhost/IGSS/direct-reset.php
2. Enter:
   - Admin Code: ADMIN001
   - New Password: [your password]
   - Confirm Password: [same password]
3. Click "Reset Password"
```

**Solution 3: Manual Database Check**
```
1. Open phpMyAdmin or MySQL command line
2. Check if 'igss_management' database exists
3. Check if 'users' table exists
4. Verify admin user exists:
   SELECT * FROM users WHERE user_code = 'ADMIN001';
```

### Problem: Database Connection Error

**Check These:**
```
1. MySQL/MariaDB service is running
2. Database credentials in config/database.php:
   - DB_HOST = 'localhost'
   - DB_NAME = 'igss_management'  
   - DB_USER = 'root'
   - DB_PASS = '' (or your MySQL password)
```

### Problem: Admin Setup Page Not Loading

**Solutions:**
```
1. Clear browser cache and cookies
2. Check if session is working:
   - Go to verify-setup.php
   - Click "Reset Setup Session"
3. Try direct-reset.php instead
```

## 📋 Verification Checklist

After deployment, verify these work:

- [ ] Database imported successfully
- [ ] Admin user created/updated
- [ ] Can access login page
- [ ] Can login with ADMIN001
- [ ] Admin dashboard loads
- [ ] Can access admin features

## 🛠️ Useful Tools

### Diagnostic Tools:
- **verify-setup.php** - Complete system diagnosis
- **test-admin-setup.php** - Admin setup testing
- **debug-admin.php** - Admin user debugging

### Setup Tools:
- **importdb.php** - Database import
- **admin-setup.php** - Admin account setup  
- **direct-reset.php** - Direct password reset
- **setup.php** - Alternative setup method

## 🔒 Security Notes

**After Successful Deployment:**
1. Delete setup files for security:
   - importdb.php
   - admin-setup.php
   - verify-setup.php
   - test-admin-setup.php
   - debug-admin.php
   - direct-reset.php

2. Change default database credentials
3. Enable HTTPS in production
4. Set proper file permissions

## 📞 Support

If you still have issues:
1. Check the browser console for JavaScript errors
2. Check web server error logs
3. Verify PHP version (7.4+ recommended)
4. Ensure all required PHP extensions are installed

## 🎯 Default Credentials

**After Setup:**
- **User Code:** ADMIN001
- **Password:** [your chosen password]
- **Role:** Administrator

**Database:**
- **Host:** localhost
- **Database:** igss_management
- **Username:** root
- **Password:** [empty or your MySQL password]

---

**✅ That's it! Your IGSS Management System should now be ready to use.**
