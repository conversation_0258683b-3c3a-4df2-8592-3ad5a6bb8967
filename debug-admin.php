<?php
/**
 * Debug Admin User - Check admin user status
 * This file helps debug admin login issues
 * DELETE AFTER DEBUGGING FOR SECURITY
 */

require_once 'config/config.php';

try {
    $conn = getDBConnection();
    
    echo "<h2>Admin User Debug Information</h2>";
    
    // Check if admin user exists
    $stmt = $conn->prepare("
        SELECT id, user_code, first_name, last_name, email, user_type, 
               is_approved, is_active, password_hash, created_at
        FROM users 
        WHERE user_code = 'ADMIN001' OR user_type = 'admin'
    ");
    $stmt->execute();
    $admin_users = $stmt->fetchAll();
    
    if (empty($admin_users)) {
        echo "<p style='color: red;'>❌ No admin users found in database!</p>";
        echo "<p>You may need to run the database setup first.</p>";
        echo "<a href='setup.php'>Go to Database Setup</a>";
    } else {
        echo "<p style='color: green;'>✅ Found " . count($admin_users) . " admin user(s)</p>";
        
        foreach ($admin_users as $user) {
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
            echo "<h3>User ID: " . $user['id'] . "</h3>";
            echo "<p><strong>User Code:</strong> " . $user['user_code'] . "</p>";
            echo "<p><strong>Name:</strong> " . $user['first_name'] . " " . $user['last_name'] . "</p>";
            echo "<p><strong>Email:</strong> " . $user['email'] . "</p>";
            echo "<p><strong>User Type:</strong> " . $user['user_type'] . "</p>";
            echo "<p><strong>Is Approved:</strong> " . ($user['is_approved'] ? 'YES' : 'NO') . "</p>";
            echo "<p><strong>Is Active:</strong> " . ($user['is_active'] ? 'YES' : 'NO') . "</p>";
            echo "<p><strong>Password Hash:</strong> " . substr($user['password_hash'], 0, 20) . "...</p>";
            echo "<p><strong>Created:</strong> " . $user['created_at'] . "</p>";
            
            // Test password verification
            $test_password = 'admin@1234'; // The password you set
            $password_valid = password_verify($test_password, $user['password_hash']);
            echo "<p><strong>Password 'admin@1234' valid:</strong> " . ($password_valid ? 'YES' : 'NO') . "</p>";
            
            echo "</div>";
        }
    }
    
    // Test authentication function
    echo "<h3>Testing Authentication Function</h3>";
    
    if (!empty($admin_users)) {
        $test_result = authenticateUserWithRole('ADMIN001', 'admin@1234', 'admin');
        echo "<p><strong>authenticateUserWithRole('ADMIN001', 'admin@1234', 'admin'):</strong> " . ($test_result ? 'SUCCESS' : 'FAILED') . "</p>";
        
        if (!$test_result) {
            echo "<p style='color: red;'>Authentication failed. Possible issues:</p>";
            echo "<ul>";
            echo "<li>Password doesn't match</li>";
            echo "<li>User is not approved (is_approved = 0)</li>";
            echo "<li>User is not active (is_active = 0)</li>";
            echo "<li>User type doesn't match</li>";
            echo "</ul>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
</style>

<p><a href="login.php">← Back to Login</a></p>
