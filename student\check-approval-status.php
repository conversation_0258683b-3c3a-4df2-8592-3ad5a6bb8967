<?php
/**
 * Check Student Approval Status API
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in and is a student
if (!isLoggedIn() || $_SESSION['user_type'] !== 'student') {
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

try {
    $conn = getDBConnection();
    $user_id = $_SESSION['user_id'];
    
    // Check current approval status
    $stmt = $conn->prepare("SELECT is_approved FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    if ($user) {
        // Update session if approved
        if ($user['is_approved']) {
            $_SESSION['is_approved'] = true;
        }
        
        echo json_encode([
            'approved' => (bool)$user['is_approved'],
            'timestamp' => time()
        ]);
    } else {
        echo json_encode(['error' => 'User not found']);
    }
    
} catch (Exception $e) {
    echo json_encode(['error' => 'Database error']);
}
?>
