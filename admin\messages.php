<?php
/**
 * Admin Messages & Communication
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/messaging-functions.php';

// Check if user is logged in and is an admin
if (!isLoggedIn() || $_SESSION['user_type'] !== 'admin') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

$message = '';
$error = '';

// Handle message actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['mark_read'])) {
        $message_id = (int)$_POST['message_id'];
        markMessageAsRead($message_id, $user_id);
        $message = 'Message marked as read.';
    } elseif (isset($_POST['send_reply'])) {
        $original_message_id = (int)$_POST['original_message_id'];
        $reply_content = sanitizeInput($_POST['reply_content']);
        
        if (!empty($reply_content)) {
            $result = replyToMessage($original_message_id, $user_id, 'admin', $reply_content);
            if ($result['success']) {
                $message = 'Reply sent successfully!';
            } else {
                $error = 'Failed to send reply: ' . $result['error'];
            }
        } else {
            $error = 'Please enter a reply message.';
        }
    }
}

// Get admin's messages
$admin_messages = getUserMessages($user_id, 'admin', 100, 0);

// Get unread count
$unread_count = getUnreadMessageCount($user_id, 'admin');

// Get message statistics
$stmt = $conn->prepare("
    SELECT 
        message_type,
        COUNT(*) as count,
        SUM(CASE WHEN mr.is_read = 0 THEN 1 ELSE 0 END) as unread_count
    FROM messages m
    JOIN message_recipients mr ON m.id = mr.message_id
    WHERE mr.recipient_id = ? AND mr.recipient_type = 'admin'
    GROUP BY message_type
    ORDER BY count DESC
");
$stmt->execute([$user_id]);
$message_stats = $stmt->fetchAll();

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Messages - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --light-bg: #ecf0f1;
            --dark-text: #2c3e50;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            min-height: calc(100vh - 40px);
        }

        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            margin-bottom: 0;
        }

        .content-section {
            padding: 30px;
        }

        .message-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .message-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .message-item {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            background: white;
        }

        .message-item:hover {
            border-color: var(--secondary-color);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.15);
        }

        .message-item.unread {
            border-left: 4px solid var(--accent-color);
            background: #fff8f8;
        }

        .message-item.urgent {
            border-left: 4px solid var(--accent-color);
            background: #fff5f5;
        }

        .message-item.high {
            border-left: 4px solid var(--warning-color);
            background: #fffbf0;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .message-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-sent { background: #e3f2fd; color: #1976d2; }
        .status-read { background: #e8f5e8; color: #2e7d32; }
        .status-replied { background: #f3e5f5; color: #7b1fa2; }

        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color), #2980b9);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .reply-form {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
            border: 1px solid #e9ecef;
        }

        .breadcrumb {
            background: transparent;
            padding: 0;
            margin-bottom: 20px;
        }

        .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: white;
        }

        .filter-tabs {
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 20px;
        }

        .filter-tabs .nav-link {
            border: none;
            color: #6c757d;
            font-weight: 500;
        }

        .filter-tabs .nav-link.active {
            color: var(--secondary-color);
            border-bottom: 2px solid var(--secondary-color);
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header-section">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php"><i class="fas fa-home"></i> Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page"><i class="fas fa-envelope"></i> Messages</li>
                </ol>
            </nav>
            
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-0">
                        <i class="fas fa-envelope me-3"></i>
                        Administrative Messages
                    </h2>
                    <p class="mb-0 mt-2 opacity-75">Manage parent and teacher communications</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="stats-card d-inline-block">
                        <div class="stats-number"><?php echo $unread_count; ?></div>
                        <div>Unread Messages</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="content-section">
            <!-- Success/Error Messages -->
            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Messages List -->
                <div class="col-lg-8">
                    <!-- Filter Tabs -->
                    <ul class="nav nav-tabs filter-tabs" id="messageFilter" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
                                All Messages (<?php echo count($admin_messages); ?>)
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="unread-tab" data-bs-toggle="tab" data-bs-target="#unread" type="button" role="tab">
                                Unread (<?php echo $unread_count; ?>)
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="urgent-tab" data-bs-toggle="tab" data-bs-target="#urgent" type="button" role="tab">
                                Urgent
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" id="messageFilterContent">
                        <div class="tab-pane fade show active" id="all" role="tabpanel">
                            <div class="message-card">
                                <h4 class="mb-4">
                                    <i class="fas fa-inbox me-2"></i>
                                    All Messages
                                </h4>
                                
                                <?php if (!empty($admin_messages)): ?>
                                    <?php foreach ($admin_messages as $msg): ?>
                                        <div class="message-item <?php echo !$msg['is_read'] ? 'unread' : ''; ?> <?php echo $msg['priority']; ?>" 
                                             id="message-<?php echo $msg['id']; ?>">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <h6 class="mb-0"><?php echo htmlspecialchars($msg['subject']); ?></h6>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-info me-2"><?php echo ucfirst($msg['message_type']); ?></span>
                                                    <?php if ($msg['priority'] === 'urgent'): ?>
                                                        <span class="badge bg-danger me-2">Urgent</span>
                                                    <?php elseif ($msg['priority'] === 'high'): ?>
                                                        <span class="badge bg-warning me-2">High</span>
                                                    <?php endif; ?>
                                                    <span class="message-status status-<?php echo $msg['is_read'] ? 'read' : 'sent'; ?>">
                                                        <?php echo $msg['is_read'] ? 'Read' : 'New'; ?>
                                                    </span>
                                                </div>
                                            </div>
                                            
                                            <p class="text-muted mb-2">
                                                <i class="fas fa-user me-1"></i>
                                                From: <?php echo htmlspecialchars($msg['sender_first_name'] . ' ' . $msg['sender_last_name']); ?>
                                                (<?php echo ucfirst($msg['sender_user_type']); ?>)
                                                <?php if ($msg['ward_first_name']): ?>
                                                    <br><i class="fas fa-child me-1"></i>
                                                    Regarding: <?php echo htmlspecialchars($msg['ward_first_name'] . ' ' . $msg['ward_last_name'] . ' (' . $msg['admission_number'] . ')'); ?>
                                                <?php endif; ?>
                                            </p>
                                            
                                            <p class="mb-3"><?php echo nl2br(htmlspecialchars($msg['message_content'])); ?></p>
                                            
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <i class="fas fa-clock me-1"></i>
                                                    <?php echo date('M j, Y g:i A', strtotime($msg['created_at'])); ?>
                                                </small>
                                                <div>
                                                    <?php if (!$msg['is_read']): ?>
                                                        <form method="POST" class="d-inline">
                                                            <input type="hidden" name="message_id" value="<?php echo $msg['id']; ?>">
                                                            <button type="submit" name="mark_read" class="btn btn-sm btn-outline-primary me-2">
                                                                <i class="fas fa-check me-1"></i>Mark as Read
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                    <button class="btn btn-sm btn-primary" onclick="toggleReplyForm(<?php echo $msg['id']; ?>)">
                                                        <i class="fas fa-reply me-1"></i>Reply
                                                    </button>
                                                </div>
                                            </div>
                                            
                                            <!-- Reply Form -->
                                            <div class="reply-form" id="reply-form-<?php echo $msg['id']; ?>" style="display: none;">
                                                <form method="POST">
                                                    <input type="hidden" name="original_message_id" value="<?php echo $msg['id']; ?>">
                                                    <div class="mb-3">
                                                        <label class="form-label">Your Reply:</label>
                                                        <textarea class="form-control" name="reply_content" rows="4" 
                                                                  placeholder="Type your reply here..." required></textarea>
                                                    </div>
                                                    <div class="d-flex justify-content-end">
                                                        <button type="button" class="btn btn-secondary me-2" 
                                                                onclick="toggleReplyForm(<?php echo $msg['id']; ?>)">Cancel</button>
                                                        <button type="submit" name="send_reply" class="btn btn-primary">
                                                            <i class="fas fa-paper-plane me-1"></i>Send Reply
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="text-center py-5">
                                        <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                                        <h5 class="text-muted">No messages yet</h5>
                                        <p class="text-muted">Parent and teacher messages will appear here.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Message Statistics -->
                    <div class="message-card">
                        <h5 class="mb-3">
                            <i class="fas fa-chart-bar me-2"></i>
                            Message Statistics
                        </h5>
                        <?php if (!empty($message_stats)): ?>
                            <?php foreach ($message_stats as $stat): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                    <span><?php echo ucwords(str_replace('_', ' ', $stat['message_type'])); ?></span>
                                    <div>
                                        <span class="badge bg-primary"><?php echo $stat['count']; ?></span>
                                        <?php if ($stat['unread_count'] > 0): ?>
                                            <span class="badge bg-danger"><?php echo $stat['unread_count']; ?> unread</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-muted">No message statistics available.</p>
                        <?php endif; ?>
                    </div>

                    <!-- Quick Actions -->
                    <div class="message-card">
                        <h5 class="mb-3">
                            <i class="fas fa-bolt me-2"></i>
                            Quick Actions
                        </h5>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="markAllAsRead()">
                                <i class="fas fa-check-double me-2"></i>Mark All as Read
                            </button>
                            <button class="btn btn-outline-info" onclick="filterByType('urgent')">
                                <i class="fas fa-exclamation-triangle me-2"></i>Show Urgent Only
                            </button>
                            <button class="btn btn-outline-success" onclick="filterByType('fee_inquiry')">
                                <i class="fas fa-credit-card me-2"></i>Fee Inquiries
                            </button>
                        </div>
                    </div>

                    <!-- Communication Guidelines -->
                    <div class="message-card">
                        <h5 class="mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            Admin Guidelines
                        </h5>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Prioritize urgent and emergency messages</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Escalate complex issues to appropriate staff</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Maintain professional communication standards</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Document important decisions and actions</small>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function toggleReplyForm(messageId) {
            const replyForm = document.getElementById('reply-form-' + messageId);
            if (replyForm.style.display === 'none') {
                replyForm.style.display = 'block';
                replyForm.querySelector('textarea').focus();
            } else {
                replyForm.style.display = 'none';
            }
        }

        function markAllAsRead() {
            if (confirm('Mark all messages as read?')) {
                // Implementation would go here
                alert('Feature coming soon!');
            }
        }

        function filterByType(type) {
            // Implementation would go here
            alert('Filter by ' + type + ' - Feature coming soon!');
        }
    </script>
</body>
</html>
