<?php
/**
 * Student Grades View for Parents
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a parent
if (!isLoggedIn() || $_SESSION['user_type'] !== 'parent') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];
$student_id = $_GET['id'] ?? 0;

// Get parent information
$stmt = $conn->prepare("
    SELECT p.*, u.first_name as parent_first_name, u.last_name as parent_last_name
    FROM parents p
    JOIN users u ON p.user_id = u.id
    WHERE p.user_id = ?
");
$stmt->execute([$user_id]);
$parent = $stmt->fetch();

if (!$parent) {
    header('Location: ../login.php');
    exit();
}

// Verify parent has access to this student
$stmt = $conn->prepare("
    SELECT COUNT(*) FROM parent_student_relationships 
    WHERE parent_id = ? AND student_id = ?
");
$stmt->execute([$parent['id'], $student_id]);
$has_access = $stmt->fetchColumn() > 0;

if (!$has_access) {
    header('Location: index.php');
    exit();
}

// Get student information
$stmt = $conn->prepare("
    SELECT s.*, u.first_name, u.last_name, u.middle_name, u.user_code,
           c.class_name, l.level_name
    FROM students s
    JOIN users u ON s.user_id = u.id
    JOIN classes c ON s.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    WHERE s.id = ?
");
$stmt->execute([$student_id]);
$student = $stmt->fetch();

if (!$student) {
    header('Location: index.php');
    exit();
}

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();

// Get student grades for current term
$stmt = $conn->prepare("
    SELECT sg.*, sub.subject_name, sub.subject_code,
           DATE_FORMAT(sg.created_at, '%M %d, %Y') as grade_date
    FROM student_grades sg
    JOIN subjects sub ON sg.subject_id = sub.id
    WHERE sg.student_id = ? AND sg.term_id = ?
    ORDER BY sub.subject_name
");
$stmt->execute([$student['id'], $current_academic['current_term_id'] ?? 1]);
$grades = $stmt->fetchAll();

// Calculate statistics
$total_score = 0;
$grade_count = 0;
$grade_distribution = ['A' => 0, 'B' => 0, 'C' => 0, 'D' => 0, 'F' => 0];

foreach ($grades as $grade) {
    $total_score += $grade['total_score'];
    $grade_count++;
    $grade_distribution[$grade['grade']]++;
}

$average_score = $grade_count > 0 ? round($total_score / $grade_count, 1) : 0;

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?> - Grades</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 50%, #ff4500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container-custom {
            padding: 20px;
        }
        
        .grades-header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .grades-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .grade-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .grade-item:hover {
            border-color: #ff8c00;
            box-shadow: 0 2px 8px rgba(255, 140, 0, 0.2);
        }
        
        .grade-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            color: white;
            font-size: 1.1rem;
        }
        
        .grade-A { background: #28a745; }
        .grade-B { background: #17a2b8; }
        .grade-C { background: #ffc107; color: #333; }
        .grade-D { background: #fd7e14; }
        .grade-F { background: #dc3545; }
        
        .stat-card {
            text-align: center;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 15px;
            color: white;
        }
        
        .average-stat { background: linear-gradient(135deg, #28a745, #20c997); }
        .subjects-stat { background: linear-gradient(135deg, #17a2b8, #138496); }
        .performance-stat { background: linear-gradient(135deg, #ffc107, #e0a800); }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .student-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #ff8c00;
            font-size: 1.1rem;
        }
        
        .score-breakdown {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
        }
        
        .score-item {
            text-align: center;
            flex: 1;
        }
        
        .score-label {
            font-size: 0.8rem;
            color: #6c757d;
            margin-bottom: 2px;
        }
        
        .score-value {
            font-weight: bold;
            color: #495057;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="student-details.php?id=<?php echo $student['id']; ?>">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Student Details
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($parent['parent_first_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="index.php"><i class="fas fa-home me-2"></i>Dashboard</a></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-custom">
        <div class="container">
            <!-- Student Header -->
            <div class="grades-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-2">
                            Academic Performance: <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                        </h2>
                        <p class="text-muted mb-1">
                            <i class="fas fa-graduation-cap me-2"></i>
                            <?php echo htmlspecialchars($student['class_name'] . ' - ' . $student['level_name']); ?>
                        </p>
                        <p class="text-muted mb-0">
                            <span class="student-code"><?php echo htmlspecialchars($student['user_code']); ?></span>
                        </p>
                    </div>
                    <div class="col-md-4 text-center">
                        <h6 class="mb-2">Current Term</h6>
                        <div class="badge bg-primary fs-6 px-3 py-2">
                            Term <?php echo $current_academic['current_term_id'] ?? 1; ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Performance Statistics -->
                <div class="col-md-4">
                    <div class="stat-card average-stat">
                        <div class="stat-number"><?php echo $average_score; ?>%</div>
                        <h6>Overall Average</h6>
                        <small>Based on <?php echo $grade_count; ?> subjects</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card subjects-stat">
                        <div class="stat-number"><?php echo $grade_count; ?></div>
                        <h6>Subjects Assessed</h6>
                        <small>Current term results</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card performance-stat">
                        <div class="stat-number"><?php echo $grade_distribution['A'] + $grade_distribution['B']; ?></div>
                        <h6>A & B Grades</h6>
                        <small>Excellent performance</small>
                    </div>
                </div>
            </div>

            <!-- Grades List -->
            <div class="grades-card">
                <h5 class="mb-4">
                    <i class="fas fa-chart-line me-2"></i>
                    Subject Grades
                </h5>
                
                <?php if (!empty($grades)): ?>
                    <?php foreach ($grades as $grade): ?>
                        <div class="grade-item">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($grade['subject_name']); ?></h6>
                                    <small class="text-muted"><?php echo htmlspecialchars($grade['subject_code']); ?></small>
                                </div>
                                <div class="col-md-4">
                                    <div class="score-breakdown">
                                        <div class="score-item">
                                            <div class="score-label">1st Test</div>
                                            <div class="score-value"><?php echo $grade['first_test'] ?? '-'; ?></div>
                                        </div>
                                        <div class="score-item">
                                            <div class="score-label">2nd Test</div>
                                            <div class="score-value"><?php echo $grade['second_test'] ?? '-'; ?></div>
                                        </div>
                                        <div class="score-item">
                                            <div class="score-label">Exam</div>
                                            <div class="score-value"><?php echo $grade['exam_score'] ?? '-'; ?></div>
                                        </div>
                                        <div class="score-item">
                                            <div class="score-label">Total</div>
                                            <div class="score-value"><strong><?php echo $grade['total_score']; ?>%</strong></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <span class="grade-badge grade-<?php echo $grade['grade']; ?>">
                                        <?php echo $grade['grade']; ?>
                                    </span>
                                    <br>
                                    <small class="text-muted"><?php echo $grade['grade_date']; ?></small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No grades available yet</h6>
                        <p class="text-muted">Grades will appear here once teachers have entered assessment results</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Grade Distribution -->
            <?php if (!empty($grades)): ?>
            <div class="grades-card">
                <h5 class="mb-4">
                    <i class="fas fa-chart-pie me-2"></i>
                    Grade Distribution
                </h5>
                
                <div class="row">
                    <?php foreach ($grade_distribution as $grade_letter => $count): ?>
                        <?php if ($count > 0): ?>
                            <div class="col-md-2 col-6 mb-3">
                                <div class="text-center">
                                    <div class="grade-badge grade-<?php echo $grade_letter; ?> d-inline-block mb-2">
                                        <?php echo $grade_letter; ?>
                                    </div>
                                    <div class="h4 mb-0"><?php echo $count; ?></div>
                                    <small class="text-muted">subject<?php echo $count > 1 ? 's' : ''; ?></small>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="grades-card">
                <h5 class="mb-4">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
                
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="student-details.php?id=<?php echo $student['id']; ?>" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user me-2"></i>Student Profile
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="student-attendance.php?id=<?php echo $student['id']; ?>" class="btn btn-outline-info w-100">
                            <i class="fas fa-calendar-check me-2"></i>Attendance
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="pay-fees.php?student_id=<?php echo $student['id']; ?>" class="btn btn-warning w-100">
                            <i class="fas fa-credit-card me-2"></i>Pay Fees
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="index.php" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-home me-2"></i>Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
