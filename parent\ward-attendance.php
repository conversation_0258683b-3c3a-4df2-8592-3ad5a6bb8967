<?php
/**
 * Ward Attendance History Page for Parents
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a parent
if (!isLoggedIn() || $_SESSION['user_type'] !== 'parent') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get parent information including approval status
$stmt = $conn->prepare("
    SELECT p.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, u.user_code, u.is_approved
    FROM parents p
    JOIN users u ON p.user_id = u.id
    WHERE p.user_id = ?
");
$stmt->execute([$user_id]);
$parent = $stmt->fetch();

if (!$parent) {
    header('Location: ../login.php');
    exit();
}

// Check if parent account is approved
if (!$parent['is_approved']) {
    header('Location: pending-approval.php');
    exit();
}

// Get student ID from URL
$student_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$student_id) {
    header('Location: index.php');
    exit();
}

// Verify parent has access to this student
$stmt = $conn->prepare("
    SELECT COUNT(*) FROM parent_student_relationships 
    WHERE parent_id = ? AND student_id = ?
");
$stmt->execute([$parent['id'], $student_id]);
$has_access = $stmt->fetchColumn();

if (!$has_access) {
    header('Location: index.php');
    exit();
}

// Get student information
$stmt = $conn->prepare("
    SELECT 
        s.*,
        u.first_name,
        u.last_name,
        u.middle_name,
        c.class_name,
        l.level_name,
        psr.relationship_type
    FROM students s
    JOIN users u ON s.user_id = u.id
    JOIN classes c ON s.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    JOIN parent_student_relationships psr ON s.id = psr.student_id
    WHERE s.id = ? AND psr.parent_id = ?
");
$stmt->execute([$student_id, $parent['id']]);
$student = $stmt->fetch();

if (!$student) {
    header('Location: index.php');
    exit();
}

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();

// Get date range for filtering (default to current month)
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-t');

// Get attendance records for the date range
$stmt = $conn->prepare("
    SELECT 
        a.*,
        DATE_FORMAT(a.attendance_date, '%Y-%m-%d') as formatted_date,
        DATE_FORMAT(a.attendance_date, '%W, %M %d, %Y') as full_date,
        DAYNAME(a.attendance_date) as day_name
    FROM attendance a
    WHERE a.student_id = ? 
    AND a.attendance_date BETWEEN ? AND ?
    ORDER BY a.attendance_date DESC
");
$stmt->execute([$student_id, $start_date, $end_date]);
$attendance_records = $stmt->fetchAll();

// Calculate attendance statistics
$total_days = count($attendance_records);
$present_days = 0;
$absent_days = 0;
$late_days = 0;
$excused_days = 0;

foreach ($attendance_records as $record) {
    switch ($record['status']) {
        case 'Present':
            $present_days++;
            break;
        case 'Absent':
            $absent_days++;
            break;
        case 'Late':
            $late_days++;
            break;
        case 'Excused':
            $excused_days++;
            break;
    }
}

$attendance_percentage = $total_days > 0 ? round(($present_days / $total_days) * 100, 1) : 0;

// Get monthly attendance summary for the current academic year
$stmt = $conn->prepare("
    SELECT 
        DATE_FORMAT(attendance_date, '%Y-%m') as month,
        DATE_FORMAT(attendance_date, '%M %Y') as month_name,
        COUNT(*) as total_days,
        SUM(CASE WHEN status = 'Present' THEN 1 ELSE 0 END) as present_days,
        SUM(CASE WHEN status = 'Absent' THEN 1 ELSE 0 END) as absent_days,
        SUM(CASE WHEN status = 'Late' THEN 1 ELSE 0 END) as late_days
    FROM attendance 
    WHERE student_id = ? 
    AND attendance_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(attendance_date, '%Y-%m')
    ORDER BY month DESC
    LIMIT 12
");
$stmt->execute([$student_id]);
$monthly_summary = $stmt->fetchAll();

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance History - <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?> - <?php echo $school_info['name']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 50%, #ff4500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }
        
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .main-container {
            padding: 20px;
            position: relative;
            z-index: 1;
        }
        
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff8c00, #ff6b35);
        }
        
        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff8c00, #ff6b35);
        }
        
        .btn-orange {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            border: none;
            color: white;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 140, 0, 0.3);
        }
        
        .btn-orange:hover {
            background: linear-gradient(135deg, #ff9500 0%, #ff7635 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 140, 0, 0.4);
        }

        .btn-action {
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 2px;
        }

        .btn-action:hover {
            transform: translateY(-2px);
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #28a745, #20c997);
        }

        .stat-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #ff8c00;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
        }

        .attendance-table {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .attendance-table th {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            color: white;
            font-weight: 600;
            border: none;
            padding: 15px;
        }

        .attendance-table td {
            padding: 15px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            vertical-align: middle;
        }

        .attendance-table tbody tr:hover {
            background: rgba(255, 140, 0, 0.05);
        }

        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.85rem;
        }

        .status-present { background: #d4edda; color: #155724; }
        .status-absent { background: #f8d7da; color: #721c24; }
        .status-late { background: #fff3cd; color: #856404; }
        .status-excused { background: #d1ecf1; color: #0c5460; }

        .filter-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .monthly-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .monthly-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .calendar-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-right: 20px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-users me-2 text-orange"></i>
                IGSS Parent Portal
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle fw-semibold" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($parent['first_name'] . ' ' . $parent['last_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="index.php"><i class="fas fa-home me-2"></i>Dashboard</a></li>
                        <li><a class="dropdown-item" href="ward-management.php"><i class="fas fa-users me-2"></i>Ward Management</a></li>
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
                        <li><a class="dropdown-item" href="payments.php"><i class="fas fa-credit-card me-2"></i>Fee Payments</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-2">
                            <i class="fas fa-calendar-check me-2"></i>
                            Attendance History - <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                        </h2>
                        <p class="text-muted mb-0">
                            <?php echo htmlspecialchars($student['class_name'] . ' - ' . $student['level_name']); ?>
                        </p>
                    </div>
                    <div>
                        <a href="ward-details.php?id=<?php echo $student_id; ?>" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Details
                        </a>
                        <button class="btn btn-orange" onclick="exportAttendance()">
                            <i class="fas fa-download me-2"></i>Export Report
                        </button>
                    </div>
                </div>
            </div>

            <!-- Filter Section -->
            <div class="filter-card">
                <form method="GET" action="">
                    <input type="hidden" name="id" value="<?php echo $student_id; ?>">
                    <div class="row align-items-end">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">
                                <i class="fas fa-calendar me-1"></i>Start Date
                            </label>
                            <input type="date" class="form-control" id="start_date" name="start_date"
                                   value="<?php echo $start_date; ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">
                                <i class="fas fa-calendar-alt me-1"></i>End Date
                            </label>
                            <input type="date" class="form-control" id="end_date" name="end_date"
                                   value="<?php echo $end_date; ?>">
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-filter me-2"></i>Filter
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="setCurrentMonth()">
                                <i class="fas fa-calendar me-2"></i>This Month
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Attendance Statistics -->
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $total_days; ?></div>
                        <div class="stat-label">Total Days</div>
                        <small class="text-muted">
                            <?php echo date('M d', strtotime($start_date)); ?> - <?php echo date('M d, Y', strtotime($end_date)); ?>
                        </small>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number text-success"><?php echo $present_days; ?></div>
                        <div class="stat-label">Present Days</div>
                        <small class="text-muted">
                            <?php echo $total_days > 0 ? round(($present_days / $total_days) * 100, 1) : 0; ?>% of total
                        </small>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number text-danger"><?php echo $absent_days; ?></div>
                        <div class="stat-label">Absent Days</div>
                        <small class="text-muted">
                            <?php echo $total_days > 0 ? round(($absent_days / $total_days) * 100, 1) : 0; ?>% of total
                        </small>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $attendance_percentage; ?>%</div>
                        <div class="stat-label">Attendance Rate</div>
                        <small class="text-muted">
                            <?php if ($attendance_percentage >= 90): ?>
                                <span class="text-success">Excellent</span>
                            <?php elseif ($attendance_percentage >= 80): ?>
                                <span class="text-warning">Good</span>
                            <?php else: ?>
                                <span class="text-danger">Needs Improvement</span>
                            <?php endif; ?>
                        </small>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Detailed Attendance Records -->
                <div class="col-lg-8">
                    <div class="content-card">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2"></i>Attendance Records
                            </h5>
                            <div class="progress" style="width: 200px; height: 8px;">
                                <div class="progress-bar bg-success" role="progressbar"
                                     style="width: <?php echo $attendance_percentage; ?>%">
                                </div>
                            </div>
                        </div>

                        <?php if (!empty($attendance_records)): ?>
                            <div class="table-responsive">
                                <table class="table attendance-table mb-0">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Day</th>
                                            <th>Status</th>
                                            <th>Time In</th>
                                            <th>Time Out</th>
                                            <th>Remarks</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($attendance_records as $record): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo date('M d, Y', strtotime($record['attendance_date'])); ?></strong>
                                                </td>
                                                <td>
                                                    <span class="text-muted"><?php echo $record['day_name']; ?></span>
                                                </td>
                                                <td>
                                                    <span class="status-badge status-<?php echo strtolower($record['status']); ?>">
                                                        <?php echo htmlspecialchars($record['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($record['time_in']): ?>
                                                        <i class="fas fa-clock me-1"></i>
                                                        <?php echo date('g:i A', strtotime($record['time_in'])); ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">N/A</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($record['time_out']): ?>
                                                        <i class="fas fa-clock me-1"></i>
                                                        <?php echo date('g:i A', strtotime($record['time_out'])); ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">N/A</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <?php echo htmlspecialchars($record['remarks'] ?? 'No remarks'); ?>
                                                    </small>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Attendance Records</h5>
                                <p class="text-muted">
                                    No attendance records found for the selected date range.
                                </p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Monthly Summary -->
                <div class="col-lg-4">
                    <div class="content-card">
                        <h5 class="mb-4">
                            <i class="fas fa-chart-bar me-2"></i>Monthly Summary
                        </h5>

                        <?php if (!empty($monthly_summary)): ?>
                            <?php foreach ($monthly_summary as $month): ?>
                                <?php
                                $month_percentage = $month['total_days'] > 0 ? round(($month['present_days'] / $month['total_days']) * 100, 1) : 0;
                                ?>
                                <div class="monthly-card">
                                    <div class="d-flex align-items-center">
                                        <div class="calendar-icon">
                                            <i class="fas fa-calendar"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($month['month_name']); ?></h6>
                                            <div class="row text-center">
                                                <div class="col-4">
                                                    <div class="small text-success"><?php echo $month['present_days']; ?></div>
                                                    <div class="small text-muted">Present</div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="small text-danger"><?php echo $month['absent_days']; ?></div>
                                                    <div class="small text-muted">Absent</div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="small text-primary"><?php echo $month_percentage; ?>%</div>
                                                    <div class="small text-muted">Rate</div>
                                                </div>
                                            </div>
                                            <div class="progress mt-2" style="height: 4px;">
                                                <div class="progress-bar bg-success" style="width: <?php echo $month_percentage; ?>%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-chart-bar fa-2x mb-3"></i>
                                <p>No monthly data available</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Attendance Insights -->
            <?php if (!empty($attendance_records)): ?>
                <div class="content-card">
                    <h5 class="mb-4">
                        <i class="fas fa-lightbulb me-2"></i>Attendance Insights
                    </h5>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-success">
                                <i class="fas fa-thumbs-up me-1"></i>Positive Trends
                            </h6>
                            <ul class="list-unstyled">
                                <?php if ($attendance_percentage >= 90): ?>
                                    <li class="mb-2">
                                        <i class="fas fa-star text-warning me-2"></i>
                                        Excellent attendance rate of <?php echo $attendance_percentage; ?>%
                                    </li>
                                <?php endif; ?>
                                <?php if ($late_days == 0): ?>
                                    <li class="mb-2">
                                        <i class="fas fa-clock text-success me-2"></i>
                                        No late arrivals in the selected period
                                    </li>
                                <?php endif; ?>
                                <?php if ($present_days > $absent_days): ?>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        More present days (<?php echo $present_days; ?>) than absent days (<?php echo $absent_days; ?>)
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>

                        <div class="col-md-6">
                            <h6 class="text-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>Areas for Improvement
                            </h6>
                            <ul class="list-unstyled">
                                <?php if ($attendance_percentage < 80): ?>
                                    <li class="mb-2">
                                        <i class="fas fa-arrow-up text-warning me-2"></i>
                                        Attendance rate needs improvement (<?php echo $attendance_percentage; ?>%)
                                    </li>
                                <?php endif; ?>
                                <?php if ($late_days > 0): ?>
                                    <li class="mb-2">
                                        <i class="fas fa-clock text-warning me-2"></i>
                                        <?php echo $late_days; ?> late arrival<?php echo $late_days > 1 ? 's' : ''; ?> recorded
                                    </li>
                                <?php endif; ?>
                                <?php if ($absent_days > 3): ?>
                                    <li class="mb-2">
                                        <i class="fas fa-calendar-times text-danger me-2"></i>
                                        High number of absent days (<?php echo $absent_days; ?>)
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function setCurrentMonth() {
            const now = new Date();
            const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
            const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);

            document.getElementById('start_date').value = firstDay.toISOString().split('T')[0];
            document.getElementById('end_date').value = lastDay.toISOString().split('T')[0];
        }

        function exportAttendance() {
            // Create CSV content
            let csv = 'Date,Day,Status,Time In,Time Out,Remarks\n';

            <?php if (!empty($attendance_records)): ?>
                <?php foreach ($attendance_records as $record): ?>
                    csv += '<?php echo date('Y-m-d', strtotime($record['attendance_date'])); ?>,';
                    csv += '<?php echo $record['day_name']; ?>,';
                    csv += '<?php echo addslashes($record['status']); ?>,';
                    csv += '<?php echo $record['time_in'] ? date('H:i', strtotime($record['time_in'])) : 'N/A'; ?>,';
                    csv += '<?php echo $record['time_out'] ? date('H:i', strtotime($record['time_out'])) : 'N/A'; ?>,';
                    csv += '<?php echo addslashes($record['remarks'] ?? 'No remarks'); ?>\n';
                <?php endforeach; ?>
            <?php endif; ?>

            // Download CSV
            const blob = new Blob([csv], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '<?php echo htmlspecialchars($student['first_name'] . '_' . $student['last_name']); ?>_attendance.csv';
            a.click();
            window.URL.revokeObjectURL(url);
        }

        // Enhanced hover effects
        document.addEventListener('DOMContentLoaded', function() {
            const statCards = document.querySelectorAll('.stat-card');

            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.05)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Add hover effects to table rows
            const tableRows = document.querySelectorAll('.attendance-table tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.01)';
                });

                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // Add hover effects to monthly cards
            const monthlyCards = document.querySelectorAll('.monthly-card');
            monthlyCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
