<?php
/**
 * Assign Form Teachers Page for IGSS Management System
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
requireLogin();
if (!hasRole('admin')) {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$message = '';
$error = '';

// Check if form_teachers table exists, create if not
try {
    $stmt = $conn->query("SHOW TABLES LIKE 'form_teachers'");
    if ($stmt->rowCount() == 0) {
        // Create form_teachers table
        $conn->exec("
            CREATE TABLE form_teachers (
                id INT PRIMARY KEY AUTO_INCREMENT,
                teacher_id INT NOT NULL,
                class_id INT NOT NULL,
                session_id INT NOT NULL DEFAULT 1,
                assigned_by INT NOT NULL,
                assigned_date DATE NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_form_teachers_teacher (teacher_id),
                INDEX idx_form_teachers_class (class_id),
                UNIQUE KEY unique_class_session (class_id, session_id)
            )
        ");
        $message = 'Form teachers table created successfully. You can now assign form teachers.';
    }
} catch (Exception $e) {
    $error = 'Database setup error: ' . $e->getMessage();
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'assign_form_teacher') {
        $teacher_id = $_POST['teacher_id'];
        $class_id = $_POST['class_id'];
        $session_id = getCurrentAcademicInfo()['session_id'];

        try {
            // Check if class already has a form teacher for this session
            $stmt = $conn->prepare("
                SELECT id FROM form_teachers
                WHERE class_id = ? AND session_id = ?
            ");
            $stmt->execute([$class_id, $session_id]);

            if ($stmt->fetch()) {
                $error = 'This class already has a form teacher assigned for the current session.';
            } else {
                // Check if teacher is already a form teacher for another class this session
                $stmt = $conn->prepare("
                    SELECT c.class_name
                    FROM form_teachers ft
                    JOIN classes c ON ft.class_id = c.id
                    WHERE ft.teacher_id = ? AND ft.session_id = ?
                ");
                $stmt->execute([$teacher_id, $session_id]);
                $existing_class = $stmt->fetch();

                if ($existing_class) {
                    $error = 'This teacher is already a form teacher for ' . $existing_class['class_name'] . ' in the current session.';
                } else {
                    $stmt = $conn->prepare("
                        INSERT INTO form_teachers (teacher_id, class_id, session_id, assigned_by, assigned_date)
                        VALUES (?, ?, ?, ?, CURDATE())
                    ");
                    $stmt->execute([$teacher_id, $class_id, $session_id, $_SESSION['user_id']]);
                    $message = 'Form teacher assigned successfully.';
                }
            }
        } catch (Exception $e) {
            $error = 'Failed to assign form teacher: ' . $e->getMessage();
        }
    } elseif ($action === 'remove_form_teacher') {
        $assignment_id = $_POST['assignment_id'];

        try {
            $stmt = $conn->prepare("DELETE FROM form_teachers WHERE id = ?");
            $stmt->execute([$assignment_id]);
            $message = 'Form teacher assignment removed successfully.';
        } catch (Exception $e) {
            $error = 'Failed to remove assignment: ' . $e->getMessage();
        }
    }
}

// Get teachers who are not already form teachers for current session
$current_academic = getCurrentAcademicInfo();
$current_session_id = 1; // Default to session ID 1 for now
$stmt = $conn->prepare("
    SELECT u.id, u.first_name, u.last_name, t.employee_id, t.qualification
    FROM users u
    JOIN teachers t ON u.id = t.user_id
    WHERE u.user_type = 'teacher' AND u.is_approved = 1 AND u.is_active = 1
    AND u.id NOT IN (
        SELECT teacher_id FROM form_teachers WHERE session_id = ?
    )
    ORDER BY u.first_name, u.last_name
");
$stmt->execute([$current_session_id]);
$available_teachers = $stmt->fetchAll();

// Get classes without form teachers for current session
$stmt = $conn->prepare("
    SELECT c.*, l.level_name
    FROM classes c
    JOIN levels l ON c.level_id = l.id
    WHERE c.id NOT IN (
        SELECT class_id FROM form_teachers WHERE session_id = ?
    )
    ORDER BY l.id, c.class_name
");
$stmt->execute([$current_session_id]);
$available_classes = $stmt->fetchAll();

// Get current form teacher assignments
$current_assignments = [];
try {
    $stmt = $conn->prepare("
        SELECT ft.id as assignment_id,
               CONCAT(u.first_name, ' ', u.last_name) as teacher_name,
               t.employee_id,
               c.class_name,
               l.level_name,
               ft.assigned_date,
               COUNT(s.id) as student_count
        FROM form_teachers ft
        JOIN users u ON ft.teacher_id = u.id
        JOIN teachers t ON u.id = t.user_id
        JOIN classes c ON ft.class_id = c.id
        JOIN levels l ON c.level_id = l.id
        LEFT JOIN students s ON c.id = s.class_id
        WHERE ft.session_id = ? AND ft.is_active = 1
        GROUP BY ft.id, u.first_name, u.last_name, t.employee_id, c.class_name, l.level_name, ft.assigned_date
        ORDER BY l.id, c.class_name
    ");
    $stmt->execute([$current_session_id]);
    $current_assignments = $stmt->fetchAll();
} catch (Exception $e) {
    // If table doesn't exist or query fails, assignments will remain empty array
    if (!$error) {
        $error = 'Unable to load form teacher assignments: ' . $e->getMessage();
    }
}

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assign Form Teachers - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }

        .sidebar {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        /* Custom white scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.8);
        }

        /* For Firefox */
        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.6) rgba(255, 255, 255, 0.2);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 80px;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.25) 100%);
            border-radius: 60px 0 0 60px;
        }

        .sidebar-header {
            padding: 25px 20px; /* Reduced padding */
            text-align: center;
            position: relative;
            background: rgba(255, 255, 255, 0.01); /* Minimal opacity */
            border-radius: 0 0 30px 30px;
            margin-bottom: 15px; /* Reduced margin */
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 700;
            font-size: 1.4rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header small {
            opacity: 0.9;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            margin-bottom: 20px; /* Extra space before back to home button */
        }

        .sidebar-menu li {
            margin: 1px 15px; /* Minimal margin for compact layout */
        }

        .sidebar-menu a {
            color: white;
            text-decoration: none;
            padding: 8px 20px; /* Smaller padding like other pages */
            display: block;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 25px;
            position: relative;
            font-weight: 500; /* Normal font weight */
            font-size: 0.85rem; /* Smaller font size */
            background: rgba(255, 255, 255, 0.05); /* Very subtle background */
            border: none; /* Remove border */
            margin-bottom: 1px; /* Minimal spacing */
        }

        .sidebar-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.03); /* Minimal white overlay */
            border-radius: 25px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover::before, .sidebar-menu a.active::before {
            opacity: 1;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            transform: translateX(8px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .sidebar-menu a.active {
            background: rgba(255, 255, 255, 0.05); /* Minimal white background */
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .back-to-home {
            position: absolute;
            bottom: 15px;
            left: 15px;
            right: 15px;
            z-index: 1001;
        }

        .back-to-home .btn {
            background: rgba(255, 255, 255, 0.2); /* More visible white background */
            border: 2px solid rgba(255, 255, 255, 0.4); /* More visible white border */
            color: white;
            font-weight: 700;
            padding: 15px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            width: 100%;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .back-to-home .btn:hover {
            background: rgba(255, 255, 255, 0.3); /* More visible hover effect */
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
            position: relative;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px 30px;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff9a56, #ff6b35, #f7931e);
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 25px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff9a56, #ff6b35, #f7931e);
        }

        .form-teacher-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }

        .form-teacher-card:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .teacher-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
            margin-right: 20px;
        }

        .class-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 10px;
        }

        .student-count {
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.85rem;
            color: #666;
        }

        .select2-container--default .select2-selection--single {
            height: 38px;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 36px;
            padding-left: 12px;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-graduation-cap me-2"></i>IGSS Admin</h4>
            <small>Management System</small>
        </div>

        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
            <li><a href="manage-users.php"><i class="fas fa-users me-2"></i>Manage Users</a></li>
            <li><a href="academic-settings.php"><i class="fas fa-calendar-alt me-2"></i>Academic Settings</a></li>
            <li><a href="assign-subjects.php"><i class="fas fa-book me-2"></i>Assign Subjects</a></li>
            <li><a href="assign-form-teachers.php" class="active"><i class="fas fa-chalkboard-teacher me-2"></i>Form Teachers</a></li>
            <li><a href="manage-fees.php"><i class="fas fa-money-bill me-2"></i>Manage Fees</a></li>
            <li><a href="fee-confirmations.php"><i class="fas fa-money-check-alt me-2"></i>Fee Confirmations</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar me-2"></i>Reports</a></li>
            <li><a href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
            <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
        </ul>

        <!-- Back to Home Button -->
        <div class="back-to-home">
            <a href="../index.php" class="btn">
                <i class="fas fa-home me-2"></i>Back to Home
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><i class="fas fa-chalkboard-teacher me-2"></i>Assign Form Teachers</h4>
                    <small class="text-muted">Manage form teacher assignments for classes</small>
                </div>
                <div>
                    <span class="badge bg-primary me-2">Session: <?php echo $current_academic['session']; ?></span>
                    <span class="badge bg-info">Term: <?php echo TERMS[$current_academic['term']]; ?></span>
                </div>
            </div>
        </div>

        <!-- Alerts -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Assignment Form -->
        <?php if (!empty($available_teachers) && !empty($available_classes)): ?>
        <div class="content-card">
            <h5 class="mb-4"><i class="fas fa-plus me-2"></i>Assign New Form Teacher</h5>
            <form method="POST" class="row g-3">
                <input type="hidden" name="action" value="assign_form_teacher">

                <div class="col-md-6">
                    <label class="form-label">Select Teacher</label>
                    <select name="teacher_id" class="form-select select2" required>
                        <option value="">Choose a teacher...</option>
                        <?php foreach ($available_teachers as $teacher): ?>
                            <option value="<?php echo $teacher['id']; ?>">
                                <?php echo htmlspecialchars($teacher['first_name'] . ' ' . $teacher['last_name']); ?>
                                (ID: <?php echo htmlspecialchars($teacher['employee_id']); ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <small class="form-text text-muted">Only teachers not currently assigned as form teachers are shown</small>
                </div>

                <div class="col-md-6">
                    <label class="form-label">Select Class</label>
                    <select name="class_id" class="form-select select2" required>
                        <option value="">Choose a class...</option>
                        <?php foreach ($available_classes as $class): ?>
                            <option value="<?php echo $class['id']; ?>">
                                <?php echo htmlspecialchars($class['class_name']); ?> -
                                <?php echo htmlspecialchars($class['level_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <small class="form-text text-muted">Only classes without form teachers are shown</small>
                </div>

                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Assign Form Teacher
                    </button>
                </div>
            </form>
        </div>
        <?php else: ?>
        <div class="content-card">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <?php if (empty($available_teachers) && empty($available_classes)): ?>
                    All teachers have been assigned as form teachers and all classes have form teachers assigned.
                <?php elseif (empty($available_teachers)): ?>
                    All available teachers have been assigned as form teachers for this session.
                <?php else: ?>
                    All classes have form teachers assigned for this session.
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Current Assignments -->
        <div class="content-card">
            <h5 class="mb-4"><i class="fas fa-list me-2"></i>Current Form Teacher Assignments</h5>

            <?php if (empty($current_assignments)): ?>
                <div class="empty-state">
                    <i class="fas fa-chalkboard-teacher"></i>
                    <h6>No Form Teachers Assigned Yet</h6>
                    <p>Start by assigning form teachers to classes using the form above.</p>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($current_assignments as $assignment): ?>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-teacher-card">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="teacher-avatar">
                                        <?php
                                        $names = explode(' ', $assignment['teacher_name']);
                                        echo strtoupper(substr($names[0], 0, 1) . substr($names[1] ?? '', 0, 1));
                                        ?>
                                    </div>
                                    <div>
                                        <h6 class="mb-1"><?php echo htmlspecialchars($assignment['teacher_name']); ?></h6>
                                        <small class="text-muted">ID: <?php echo htmlspecialchars($assignment['employee_id']); ?></small>
                                    </div>
                                </div>

                                <div class="class-badge">
                                    <i class="fas fa-school me-2"></i>
                                    <?php echo htmlspecialchars($assignment['class_name']); ?>
                                </div>

                                <div class="mb-3">
                                    <span class="badge bg-secondary me-2"><?php echo htmlspecialchars($assignment['level_name']); ?></span>
                                    <span class="student-count">
                                        <i class="fas fa-users me-1"></i>
                                        <?php echo $assignment['student_count']; ?> students
                                    </span>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        Assigned: <?php echo date('M j, Y', strtotime($assignment['assigned_date'])); ?>
                                    </small>
                                </div>

                                <form method="POST">
                                    <input type="hidden" name="action" value="remove_form_teacher">
                                    <input type="hidden" name="assignment_id" value="<?php echo $assignment['assignment_id']; ?>">
                                    <button type="submit" class="btn btn-outline-danger btn-sm w-100"
                                            onclick="return confirm('Remove this form teacher assignment?')">
                                        <i class="fas fa-trash me-2"></i>Remove Assignment
                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Summary Statistics -->
        <div class="content-card">
            <h5 class="mb-4"><i class="fas fa-chart-pie me-2"></i>Assignment Summary</h5>
            <div class="row text-center">
                <div class="col-md-3">
                    <div class="p-3">
                        <h3 class="text-primary"><?php echo count($current_assignments); ?></h3>
                        <small class="text-muted">Classes with Form Teachers</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="p-3">
                        <h3 class="text-warning"><?php echo count($available_classes); ?></h3>
                        <small class="text-muted">Classes without Form Teachers</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="p-3">
                        <h3 class="text-success"><?php echo count($available_teachers); ?></h3>
                        <small class="text-muted">Available Teachers</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="p-3">
                        <h3 class="text-info"><?php echo array_sum(array_column($current_assignments, 'student_count')); ?></h3>
                        <small class="text-muted">Total Students Covered</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        $(document).ready(function() {
            $('.select2').select2({
                theme: 'default',
                width: '100%'
            });
        });
    </script>
</body>
</html>
