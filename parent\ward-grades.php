<?php
/**
 * Ward Academic Records/Grades Page for Parents
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a parent
if (!isLoggedIn() || $_SESSION['user_type'] !== 'parent') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get parent information including approval status
$stmt = $conn->prepare("
    SELECT p.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, u.user_code, u.is_approved
    FROM parents p
    JOIN users u ON p.user_id = u.id
    WHERE p.user_id = ?
");
$stmt->execute([$user_id]);
$parent = $stmt->fetch();

if (!$parent) {
    header('Location: ../login.php');
    exit();
}

// Check if parent account is approved
if (!$parent['is_approved']) {
    header('Location: pending-approval.php');
    exit();
}

// Get student ID from URL
$student_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$student_id) {
    header('Location: index.php');
    exit();
}

// Verify parent has access to this student
$stmt = $conn->prepare("
    SELECT COUNT(*) FROM parent_student_relationships 
    WHERE parent_id = ? AND student_id = ?
");
$stmt->execute([$parent['id'], $student_id]);
$has_access = $stmt->fetchColumn();

if (!$has_access) {
    header('Location: index.php');
    exit();
}

// Get student information
$stmt = $conn->prepare("
    SELECT 
        s.*,
        u.first_name,
        u.last_name,
        u.middle_name,
        c.class_name,
        l.level_name,
        psr.relationship_type
    FROM students s
    JOIN users u ON s.user_id = u.id
    JOIN classes c ON s.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    JOIN parent_student_relationships psr ON s.id = psr.student_id
    WHERE s.id = ? AND psr.parent_id = ?
");
$stmt->execute([$student_id, $parent['id']]);
$student = $stmt->fetch();

if (!$student) {
    header('Location: index.php');
    exit();
}

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();

// Get selected session and term (default to current)
$selected_session = isset($_GET['session']) ? (int)$_GET['session'] : $current_academic['session_id'];
$selected_term = isset($_GET['term']) ? (int)$_GET['term'] : $current_academic['term_id'];

// Get available sessions
$stmt = $conn->prepare("SELECT id, session_name FROM academic_sessions ORDER BY session_name DESC");
$stmt->execute();
$sessions = $stmt->fetchAll();

// Get available terms
$stmt = $conn->prepare("SELECT id, term_name FROM terms ORDER BY id");
$stmt->execute();
$terms = $stmt->fetchAll();

// Get grades for selected session and term
$stmt = $conn->prepare("
    SELECT 
        sg.*,
        sub.subject_name,
        sub.subject_code,
        t.first_name as teacher_first_name,
        t.last_name as teacher_last_name
    FROM student_grades sg
    JOIN subjects sub ON sg.subject_id = sub.id
    LEFT JOIN subject_assignments sa ON sub.id = sa.subject_id AND sa.class_id = ?
    LEFT JOIN teachers teach ON sa.teacher_id = teach.id
    LEFT JOIN users t ON teach.user_id = t.id
    WHERE sg.student_id = ? AND sg.session_id = ? AND sg.term_id = ?
    ORDER BY sub.subject_name
");
$stmt->execute([$student['class_id'], $student_id, $selected_session, $selected_term]);
$grades = $stmt->fetchAll();

// Calculate overall statistics
$total_subjects = count($grades);
$total_score = 0;
$total_possible = 0;
$grade_distribution = ['A' => 0, 'B' => 0, 'C' => 0, 'D' => 0, 'E' => 0, 'F' => 0];

foreach ($grades as $grade) {
    if ($grade['total_score'] !== null) {
        $total_score += $grade['total_score'];
        $total_possible += 100; // Assuming 100 is the maximum score per subject
        
        if (isset($grade_distribution[$grade['grade']])) {
            $grade_distribution[$grade['grade']]++;
        }
    }
}

$overall_average = $total_possible > 0 ? round(($total_score / $total_possible) * 100, 1) : 0;

// Determine overall grade
$overall_grade = 'F';
if ($overall_average >= 80) $overall_grade = 'A';
elseif ($overall_average >= 70) $overall_grade = 'B';
elseif ($overall_average >= 60) $overall_grade = 'C';
elseif ($overall_average >= 50) $overall_grade = 'D';
elseif ($overall_average >= 40) $overall_grade = 'E';

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Academic Records - <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?> - <?php echo $school_info['name']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 50%, #ff4500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }
        
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 1050;
        }

        .navbar-custom .dropdown-menu {
            z-index: 1060;
            position: absolute;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            margin-top: 8px;
            min-width: 220px;
        }

        .navbar-custom .dropdown-item {
            padding: 12px 20px;
            border-radius: 10px;
            margin: 4px 8px;
            transition: all 0.3s ease;
        }

        .navbar-custom .dropdown-item:hover {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            color: white;
            transform: translateX(5px);
        }

        .navbar-custom .dropdown-item i {
            width: 20px;
            text-align: center;
        }
        
        .main-container {
            padding: 20px;
            position: relative;
            z-index: 1;
        }
        
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff8c00, #ff6b35);
        }
        
        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff8c00, #ff6b35);
        }
        
        .btn-orange {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            border: none;
            color: white;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 140, 0, 0.3);
        }
        
        .btn-orange:hover {
            background: linear-gradient(135deg, #ff9500 0%, #ff7635 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 140, 0, 0.4);
        }

        .btn-action {
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 2px;
        }

        .btn-action:hover {
            transform: translateY(-2px);
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #28a745, #20c997);
        }

        .stat-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #ff8c00;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
        }

        .grade-table {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .grade-table th {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            color: white;
            font-weight: 600;
            border: none;
            padding: 15px;
        }

        .grade-table td {
            padding: 15px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            vertical-align: middle;
        }

        .grade-table tbody tr:hover {
            background: rgba(255, 140, 0, 0.05);
        }

        .grade-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .grade-A { background: #d4edda; color: #155724; }
        .grade-B { background: #d1ecf1; color: #0c5460; }
        .grade-C { background: #fff3cd; color: #856404; }
        .grade-D { background: #ffeaa7; color: #6c5ce7; }
        .grade-E { background: #ffeaa7; color: #e17055; }
        .grade-F { background: #f8d7da; color: #721c24; }

        .subject-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #6c757d;
            font-size: 0.85rem;
        }

        .score-breakdown {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .filter-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-users me-2 text-orange"></i>
                IGSS Parent Portal
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle fw-semibold" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($parent['first_name'] . ' ' . $parent['last_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="index.php"><i class="fas fa-home me-2"></i>Dashboard</a></li>
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
                        <li><a class="dropdown-item" href="ward-management.php"><i class="fas fa-users me-2"></i>Ward Management</a></li>
                        <li><a class="dropdown-item" href="payments.php"><i class="fas fa-credit-card me-2"></i>Fee Payments</a></li>
                        <li><a class="dropdown-item" href="messages.php"><i class="fas fa-envelope me-2"></i>Messages</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-2">
                            <i class="fas fa-chart-line me-2"></i>
                            Academic Records - <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                        </h2>
                        <p class="text-muted mb-0">
                            <?php echo htmlspecialchars($student['class_name'] . ' - ' . $student['level_name']); ?>
                        </p>
                    </div>
                    <div>
                        <a href="ward-details.php?id=<?php echo $student_id; ?>" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Details
                        </a>
                        <button class="btn btn-orange" onclick="window.print()">
                            <i class="fas fa-print me-2"></i>Print Report
                        </button>
                    </div>
                </div>
            </div>

            <!-- Filter Section -->
            <div class="filter-card">
                <form method="GET" action="">
                    <input type="hidden" name="id" value="<?php echo $student_id; ?>">
                    <div class="row align-items-end">
                        <div class="col-md-4">
                            <label for="session" class="form-label">
                                <i class="fas fa-calendar me-1"></i>Academic Session
                            </label>
                            <select class="form-select" id="session" name="session" onchange="this.form.submit()">
                                <?php foreach ($sessions as $session): ?>
                                    <option value="<?php echo $session['id']; ?>"
                                            <?php echo $session['id'] == $selected_session ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($session['session_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="term" class="form-label">
                                <i class="fas fa-calendar-alt me-1"></i>Term
                            </label>
                            <select class="form-select" id="term" name="term" onchange="this.form.submit()">
                                <?php foreach ($terms as $term): ?>
                                    <option value="<?php echo $term['id']; ?>"
                                            <?php echo $term['id'] == $selected_term ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($term['term_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i>Filter Results
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Overall Performance Stats -->
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $total_subjects; ?></div>
                        <div class="stat-label">Total Subjects</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $overall_average; ?>%</div>
                        <div class="stat-label">Overall Average</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number grade-<?php echo $overall_grade; ?>" style="color: inherit;">
                            <?php echo $overall_grade; ?>
                        </div>
                        <div class="stat-label">Overall Grade</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $grade_distribution['A'] + $grade_distribution['B']; ?></div>
                        <div class="stat-label">A & B Grades</div>
                    </div>
                </div>
            </div>

            <!-- Detailed Grades Table -->
            <div class="content-card">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>Detailed Grade Report
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-success btn-sm" onclick="exportToCSV()">
                            <i class="fas fa-file-csv me-1"></i>Export CSV
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="generatePDF()">
                            <i class="fas fa-file-pdf me-1"></i>Generate PDF
                        </button>
                    </div>
                </div>

                <?php if (!empty($grades)): ?>
                    <div class="table-responsive">
                        <table class="table grade-table mb-0">
                            <thead>
                                <tr>
                                    <th>Subject</th>
                                    <th>Teacher</th>
                                    <th>CA Score</th>
                                    <th>Exam Score</th>
                                    <th>Total Score</th>
                                    <th>Grade</th>
                                    <th>Remarks</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($grades as $grade): ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($grade['subject_name']); ?></strong>
                                                <br>
                                                <span class="subject-code"><?php echo htmlspecialchars($grade['subject_code']); ?></span>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($grade['teacher_first_name']): ?>
                                                <?php echo htmlspecialchars($grade['teacher_first_name'] . ' ' . $grade['teacher_last_name']); ?>
                                            <?php else: ?>
                                                <span class="text-muted">Not assigned</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo $grade['ca_score'] ?? 'N/A'; ?></strong>
                                            <?php if ($grade['ca_score']): ?>
                                                <div class="score-breakdown">out of 40</div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo $grade['exam_score'] ?? 'N/A'; ?></strong>
                                            <?php if ($grade['exam_score']): ?>
                                                <div class="score-breakdown">out of 60</div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong class="text-primary"><?php echo $grade['total_score'] ?? 'N/A'; ?></strong>
                                            <?php if ($grade['total_score']): ?>
                                                <div class="score-breakdown">out of 100</div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($grade['grade']): ?>
                                                <span class="grade-badge grade-<?php echo $grade['grade']; ?>">
                                                    <?php echo $grade['grade']; ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($grade['remarks'] ?? 'No remarks'); ?>
                                            </small>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Grades Available</h5>
                        <p class="text-muted">
                            No grades have been recorded for the selected session and term.
                        </p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Grade Distribution Chart -->
            <?php if (!empty($grades)): ?>
                <div class="content-card">
                    <h5 class="mb-4">
                        <i class="fas fa-chart-pie me-2"></i>Grade Distribution
                    </h5>

                    <div class="row">
                        <?php foreach ($grade_distribution as $grade_letter => $count): ?>
                            <?php if ($count > 0): ?>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="text-center">
                                        <div class="grade-badge grade-<?php echo $grade_letter; ?> d-inline-block mb-2">
                                            Grade <?php echo $grade_letter; ?>
                                        </div>
                                        <div class="h4 mb-0"><?php echo $count; ?></div>
                                        <small class="text-muted">
                                            <?php echo $total_subjects > 0 ? round(($count / $total_subjects) * 100, 1) : 0; ?>%
                                        </small>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Performance Analysis -->
            <?php if (!empty($grades)): ?>
                <div class="content-card">
                    <h5 class="mb-4">
                        <i class="fas fa-analytics me-2"></i>Performance Analysis
                    </h5>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>Strengths
                            </h6>
                            <ul class="list-unstyled">
                                <?php
                                $strong_subjects = array_filter($grades, function($grade) {
                                    return $grade['total_score'] && $grade['total_score'] >= 70;
                                });
                                ?>
                                <?php if (!empty($strong_subjects)): ?>
                                    <?php foreach (array_slice($strong_subjects, 0, 3) as $subject): ?>
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            <strong><?php echo htmlspecialchars($subject['subject_name']); ?></strong>
                                            (<?php echo $subject['total_score']; ?>% - Grade <?php echo $subject['grade']; ?>)
                                        </li>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <li class="text-muted">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Focus on improving performance across all subjects
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>

                        <div class="col-md-6">
                            <h6 class="text-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>Areas for Improvement
                            </h6>
                            <ul class="list-unstyled">
                                <?php
                                $weak_subjects = array_filter($grades, function($grade) {
                                    return $grade['total_score'] && $grade['total_score'] < 60;
                                });
                                ?>
                                <?php if (!empty($weak_subjects)): ?>
                                    <?php foreach (array_slice($weak_subjects, 0, 3) as $subject): ?>
                                        <li class="mb-2">
                                            <i class="fas fa-arrow-down text-warning me-2"></i>
                                            <strong><?php echo htmlspecialchars($subject['subject_name']); ?></strong>
                                            (<?php echo $subject['total_score']; ?>% - Grade <?php echo $subject['grade']; ?>)
                                        </li>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <li class="text-success">
                                        <i class="fas fa-thumbs-up me-2"></i>
                                        Excellent performance across all subjects!
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function exportToCSV() {
            // Create CSV content
            let csv = 'Subject,Subject Code,Teacher,CA Score,Exam Score,Total Score,Grade,Remarks\n';

            <?php if (!empty($grades)): ?>
                <?php foreach ($grades as $grade): ?>
                    csv += '<?php echo addslashes($grade['subject_name']); ?>,';
                    csv += '<?php echo addslashes($grade['subject_code']); ?>,';
                    csv += '<?php echo addslashes(($grade['teacher_first_name'] ?? '') . ' ' . ($grade['teacher_last_name'] ?? '')); ?>,';
                    csv += '<?php echo $grade['ca_score'] ?? 'N/A'; ?>,';
                    csv += '<?php echo $grade['exam_score'] ?? 'N/A'; ?>,';
                    csv += '<?php echo $grade['total_score'] ?? 'N/A'; ?>,';
                    csv += '<?php echo $grade['grade'] ?? 'N/A'; ?>,';
                    csv += '<?php echo addslashes($grade['remarks'] ?? 'No remarks'); ?>\n';
                <?php endforeach; ?>
            <?php endif; ?>

            // Download CSV
            const blob = new Blob([csv], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '<?php echo htmlspecialchars($student['first_name'] . '_' . $student['last_name']); ?>_grades.csv';
            a.click();
            window.URL.revokeObjectURL(url);
        }

        function generatePDF() {
            alert('PDF generation feature will be available soon!');
        }

        // Enhanced hover effects
        document.addEventListener('DOMContentLoaded', function() {
            const statCards = document.querySelectorAll('.stat-card');

            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.05)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Add hover effects to table rows
            const tableRows = document.querySelectorAll('.grade-table tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.01)';
                });

                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>
