<?php
/**
 * Settings Page for IGSS Management System
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
requireLogin();
if (!hasRole('admin')) {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'update_school_info') {
        $school_name = sanitizeInput($_POST['school_name']);
        $school_address = sanitizeInput($_POST['school_address']);
        $school_phone = sanitizeInput($_POST['school_phone']);
        $school_email = sanitizeInput($_POST['school_email']);
        $school_website = sanitizeInput($_POST['school_website']);

        try {
            $settings = [
                'school_name' => $school_name,
                'school_address' => $school_address,
                'school_phone' => $school_phone,
                'school_email' => $school_email,
                'school_website' => $school_website
            ];

            foreach ($settings as $key => $value) {
                $stmt = $conn->prepare("
                    INSERT INTO system_settings (setting_key, setting_value, updated_by)
                    VALUES (?, ?, ?)
                    ON DUPLICATE KEY UPDATE setting_value = ?, updated_by = ?
                ");
                $stmt->execute([$key, $value, $_SESSION['user_id'], $value, $_SESSION['user_id']]);
            }

            $message = 'School information updated successfully.';
        } catch (Exception $e) {
            $error = 'Failed to update school information: ' . $e->getMessage();
        }
    } elseif ($action === 'update_system_settings') {
        $maintenance_mode = isset($_POST['maintenance_mode']) ? 1 : 0;
        $allow_registration = isset($_POST['allow_registration']) ? 1 : 0;
        $email_notifications = isset($_POST['email_notifications']) ? 1 : 0;
        $auto_approve_teachers = isset($_POST['auto_approve_teachers']) ? 1 : 0;

        try {
            $settings = [
                'maintenance_mode' => $maintenance_mode,
                'allow_registration' => $allow_registration,
                'email_notifications' => $email_notifications,
                'auto_approve_teachers' => $auto_approve_teachers
            ];

            foreach ($settings as $key => $value) {
                $stmt = $conn->prepare("
                    INSERT INTO system_settings (setting_key, setting_value, updated_by)
                    VALUES (?, ?, ?)
                    ON DUPLICATE KEY UPDATE setting_value = ?, updated_by = ?
                ");
                $stmt->execute([$key, $value, $_SESSION['user_id'], $value, $_SESSION['user_id']]);
            }

            $message = 'System settings updated successfully.';
        } catch (Exception $e) {
            $error = 'Failed to update system settings: ' . $e->getMessage();
        }
    } elseif ($action === 'add_fee_type') {
        $fee_type_name = sanitizeInput($_POST['fee_type_name'] ?? '');
        $fee_amount = floatval($_POST['fee_amount'] ?? 0);
        $level_id = $_POST['level_id'] ?? '';
        $description = sanitizeInput($_POST['description'] ?? '');
        $is_exam_fee = isset($_POST['is_exam_fee']) ? 1 : 0;
        $target_class = $_POST['target_class'] ?? '';
        $payment_term = $_POST['payment_term'] ?? '';

        try {
            $stmt = $conn->prepare("
                INSERT INTO fee_types (fee_name, amount_js, amount_ss, fee_description, is_exam_fee, target_class, payment_term, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$fee_type_name, $fee_amount, $fee_amount, $description, $is_exam_fee, $target_class, $payment_term, $_SESSION['user_id']]);
            $message = 'Fee type added successfully.';
        } catch (Exception $e) {
            $error = 'Failed to add fee type: ' . $e->getMessage();
        }
    } elseif ($action === 'update_fee_type') {
        $fee_id = $_POST['fee_id'] ?? '';
        $fee_type_name = sanitizeInput($_POST['fee_type_name'] ?? '');
        $fee_amount = floatval($_POST['fee_amount'] ?? 0);
        $description = sanitizeInput($_POST['description'] ?? '');
        $is_exam_fee = isset($_POST['is_exam_fee']) ? 1 : 0;
        $target_class = $_POST['target_class'] ?? '';
        $payment_term = $_POST['payment_term'] ?? '';

        try {
            $stmt = $conn->prepare("
                UPDATE fee_types
                SET fee_name = ?, amount_js = ?, amount_ss = ?, fee_description = ?,
                    is_exam_fee = ?, target_class = ?, payment_term = ?
                WHERE id = ?
            ");
            $stmt->execute([$fee_type_name, $fee_amount, $fee_amount, $description, $is_exam_fee, $target_class, $payment_term, $fee_id]);
            $message = 'Fee type updated successfully.';
        } catch (Exception $e) {
            $error = 'Failed to update fee type: ' . $e->getMessage();
        }
    } elseif ($action === 'delete_fee_type') {
        $fee_id = $_POST['fee_id'] ?? '';

        try {
            $stmt = $conn->prepare("UPDATE fee_types SET is_active = 0 WHERE id = ?");
            $stmt->execute([$fee_id]);
            $message = 'Fee type deleted successfully.';
        } catch (Exception $e) {
            $error = 'Failed to delete fee type: ' . $e->getMessage();
        }
    }
}

// Get current settings
$school_info = getSchoolInfo();

$stmt = $conn->prepare("SELECT setting_key, setting_value FROM system_settings");
$stmt->execute();
$system_settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

// Get levels for fee types
$stmt = $conn->prepare("SELECT * FROM levels ORDER BY id");
$stmt->execute();
$levels = $stmt->fetchAll();

// Get fee types
$stmt = $conn->prepare("
    SELECT ft.*,
           ft.fee_name as fee_type_name,
           ft.fee_description as description,
           ft.amount_js as amount,
           CASE
               WHEN ft.is_exam_fee = 1 THEN CONCAT(ft.target_class, ' - Term ', ft.payment_term)
               ELSE 'All Levels'
           END as level_name,
           CONCAT(u.first_name, ' ', u.last_name) as created_by_name
    FROM fee_types ft
    LEFT JOIN users u ON ft.created_by = u.id
    WHERE ft.is_active = 1
    ORDER BY ft.is_exam_fee ASC, ft.fee_name
");
$stmt->execute();
$fee_types = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }

        .sidebar {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        /* Custom white scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.8);
        }

        /* For Firefox */
        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.6) rgba(255, 255, 255, 0.2);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 80px;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.25) 100%);
            border-radius: 60px 0 0 60px;
        }

        .sidebar-header {
            padding: 25px 20px; /* Reduced padding */
            text-align: center;
            position: relative;
            background: rgba(255, 255, 255, 0.01); /* Minimal opacity */
            border-radius: 0 0 30px 30px;
            margin-bottom: 15px; /* Reduced margin */
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 700;
            font-size: 1.4rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header small {
            opacity: 0.9;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            margin-bottom: 80px; /* Space for back to home button */
        }

        .sidebar-menu li {
            margin: 1px 15px; /* Minimal margin for tight fit */
        }

        .sidebar-menu a {
            color: white;
            text-decoration: none;
            padding: 8px 20px; /* Smaller padding like other pages */
            display: block;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 25px;
            position: relative;
            font-weight: 500; /* Normal font weight */
            font-size: 0.85rem; /* Smaller font size */
            background: rgba(255, 255, 255, 0.05); /* Very subtle background */
            margin-bottom: 1px; /* Minimal spacing */
        }

        .sidebar-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover::before, .sidebar-menu a.active::before {
            opacity: 1;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            transform: translateX(8px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.15);
        }

        .sidebar-menu a.active {
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .back-to-home {
            position: absolute;
            bottom: 60px; /* Moved higher to avoid overlap */
            left: 15px;
            right: 15px;
            z-index: 1001;
        }

        .back-to-home .btn {
            background: rgba(255, 255, 255, 0.25); /* More visible white background */
            border: 3px solid rgba(255, 255, 255, 0.5); /* Thicker, more visible border */
            color: white;
            font-weight: 700; /* Bold text */
            padding: 15px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            width: 100%;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .back-to-home .btn:hover {
            background: rgba(255, 255, 255, 0.35); /* More visible hover effect */
            border-color: rgba(255, 255, 255, 0.7);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
            position: relative;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px 30px;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff9a56, #ff6b35, #f7931e);
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 25px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff9a56, #ff6b35, #f7931e);
        }

        .nav-tabs .nav-link {
            border: none;
            color: #666;
            font-weight: 500;
        }

        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
        }

        .form-switch .form-check-input {
            width: 3rem;
            height: 1.5rem;
        }

        .settings-section {
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }

        .settings-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .fee-type-card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .fee-type-card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .amount-display {
            font-size: 1.2rem;
            font-weight: bold;
            color: #28a745;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-graduation-cap me-2"></i>IGSS Admin</h4>
            <small>Management System</small>
        </div>

        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
            <li><a href="manage-users.php"><i class="fas fa-users me-2"></i>Manage Users</a></li>
            <li><a href="academic-settings.php"><i class="fas fa-calendar-alt me-2"></i>Academic Settings</a></li>
            <li><a href="assign-subjects.php"><i class="fas fa-book me-2"></i>Assign Subjects</a></li>
            <li><a href="assign-form-teachers.php"><i class="fas fa-chalkboard-teacher me-2"></i>Form Teachers</a></li>
            <li><a href="manage-fees.php"><i class="fas fa-money-bill me-2"></i>Manage Fees</a></li>
            <li><a href="fee-confirmations.php"><i class="fas fa-money-check-alt me-2"></i>Fee Confirmations</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar me-2"></i>Reports</a></li>
            <li><a href="settings.php" class="active"><i class="fas fa-cog me-2"></i>Settings</a></li>
            <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
        </ul>

        <!-- Back to Home Button -->
        <div class="back-to-home">
            <a href="../index.php" class="btn">
                <i class="fas fa-home me-2"></i>Back to Home
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h4 class="mb-0"><i class="fas fa-cog me-2"></i>System Settings</h4>
            <small class="text-muted">Configure system settings and preferences</small>
        </div>

        <!-- Alerts -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Settings Tabs -->
        <div class="content-card">
            <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="school-tab" data-bs-toggle="tab" data-bs-target="#school" type="button" role="tab">
                        <i class="fas fa-school me-2"></i>School Information
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                        <i class="fas fa-cogs me-2"></i>System Settings
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="fees-tab" data-bs-toggle="tab" data-bs-target="#fees" type="button" role="tab">
                        <i class="fas fa-money-bill me-2"></i>Fee Types
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="settingsTabsContent">
                <!-- School Information Tab -->
                <div class="tab-pane fade show active" id="school" role="tabpanel">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_school_info">

                        <div class="settings-section">
                            <h5><i class="fas fa-info-circle me-2"></i>Basic Information</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">School Name</label>
                                        <input type="text" name="school_name" class="form-control"
                                               value="<?php echo htmlspecialchars($school_info['name']); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Email Address</label>
                                        <input type="email" name="school_email" class="form-control"
                                               value="<?php echo htmlspecialchars($school_info['email']); ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Phone Number</label>
                                        <input type="text" name="school_phone" class="form-control"
                                               value="<?php echo htmlspecialchars($school_info['phone']); ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Website</label>
                                        <input type="url" name="school_website" class="form-control"
                                               value="<?php echo htmlspecialchars($school_info['website']); ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Address</label>
                                <textarea name="school_address" class="form-control" rows="3"><?php echo htmlspecialchars($school_info['address']); ?></textarea>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update School Information
                        </button>
                    </form>
                </div>

                <!-- System Settings Tab -->
                <div class="tab-pane fade" id="system" role="tabpanel">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_system_settings">

                        <div class="settings-section">
                            <h5><i class="fas fa-shield-alt me-2"></i>Security & Access</h5>

                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" name="maintenance_mode" id="maintenanceMode"
                                       <?php echo ($system_settings['maintenance_mode'] ?? 0) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="maintenanceMode">
                                    <strong>Maintenance Mode</strong><br>
                                    <small class="text-muted">Temporarily disable access to the system for maintenance</small>
                                </label>
                            </div>

                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" name="allow_registration" id="allowRegistration"
                                       <?php echo ($system_settings['allow_registration'] ?? 1) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="allowRegistration">
                                    <strong>Allow New Registrations</strong><br>
                                    <small class="text-muted">Allow new users to register for accounts</small>
                                </label>
                            </div>

                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" name="auto_approve_teachers" id="autoApproveTeachers"
                                       <?php echo ($system_settings['auto_approve_teachers'] ?? 0) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="autoApproveTeachers">
                                    <strong>Auto-approve Teachers</strong><br>
                                    <small class="text-muted">Automatically approve teacher registrations</small>
                                </label>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h5><i class="fas fa-bell me-2"></i>Notifications</h5>

                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" name="email_notifications" id="emailNotifications"
                                       <?php echo ($system_settings['email_notifications'] ?? 1) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="emailNotifications">
                                    <strong>Email Notifications</strong><br>
                                    <small class="text-muted">Send email notifications for important events</small>
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update System Settings
                        </button>
                    </form>
                </div>

                <!-- Fee Types Tab -->
                <div class="tab-pane fade" id="fees" role="tabpanel">
                    <div class="row">
                        <div class="col-md-4">
                            <h5><i class="fas fa-plus me-2"></i>Add New Fee Type</h5>
                            <form method="POST">
                                <input type="hidden" name="action" value="add_fee_type">

                                <div class="mb-3">
                                    <label class="form-label">Fee Type Name</label>
                                    <input type="text" name="fee_type_name" class="form-control"
                                           placeholder="e.g., Tuition Fee" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Amount (₦)</label>
                                    <input type="number" name="fee_amount" class="form-control"
                                           step="0.01" min="0" placeholder="0.00" required>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="is_exam_fee" id="is_exam_fee" onchange="toggleExamFeeFields()">
                                        <label class="form-check-label" for="is_exam_fee">
                                            This is an Exam Fee (WAEC)
                                        </label>
                                    </div>
                                </div>

                                <div id="exam_fee_fields" style="display: none;">
                                    <div class="mb-3">
                                        <label class="form-label">Target Class</label>
                                        <select name="target_class" class="form-select">
                                            <option value="">Select Class</option>
                                            <option value="JSS3">JSS3 (Junior WAEC)</option>
                                            <option value="SS3">SS3 (Senior WAEC)</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Payment Term</label>
                                        <select name="payment_term" class="form-select">
                                            <option value="">Select Term</option>
                                            <option value="1">First Term</option>
                                            <option value="2">Second Term</option>
                                            <option value="3">Third Term</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Description</label>
                                    <textarea name="description" class="form-control" rows="3"
                                              placeholder="Optional description"></textarea>
                                </div>

                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>Add Fee Type
                                </button>
                            </form>
                        </div>

                        <div class="col-md-8">
                            <h5><i class="fas fa-list me-2"></i>Existing Fee Types</h5>

                            <?php if (empty($fee_types)): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-money-bill fa-3x mb-3"></i>
                                    <p>No fee types configured yet.</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($fee_types as $fee_type): ?>
                                    <div class="fee-type-card">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">
                                                    <?php echo htmlspecialchars($fee_type['fee_type_name'] ?? 'Unknown Fee'); ?>
                                                    <?php if ($fee_type['is_exam_fee']): ?>
                                                        <span class="badge bg-warning text-dark ms-2">EXAM FEE</span>
                                                    <?php endif; ?>
                                                </h6>
                                                <div class="amount-display">₦<?php echo number_format($fee_type['amount'] ?? 0, 2); ?></div>
                                                <span class="badge bg-primary"><?php echo htmlspecialchars($fee_type['level_name'] ?? 'All Levels'); ?></span>
                                                <?php if (!empty($fee_type['description'])): ?>
                                                    <p class="text-muted mt-2 mb-0"><?php echo htmlspecialchars($fee_type['description']); ?></p>
                                                <?php endif; ?>
                                                <small class="text-muted">
                                                    Created by: <?php echo htmlspecialchars($fee_type['created_by_name'] ?? 'Unknown'); ?>
                                                </small>
                                            </div>
                                            <div>
                                                <button class="btn btn-sm btn-outline-primary"
                                                        onclick="editFeeType(<?php echo $fee_type['id']; ?>)"
                                                        data-fee-id="<?php echo $fee_type['id']; ?>"
                                                        data-fee-name="<?php echo htmlspecialchars($fee_type['fee_type_name'] ?? $fee_type['fee_name'] ?? ''); ?>"
                                                        data-fee-amount="<?php echo $fee_type['amount'] ?? $fee_type['amount_js'] ?? 0; ?>"
                                                        data-fee-description="<?php echo htmlspecialchars($fee_type['description'] ?? $fee_type['fee_description'] ?? ''); ?>"
                                                        data-is-exam-fee="<?php echo $fee_type['is_exam_fee'] ?? 0; ?>"
                                                        data-target-class="<?php echo htmlspecialchars($fee_type['target_class'] ?? ''); ?>"
                                                        data-payment-term="<?php echo htmlspecialchars($fee_type['payment_term'] ?? ''); ?>">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteFeeType(<?php echo $fee_type['id']; ?>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Fee Type Modal -->
    <div class="modal fade" id="editFeeModal" tabindex="-1" aria-labelledby="editFeeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editFeeModalLabel">
                        <i class="fas fa-edit me-2"></i>Edit Fee Type
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" id="editFeeForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_fee_type">
                        <input type="hidden" name="fee_id" id="edit_fee_id">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Fee Type Name</label>
                                    <input type="text" name="fee_type_name" id="edit_fee_name" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Amount (₦)</label>
                                    <input type="number" name="fee_amount" id="edit_fee_amount" class="form-control"
                                           step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3" id="edit_exam_fee_section">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_exam_fee" id="edit_is_exam_fee"
                                       onchange="toggleEditExamFeeFields()">
                                <label class="form-check-label" for="edit_is_exam_fee">
                                    This is an Exam Fee (WAEC)
                                </label>
                            </div>
                        </div>

                        <div id="edit_exam_fee_fields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Target Class</label>
                                        <select name="target_class" id="edit_target_class" class="form-select">
                                            <option value="">Select Class</option>
                                            <option value="JSS3">JSS3 (Junior WAEC)</option>
                                            <option value="SS3">SS3 (Senior WAEC)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Payment Term</label>
                                        <select name="payment_term" id="edit_payment_term" class="form-select">
                                            <option value="">Select Term</option>
                                            <option value="1">First Term</option>
                                            <option value="2">Second Term</option>
                                            <option value="3">Third Term</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea name="description" id="edit_description" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Fee Type
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function toggleExamFeeFields() {
            const checkbox = document.getElementById('is_exam_fee');
            const examFields = document.getElementById('exam_fee_fields');

            if (checkbox.checked) {
                examFields.style.display = 'block';
            } else {
                examFields.style.display = 'none';
            }
        }

        function toggleEditExamFeeFields() {
            const checkbox = document.getElementById('edit_is_exam_fee');
            const examFields = document.getElementById('edit_exam_fee_fields');

            if (checkbox.checked) {
                examFields.style.display = 'block';
            } else {
                examFields.style.display = 'none';
            }
        }

        function editFeeType(feeId) {
            // Find the button that was clicked to get the data attributes
            const button = event.target.closest('button');

            // Get data from button attributes
            const feeData = {
                id: button.getAttribute('data-fee-id'),
                name: button.getAttribute('data-fee-name'),
                amount: button.getAttribute('data-fee-amount'),
                description: button.getAttribute('data-fee-description'),
                isExamFee: button.getAttribute('data-is-exam-fee') == '1',
                targetClass: button.getAttribute('data-target-class'),
                paymentTerm: button.getAttribute('data-payment-term')
            };

            // Populate the modal fields
            document.getElementById('edit_fee_id').value = feeData.id;
            document.getElementById('edit_fee_name').value = feeData.name;
            document.getElementById('edit_fee_amount').value = feeData.amount;
            document.getElementById('edit_description').value = feeData.description;

            // Handle exam fee checkbox and fields
            const examFeeSection = document.getElementById('edit_exam_fee_section');
            const examFeeFields = document.getElementById('edit_exam_fee_fields');
            const examFeeCheckbox = document.getElementById('edit_is_exam_fee');

            // Check if this is a WAEC fee (Junior WAEC or Senior WAEC)
            const isWAECFee = feeData.name.toLowerCase().includes('waec') ||
                             feeData.name.toLowerCase().includes('junior waec') ||
                             feeData.name.toLowerCase().includes('senior waec');

            if (isWAECFee) {
                // Show WAEC section for actual WAEC fees
                examFeeSection.style.display = 'block';
                examFeeCheckbox.checked = feeData.isExamFee;

                if (feeData.isExamFee) {
                    examFeeFields.style.display = 'block';
                    document.getElementById('edit_target_class').value = feeData.targetClass;
                    document.getElementById('edit_payment_term').value = feeData.paymentTerm;
                } else {
                    examFeeFields.style.display = 'none';
                }
            } else {
                // Hide WAEC section for regular fees (School Fee, Development Levy, etc.)
                examFeeSection.style.display = 'none';
                examFeeFields.style.display = 'none';
                examFeeCheckbox.checked = false;
            }

            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('editFeeModal'));
            modal.show();
        }

        function deleteFeeType(feeTypeId) {
            if (confirm('Are you sure you want to delete this fee type? This action cannot be undone.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'delete_fee_type';

                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'fee_id';
                idInput.value = feeTypeId;

                form.appendChild(actionInput);
                form.appendChild(idInput);

                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
