-- Comprehensive Messaging System for IGSS Management System
-- This creates a proper message routing and inbox system

USE igss_management;

-- Main messages table
CREATE TABLE IF NOT EXISTS messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sender_id INT NOT NULL,
    sender_type <PERSON><PERSON><PERSON>('parent', 'teacher', 'admin') NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message_content TEXT NOT NULL,
    message_type ENUM('general', 'academic', 'disciplinary', 'fee_inquiry', 'attendance', 'emergency') DEFAULT 'general',
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
    ward_id INT NULL, -- For parent messages about specific ward
    parent_message_id INT NULL, -- For threading replies
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON>OR<PERSON><PERSON><PERSON>EY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    <PERSON>OREI<PERSON><PERSON> KEY (ward_id) REFERENCES students(id) ON DELETE SET NULL,
    FOREIGN KEY (parent_message_id) REFERENCES messages(id) ON DELETE CASCADE,
    INDEX idx_sender (sender_id, sender_type),
    INDEX idx_ward (ward_id),
    INDEX idx_created_at (created_at),
    INDEX idx_message_type (message_type),
    INDEX idx_priority (priority)
);

-- Message recipients table (for multiple recipients)
CREATE TABLE IF NOT EXISTS message_recipients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    message_id INT NOT NULL,
    recipient_id INT NOT NULL,
    recipient_type ENUM('teacher', 'admin', 'parent') NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    is_replied BOOLEAN DEFAULT FALSE,
    replied_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_message_recipient (message_id, recipient_id),
    INDEX idx_recipient (recipient_id, recipient_type),
    INDEX idx_message (message_id),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);

-- Message attachments table (for future file attachments)
CREATE TABLE IF NOT EXISTS message_attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    message_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    INDEX idx_message (message_id)
);

-- Message notifications table
CREATE TABLE IF NOT EXISTS message_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    message_id INT NOT NULL,
    recipient_id INT NOT NULL,
    notification_type ENUM('email', 'sms', 'in_app') NOT NULL,
    is_sent BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP NULL,
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_message (message_id),
    INDEX idx_recipient (recipient_id),
    INDEX idx_is_sent (is_sent)
);

-- Quick communication templates
CREATE TABLE IF NOT EXISTS message_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL,
    template_type ENUM('emergency', 'academic', 'fee_inquiry', 'attendance', 'general') NOT NULL,
    subject_template VARCHAR(255) NOT NULL,
    message_template TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_template_type (template_type),
    INDEX idx_is_active (is_active)
);

-- Insert default message templates
INSERT IGNORE INTO message_templates (template_name, template_type, subject_template, message_template, created_by) VALUES
('Emergency Contact', 'emergency', 'URGENT: Emergency Contact Required', 'Dear Teacher/Admin,\n\nThis is an urgent matter requiring immediate attention regarding my ward [WARD_NAME].\n\nPlease contact me as soon as possible.\n\nThank you.', 1),
('Academic Concern', 'academic', 'Academic Concern - [WARD_NAME]', 'Dear [RECIPIENT],\n\nI would like to discuss my ward''s academic performance in [SUBJECT]. I have some concerns that I believe need attention.\n\nPlease let me know when we can discuss this matter.\n\nThank you.', 1),
('Fee Payment Issue', 'fee_inquiry', 'Fee Payment Inquiry - [WARD_NAME]', 'Dear Admin,\n\nI have a question regarding the fee payment for my ward [WARD_NAME]. \n\nPlease provide clarification on the payment status and any outstanding amounts.\n\nThank you.', 1),
('Attendance Inquiry', 'attendance', 'Attendance Inquiry - [WARD_NAME]', 'Dear Form Teacher,\n\nI would like to inquire about my ward''s attendance record. Please provide an update on their attendance status.\n\nThank you.', 1),
('Parent-Teacher Meeting Request', 'general', 'Request for Parent-Teacher Meeting', 'Dear [RECIPIENT],\n\nI would like to request a meeting to discuss my ward''s progress and any areas that need attention.\n\nPlease let me know your available times.\n\nThank you.', 1);
