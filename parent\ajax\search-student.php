<?php
/**
 * AJAX Student Search for Ward Linking
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in and is a parent
if (!isLoggedIn() || $_SESSION['user_type'] !== 'parent') {
    echo json_encode(['success' => false, 'error' => 'Unauthorized access']);
    exit();
}

// Check if parent account is approved
if (!$_SESSION['is_approved']) {
    echo json_encode(['success' => false, 'error' => 'Account not approved']);
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get parent information
$stmt = $conn->prepare("SELECT id FROM parents WHERE user_id = ?");
$stmt->execute([$user_id]);
$parent = $stmt->fetch();

if (!$parent) {
    echo json_encode(['success' => false, 'error' => 'Parent record not found']);
    exit();
}

// Get admission number from request
$admission_number = trim($_POST['admission_number'] ?? '');

if (empty($admission_number)) {
    echo json_encode(['success' => false, 'error' => 'Admission number is required']);
    exit();
}

try {
    // Search for student by admission number or user code
    // The search should handle both formats: IGSS-YYYY-XXXX (user_code) and ADMYYYYXXXX (admission_number)
    $stmt = $conn->prepare("
        SELECT
            s.id as student_id,
            s.admission_number,
            s.admission_date,
            s.guardian_name,
            s.guardian_phone,
            s.guardian_email,
            s.is_boarding,
            u.first_name,
            u.last_name,
            u.middle_name,
            u.email,
            u.phone,
            u.date_of_birth,
            u.gender,
            u.profile_image,
            u.user_code,
            c.class_name,
            l.level_name,
            l.level_code
        FROM students s
        JOIN users u ON s.user_id = u.id
        JOIN classes c ON s.class_id = c.id
        JOIN levels l ON c.level_id = l.id
        WHERE (s.admission_number = ? OR u.user_code = ?)
        AND u.is_active = 1
        AND u.is_approved = 1
    ");
    $stmt->execute([$admission_number, $admission_number]);
    $student = $stmt->fetch();

    if (!$student) {
        // Try to find the student without approval requirement to provide better error message
        $stmt = $conn->prepare("
            SELECT
                s.id as student_id,
                s.admission_number,
                u.user_code,
                u.first_name,
                u.last_name,
                u.is_active,
                u.is_approved
            FROM students s
            JOIN users u ON s.user_id = u.id
            WHERE (s.admission_number = ? OR u.user_code = ?)
        ");
        $stmt->execute([$admission_number, $admission_number]);
        $found_student = $stmt->fetch();

        if ($found_student) {
            if (!$found_student['is_active']) {
                $error_msg = 'Student account is inactive. Please contact the school administration.';
            } elseif (!$found_student['is_approved']) {
                $error_msg = 'Student account is pending approval. Please wait for administrator approval or contact the school.';
            } else {
                $error_msg = 'Student found but cannot be linked at this time. Please contact the school administration.';
            }
        } else {
            $error_msg = 'Student not found with admission number: ' . htmlspecialchars($admission_number) . '. Please check the number and try again.';
        }

        echo json_encode([
            'success' => false,
            'error' => $error_msg
        ]);
        exit();
    }

    // Check if student is already linked to this parent
    $stmt = $conn->prepare("
        SELECT id FROM parent_student_relationships 
        WHERE parent_id = ? AND student_id = ?
    ");
    $stmt->execute([$parent['id'], $student['student_id']]);
    $existing_link = $stmt->fetch();

    if ($existing_link) {
        echo json_encode([
            'success' => false, 
            'error' => 'This student is already linked to your account'
        ]);
        exit();
    }

    // Check if student is already linked to another parent (optional check)
    $stmt = $conn->prepare("
        SELECT COUNT(*) as link_count 
        FROM parent_student_relationships 
        WHERE student_id = ?
    ");
    $stmt->execute([$student['student_id']]);
    $link_count = $stmt->fetchColumn();

    // Calculate age
    $age = '';
    if ($student['date_of_birth']) {
        $birthDate = new DateTime($student['date_of_birth']);
        $today = new DateTime();
        $age = $today->diff($birthDate)->y . ' years old';
    }

    // Format admission date
    $admission_date_formatted = '';
    if ($student['admission_date']) {
        $admission_date_formatted = date('F j, Y', strtotime($student['admission_date']));
    }

    // Prepare student information for response
    $student_info = [
        'student_id' => $student['student_id'],
        'admission_number' => $student['admission_number'],
        'user_code' => $student['user_code'],
        'full_name' => trim($student['first_name'] . ' ' . ($student['middle_name'] ? $student['middle_name'] . ' ' : '') . $student['last_name']),
        'first_name' => $student['first_name'],
        'middle_name' => $student['middle_name'],
        'last_name' => $student['last_name'],
        'class_name' => $student['class_name'],
        'level_name' => $student['level_name'],
        'level_code' => $student['level_code'],
        'gender' => $student['gender'],
        'age' => $age,
        'date_of_birth' => $student['date_of_birth'] ? date('F j, Y', strtotime($student['date_of_birth'])) : '',
        'admission_date' => $admission_date_formatted,
        'email' => $student['email'],
        'phone' => $student['phone'],
        'guardian_name' => $student['guardian_name'],
        'guardian_phone' => $student['guardian_phone'],
        'guardian_email' => $student['guardian_email'],
        'is_boarding' => $student['is_boarding'],
        'profile_image' => $student['profile_image'],
        'existing_links' => $link_count,
        'initials' => strtoupper(substr($student['first_name'], 0, 1) . substr($student['last_name'], 0, 1))
    ];

    echo json_encode([
        'success' => true,
        'student' => $student_info,
        'message' => 'Student found successfully'
    ]);

} catch (Exception $e) {
    error_log("Student search error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'error' => 'An error occurred while searching for the student. Please try again.'
    ]);
}
?>
