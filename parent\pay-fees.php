<?php
/**
 * Fee Payment Page for Parents
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a parent
if (!isLoggedIn() || $_SESSION['user_type'] !== 'parent') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];
$student_id = $_GET['student_id'] ?? 0;

// Get parent information
$stmt = $conn->prepare("
    SELECT p.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone
    FROM parents p
    JOIN users u ON p.user_id = u.id
    WHERE p.user_id = ?
");
$stmt->execute([$user_id]);
$parent = $stmt->fetch();

if (!$parent) {
    header('Location: ../login.php');
    exit();
}

// If student_id is provided, verify parent has access
if ($student_id) {
    $stmt = $conn->prepare("
        SELECT COUNT(*) FROM parent_student_relationships 
        WHERE parent_id = ? AND student_id = ?
    ");
    $stmt->execute([$parent['id'], $student_id]);
    $has_access = $stmt->fetchColumn() > 0;
    
    if (!$has_access) {
        header('Location: index.php');
        exit();
    }
}

// Get linked students
$linked_students = getParentStudents($parent['id']);

// Get selected student details if student_id is provided
$selected_student = null;
if ($student_id) {
    foreach ($linked_students as $student) {
        if ($student['id'] == $student_id) {
            $selected_student = $student;
            break;
        }
    }
}

// Get current academic session
$current_academic = getCurrentAcademicInfo();

// Get fee types
$stmt = $conn->prepare("
    SELECT * FROM fee_types 
    WHERE is_active = 1 
    ORDER BY fee_type_name
");
$stmt->execute();
$fee_types = $stmt->fetchAll();

// Calculate total fees
$total_amount = 0;
foreach ($fee_types as $fee) {
    $total_amount += $fee['amount'];
}

$message = '';
$error = '';

// Handle payment submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'submit_payment') {
    $student_id = sanitizeInput($_POST['student_id'] ?? '');
    $payment_method = sanitizeInput($_POST['payment_method'] ?? '');
    $depositor_name = sanitizeInput($_POST['depositor_name'] ?? '');
    $payment_date = sanitizeInput($_POST['payment_date'] ?? '');
    $amount_paid = floatval($_POST['amount_paid'] ?? 0);
    
    if (empty($student_id) || empty($payment_method) || empty($depositor_name) || 
        empty($payment_date) || $amount_paid <= 0) {
        $error = 'Please fill in all required fields.';
    } else {
        try {
            $conn->beginTransaction();
            
            // Handle file upload
            $receipt_path = '';
            if (isset($_FILES['receipt_image']) && $_FILES['receipt_image']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = '../uploads/receipts/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }
                
                $file_extension = pathinfo($_FILES['receipt_image']['name'], PATHINFO_EXTENSION);
                $file_name = 'receipt_' . time() . '_' . rand(1000, 9999) . '.' . $file_extension;
                $receipt_path = $upload_dir . $file_name;
                
                if (!move_uploaded_file($_FILES['receipt_image']['tmp_name'], $receipt_path)) {
                    throw new Exception('Failed to upload receipt image.');
                }
                
                $receipt_path = 'uploads/receipts/' . $file_name; // Store relative path
            }
            
            // Insert payment record
            $stmt = $conn->prepare("
                INSERT INTO fee_payments (student_id, session_id, term_id, amount, payment_method, 
                                        depositor_name, payment_date, receipt_image, uploaded_by, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')
            ");
            
            $stmt->execute([
                $student_id,
                $current_academic['current_session_id'] ?? 1,
                $current_academic['current_term_id'] ?? 1,
                $amount_paid,
                $payment_method,
                $depositor_name,
                $payment_date,
                $receipt_path,
                $parent['id']
            ]);
            
            $conn->commit();
            $message = 'Payment submitted successfully! Your payment is pending verification by the school administration.';
            
        } catch (Exception $e) {
            $conn->rollback();
            $error = 'Failed to submit payment: ' . $e->getMessage();
        }
    }
}

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fee Payment - <?php echo $school_info['name']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 50%, #ff4500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .payment-container {
            padding: 20px;
        }
        
        .payment-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .student-selector {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .fee-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .fee-item:hover {
            border-color: #ff8c00;
            box-shadow: 0 2px 8px rgba(255, 140, 0, 0.2);
        }
        
        .fee-amount {
            font-size: 1.2rem;
            font-weight: bold;
            color: #28a745;
        }
        
        .total-amount {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ff8c00;
        }
        
        .payment-method {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .payment-method:hover {
            border-color: #ff8c00;
        }
        
        .payment-method.selected {
            border-color: #ff8c00;
            background: rgba(255, 140, 0, 0.1);
        }
        
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .btn-orange {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-orange:hover {
            background: linear-gradient(135deg, #ff9500 0%, #ff7635 100%);
            color: white;
            transform: translateY(-1px);
        }
        
        .bank-details {
            background: rgba(255, 140, 0, 0.1);
            border: 1px solid rgba(255, 140, 0, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .student-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #ff8c00;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($parent['first_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="index.php"><i class="fas fa-home me-2"></i>Dashboard</a></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="payment-container">
        <div class="container">
            <div class="payment-card">
                <h2 class="mb-4">
                    <i class="fas fa-credit-card me-2"></i>
                    Fee Payment
                </h2>
                
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (empty($linked_students)): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        You don't have any linked students. Please link a student first to make payments.
                        <a href="index.php" class="btn btn-primary btn-sm ms-2">
                            <i class="fas fa-link me-1"></i>Link Student
                        </a>
                    </div>
                <?php else: ?>
                    <!-- Student Selection -->
                    <?php if (!$selected_student): ?>
                        <div class="student-selector">
                            <h5 class="mb-3">
                                <i class="fas fa-user-graduate me-2"></i>
                                Select Student
                            </h5>
                            <div class="row">
                                <?php foreach ($linked_students as $student): ?>
                                    <div class="col-md-6 mb-2">
                                        <a href="?student_id=<?php echo $student['id']; ?>" class="text-decoration-none text-white">
                                            <div class="border border-light rounded p-3 hover-effect">
                                                <strong><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></strong>
                                                <br>
                                                <small><?php echo htmlspecialchars($student['class_name']); ?> - <?php echo htmlspecialchars($student['user_code']); ?></small>
                                            </div>
                                        </a>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- Selected Student Info -->
                        <div class="student-selector">
                            <h5 class="mb-2">
                                <i class="fas fa-user-graduate me-2"></i>
                                Payment for: <?php echo htmlspecialchars($selected_student['first_name'] . ' ' . $selected_student['last_name']); ?>
                            </h5>
                            <p class="mb-0">
                                <small>
                                    <?php echo htmlspecialchars($selected_student['class_name']); ?> - 
                                    <span class="student-code"><?php echo htmlspecialchars($selected_student['user_code']); ?></span>
                                </small>
                            </p>
                        </div>

                        <!-- Fee Structure -->
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-list me-2"></i>
                                Fee Structure
                            </h5>
                            
                            <?php foreach ($fee_types as $fee): ?>
                                <div class="fee-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($fee['fee_type_name']); ?></h6>
                                            <?php if ($fee['description']): ?>
                                                <small class="text-muted"><?php echo htmlspecialchars($fee['description']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                        <div class="fee-amount">₦<?php echo number_format($fee['amount'], 2); ?></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                            
                            <hr>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <h5>Total Amount</h5>
                                <div class="total-amount">₦<?php echo number_format($total_amount, 2); ?></div>
                            </div>
                        </div>

                        <!-- Bank Details -->
                        <div class="bank-details">
                            <h6 class="mb-3">
                                <i class="fas fa-university me-2"></i>
                                School Bank Account Details
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Bank Name:</strong> First Bank Nigeria Limited</p>
                                    <p><strong>Account Name:</strong> Intellectual Giant Secondary School</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Account Number:</strong> **********</p>
                                    <p><strong>Sort Code:</strong> *********</p>
                                </div>
                            </div>
                            <div class="alert alert-warning mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Important:</strong> Use your child's name or student code as payment reference.
                            </div>
                        </div>

                        <!-- Payment Form -->
                        <form method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="submit_payment">
                            <input type="hidden" name="student_id" value="<?php echo $selected_student['id']; ?>">
                            
                            <h5 class="mb-3">
                                <i class="fas fa-upload me-2"></i>
                                Upload Payment Details
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Payment Method *</label>
                                        <select name="payment_method" class="form-select" required>
                                            <option value="">Select Payment Method</option>
                                            <option value="Bank Transfer">Bank Transfer</option>
                                            <option value="Bank Deposit">Bank Deposit</option>
                                            <option value="Online Transfer">Online Transfer</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Amount Paid (₦) *</label>
                                        <input type="number" name="amount_paid" class="form-control" 
                                               step="0.01" min="0" value="<?php echo $total_amount; ?>" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Depositor Name *</label>
                                        <input type="text" name="depositor_name" class="form-control" 
                                               placeholder="Name of person who made the payment" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Payment Date *</label>
                                        <input type="date" name="payment_date" class="form-control" 
                                               max="<?php echo date('Y-m-d'); ?>" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Receipt/Teller Image *</label>
                                <input type="file" name="receipt_image" class="form-control" 
                                       accept="image/*" required>
                                <div class="form-text">Upload a clear image of your payment receipt or bank teller</div>
                            </div>
                            
                            <button type="submit" class="btn btn-orange btn-lg w-100">
                                <i class="fas fa-upload me-2"></i>
                                Submit Payment Details
                            </button>
                        </form>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
