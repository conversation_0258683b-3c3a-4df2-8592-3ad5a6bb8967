# 🚀 IGSS Management System - Simple Deployment

## ✅ Super Simple 3-Step Process

### **Step 1: Import Database**
1. Copy all files to your web server (e.g., `C:\xampp\htdocs\IGSS`)
2. Start MySQL/MariaDB service
3. Open browser: `http://localhost/IGSS/importdb.php`
4. Click **"Import Database"** button
5. Wait for success message

### **Step 2: Set Admin Password**
1. Click **"Set Admin Password"** button (auto-redirects)
2. Fill in the form:
   - **First Name:** Your first name
   - **Last Name:** Your last name
   - **Email:** Your admin email
   - **Password:** Choose any password you want
   - **Confirm Password:** Same password
3. Click **"Save Admin Password"**

### **Step 3: Login**
1. Go to: `http://localhost/IGSS/login.php`
2. Select **"Administrator"** role
3. Login with:
   - **User Code:** `ADMIN001`
   - **Password:** [your chosen password]

## 🎯 That's It!

Your admin account is now set up with whatever password you chose. The system will save it to the database and you can login immediately.

---

## 🔧 If Something Goes Wrong

### **Problem: "Failed to update admin password"**

**Quick Fix:**
1. Go to: `http://localhost/IGSS/test-simple-setup.php`
2. Click **"Simulate Password Setup"** 
3. This sets password to `mypassword123` for testing
4. Login with: `ADMIN001` / `mypassword123`

### **Problem: Database connection error**

**Check:**
- MySQL/MariaDB is running
- Database settings in `config/database.php`:
  ```php
  DB_HOST = 'localhost'
  DB_NAME = 'igss_management'
  DB_USER = 'root'
  DB_PASS = '' // or your MySQL password
  ```

### **Problem: Page not loading**

**Solutions:**
- Clear browser cache
- Check if web server (Apache) is running
- Verify PHP is working: create `test.php` with `<?php phpinfo(); ?>`

---

## 🔒 Security (After Setup)

**Delete these files for security:**
- `importdb.php`
- `admin-setup.php` 
- `test-simple-setup.php`
- `verify-setup.php`

---

## 📋 What Happens Behind the Scenes

1. **importdb.php** creates database and admin user with default password `setup123`
2. **admin-setup.php** updates the admin user with your chosen password
3. **login.php** authenticates you with your password

The process is designed to be foolproof - the database will always accommodate whatever password you choose to set.

---

## ✅ Success Indicators

You know it worked when:
- [x] Database import shows "✅ Database Import Successful!"
- [x] Admin setup shows "Admin password set successfully!"
- [x] You can login with `ADMIN001` and your password
- [x] Admin dashboard loads properly

---

## 🆘 Emergency Reset

If you forget your password or something breaks:

1. Go to: `http://localhost/IGSS/direct-reset.php`
2. Enter:
   - **Admin Code:** `ADMIN001`
   - **New Password:** [new password]
   - **Confirm:** [same password]
3. Click "Reset Password"

This will force-reset your admin password to whatever you choose.

---

**🎉 Enjoy your IGSS Management System!**
