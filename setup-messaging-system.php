<?php
/**
 * Setup Messaging System Database Tables
 * Run this script to create the messaging system tables
 */

require_once 'config/config.php';

try {
    $conn = getDBConnection();
    
    echo "🚀 Setting up IGSS Messaging System...\n\n";
    
    // Read and execute the messaging system SQL
    $sql_file = 'database/messaging_system.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("SQL file not found: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql_content)));
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^(--|\/\*|USE)/', $statement)) {
            try {
                $conn->exec($statement);
                echo "✅ Executed SQL statement successfully\n";
            } catch (Exception $e) {
                echo "⚠️  SQL Warning: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\n📋 Checking created tables...\n";
    
    // Check if tables were created
    $tables_to_check = [
        'messages',
        'message_recipients', 
        'message_attachments',
        'message_notifications',
        'message_templates'
    ];
    
    foreach ($tables_to_check as $table) {
        $stmt = $conn->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Table '$table' created successfully\n";
        } else {
            echo "❌ Table '$table' not found\n";
        }
    }
    
    // Check if message templates were inserted
    $stmt = $conn->query("SELECT COUNT(*) FROM message_templates");
    $template_count = $stmt->fetchColumn();
    echo "\n📝 Message templates: $template_count templates available\n";
    
    echo "\n🎉 Messaging system setup completed successfully!\n";
    echo "\n📌 Next steps:\n";
    echo "   1. Test parent messaging at: parent/messages.php\n";
    echo "   2. Check teacher inbox at: teacher/messages.php\n";
    echo "   3. Review admin messages at: admin/messages.php\n";
    echo "\n💡 Features enabled:\n";
    echo "   ✅ Message routing to form teachers\n";
    echo "   ✅ Admin message handling\n";
    echo "   ✅ Message threading and replies\n";
    echo "   ✅ Priority levels and categories\n";
    echo "   ✅ Notification system\n";
    echo "   ✅ Quick message templates\n";
    
} catch (Exception $e) {
    echo "❌ Error setting up messaging system: " . $e->getMessage() . "\n";
    exit(1);
}
?>
