<?php
/**
 * Fee Confirmations Page for IGSS Management System
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/fee-notification-functions.php';

// Check if user is logged in and is admin
requireLogin();
if (!hasRole('admin')) {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $payment_id = $_POST['payment_id'] ?? '';

    if ($action === 'confirm' && $payment_id) {
        $admin_remarks = sanitizeInput($_POST['admin_remarks'] ?? '');

        try {
            $conn->beginTransaction();

            $stmt = $conn->prepare("
                UPDATE fee_payments
                SET status = 'Confirmed', confirmed_by = ?, confirmation_date = NOW(), admin_remarks = ?
                WHERE id = ? AND status = 'Pending'
            ");
            $stmt->execute([$_SESSION['user_id'], $admin_remarks, $payment_id]);

            if ($stmt->rowCount() > 0) {
                // Send notifications to parent, student, and form teacher
                $notification_result = sendPaymentConfirmationNotification($payment_id, 'confirmed', $admin_remarks);

                $conn->commit();

                if ($notification_result) {
                    $message = 'Payment confirmed successfully and notifications sent to all stakeholders.';
                } else {
                    $message = 'Payment confirmed successfully, but some notifications may have failed.';
                }
            } else {
                $conn->rollback();
                $error = 'Payment not found or already processed.';
            }
        } catch (Exception $e) {
            $conn->rollback();
            $error = 'Failed to confirm payment: ' . $e->getMessage();
        }
    } elseif ($action === 'reject' && $payment_id) {
        $rejection_reason = sanitizeInput($_POST['rejection_reason'] ?? '');

        try {
            $conn->beginTransaction();

            $stmt = $conn->prepare("
                UPDATE fee_payments
                SET status = 'Rejected', confirmed_by = ?, confirmation_date = NOW(), admin_remarks = ?
                WHERE id = ? AND status = 'Pending'
            ");
            $stmt->execute([$_SESSION['user_id'], $rejection_reason, $payment_id]);

            if ($stmt->rowCount() > 0) {
                // Send notifications to parent, student, and form teacher
                $notification_result = sendPaymentConfirmationNotification($payment_id, 'rejected', $rejection_reason);

                $conn->commit();

                if ($notification_result) {
                    $message = 'Payment rejected successfully and notifications sent to all stakeholders.';
                } else {
                    $message = 'Payment rejected successfully, but some notifications may have failed.';
                }
            } else {
                $conn->rollback();
                $error = 'Payment not found or already processed.';
            }
        } catch (Exception $e) {
            $conn->rollback();
            $error = 'Failed to reject payment: ' . $e->getMessage();
        }
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? 'Pending';
$session_filter = $_GET['session'] ?? '';
$search = $_GET['search'] ?? '';

// Build query
$where_conditions = [];
$params = [];

if ($status_filter) {
    $where_conditions[] = "fp.status = ?";
    $params[] = $status_filter;
}

if ($session_filter) {
    $where_conditions[] = "acs.session_name = ?";
    $params[] = $session_filter;
} else {
    // Default to current session
    $current_academic = getCurrentAcademicInfo();
    $where_conditions[] = "acs.session_name = ?";
    $params[] = $current_academic['session'];
}

if ($search) {
    $where_conditions[] = "(u.first_name LIKE ? OR u.last_name LIKE ? OR s.admission_number LIKE ? OR fp.reference_number LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get fee payments
$stmt = $conn->prepare("
    SELECT fp.*,
           u.first_name, u.last_name,
           s.admission_number,
           c.class_name,
           l.level_name,
           acs.session_name,
           CONCAT(admin.first_name, ' ', admin.last_name) as confirmed_by_name
    FROM fee_payments fp
    JOIN students s ON fp.student_id = s.id
    JOIN users u ON s.user_id = u.id
    JOIN classes c ON s.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    JOIN academic_sessions acs ON fp.session_id = acs.id
    LEFT JOIN users admin ON fp.confirmed_by = admin.id
    $where_clause
    ORDER BY fp.created_at DESC
");
$stmt->execute($params);
$payments = $stmt->fetchAll();

// Get available sessions
$stmt = $conn->prepare("SELECT DISTINCT session_name FROM academic_sessions ORDER BY session_name DESC");
$stmt->execute();
$sessions = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Get payment statistics
$stats = [];
$current_academic = getCurrentAcademicInfo();

$stmt = $conn->prepare("
    SELECT status, COUNT(*) as count, SUM(amount_paid) as total_amount
    FROM fee_payments fp
    JOIN academic_sessions acs ON fp.session_id = acs.id
    WHERE acs.session_name = ?
    GROUP BY status
");
$stmt->execute([$current_academic['session']]);
$payment_stats = $stmt->fetchAll();

foreach ($payment_stats as $stat) {
    $stats[$stat['status']] = [
        'count' => $stat['count'],
        'amount' => $stat['total_amount']
    ];
}

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fee Confirmations - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }

        .sidebar {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        /* Custom white scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.8);
        }

        /* For Firefox */
        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.6) rgba(255, 255, 255, 0.2);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 80px;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.25) 100%);
            border-radius: 60px 0 0 60px;
        }

        .sidebar-header {
            padding: 25px 20px; /* Reduced padding */
            text-align: center;
            position: relative;
            background: rgba(255, 255, 255, 0.01); /* Minimal opacity */
            border-radius: 0 0 30px 30px;
            margin-bottom: 15px; /* Reduced margin */
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 700;
            font-size: 1.4rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header small {
            opacity: 0.9;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            margin-bottom: 80px; /* Space for back to home button */
        }

        .sidebar-menu li {
            margin: 1px 15px; /* Minimal margin for tight fit */
        }

        .sidebar-menu a {
            color: white;
            text-decoration: none;
            padding: 8px 20px; /* Smaller padding like other pages */
            display: block;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 25px;
            position: relative;
            font-weight: 500; /* Normal font weight */
            font-size: 0.85rem; /* Smaller font size */
            background: rgba(255, 255, 255, 0.05); /* Very subtle background */
            margin-bottom: 1px; /* Minimal spacing */
        }

        .sidebar-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover::before, .sidebar-menu a.active::before {
            opacity: 1;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            transform: translateX(8px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.15);
        }

        .sidebar-menu a.active {
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .back-to-home {
            position: absolute;
            bottom: 60px; /* Moved higher up */
            left: 15px;
            right: 15px;
            z-index: 1001;
        }

        .back-to-home .btn {
            background: rgba(255, 255, 255, 0.3); /* Even more visible white background */
            border: 3px solid rgba(255, 255, 255, 0.6); /* Thicker, more visible border */
            color: white;
            font-weight: 800;
            padding: 12px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            width: 100%;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            font-size: 0.95rem;
        }

        .back-to-home .btn:hover {
            background: rgba(255, 255, 255, 0.3); /* More visible hover effect */
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
            position: relative;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px 30px;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff9a56, #ff6b35, #f7931e);
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 25px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff9a56, #ff6b35, #f7931e);
        }

        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            border-left: 4px solid;
        }

        .stats-card.pending { border-left-color: #ffc107; }
        .stats-card.confirmed { border-left-color: #28a745; }
        .stats-card.rejected { border-left-color: #dc3545; }

        .payment-receipt {
            max-width: 200px;
            max-height: 150px;
            object-fit: cover;
            border-radius: 5px;
            cursor: pointer;
        }

        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        .action-buttons .btn {
            margin: 2px;
            padding: 5px 10px;
            font-size: 0.8rem;
        }

        .amount-display {
            font-weight: bold;
            color: #28a745;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-graduation-cap me-2"></i>IGSS Admin</h4>
            <small>Management System</small>
        </div>

        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
            <li><a href="manage-users.php"><i class="fas fa-users me-2"></i>Manage Users</a></li>
            <li><a href="academic-settings.php"><i class="fas fa-calendar-alt me-2"></i>Academic Settings</a></li>
            <li><a href="assign-subjects.php"><i class="fas fa-book me-2"></i>Assign Subjects</a></li>
            <li><a href="assign-form-teachers.php"><i class="fas fa-chalkboard-teacher me-2"></i>Form Teachers</a></li>
            <li><a href="manage-fees.php"><i class="fas fa-money-bill me-2"></i>Manage Fees</a></li>
            <li><a href="fee-confirmations.php" class="active"><i class="fas fa-money-check-alt me-2"></i>Fee Confirmations</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar me-2"></i>Reports</a></li>
            <li><a href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
            <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
        </ul>

        <!-- Back to Home Button -->
        <div class="back-to-home">
            <a href="../index.php" class="btn">
                <i class="fas fa-home me-2"></i>Back to Home
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h4 class="mb-0"><i class="fas fa-money-check-alt me-2"></i>Fee Payment Confirmations</h4>
            <small class="text-muted">Review and confirm student fee payments</small>
        </div>

        <!-- Alerts -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stats-card pending">
                    <h3 class="text-warning"><?php echo $stats['Pending']['count'] ?? 0; ?></h3>
                    <p class="mb-1">Pending Payments</p>
                    <small class="text-muted">₦<?php echo number_format($stats['Pending']['amount'] ?? 0, 2); ?></small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card confirmed">
                    <h3 class="text-success"><?php echo $stats['Confirmed']['count'] ?? 0; ?></h3>
                    <p class="mb-1">Confirmed Payments</p>
                    <small class="text-muted">₦<?php echo number_format($stats['Confirmed']['amount'] ?? 0, 2); ?></small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card rejected">
                    <h3 class="text-danger"><?php echo $stats['Rejected']['count'] ?? 0; ?></h3>
                    <p class="mb-1">Rejected Payments</p>
                    <small class="text-muted">₦<?php echo number_format($stats['Rejected']['amount'] ?? 0, 2); ?></small>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="content-card mb-4">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Status</label>
                    <select name="status" class="form-select">
                        <option value="Pending" <?php echo $status_filter === 'Pending' ? 'selected' : ''; ?>>Pending</option>
                        <option value="Confirmed" <?php echo $status_filter === 'Confirmed' ? 'selected' : ''; ?>>Confirmed</option>
                        <option value="Rejected" <?php echo $status_filter === 'Rejected' ? 'selected' : ''; ?>>Rejected</option>
                        <option value="" <?php echo $status_filter === '' ? 'selected' : ''; ?>>All Status</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Session</label>
                    <select name="session" class="form-select">
                        <option value="">Current Session</option>
                        <?php foreach ($sessions as $session): ?>
                            <option value="<?php echo $session; ?>" <?php echo $session_filter === $session ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($session); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Search</label>
                    <input type="text" name="search" class="form-control" placeholder="Search by name, admission number, or reference" value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>Filter
                    </button>
                </div>
            </form>
        </div>

        <!-- Payments Table -->
        <div class="content-card">
            <div class="table-responsive">
                <table class="table table-hover" id="paymentsTable">
                    <thead>
                        <tr>
                            <th>Student</th>
                            <th>Fee Type</th>
                            <th>Amount</th>
                            <th>Reference</th>
                            <th>Receipt</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($payments as $payment): ?>
                            <tr>
                                <td>
                                    <div>
                                        <div class="fw-bold"><?php echo htmlspecialchars($payment['first_name'] . ' ' . $payment['last_name']); ?></div>
                                        <small class="text-muted">
                                            <?php echo htmlspecialchars($payment['admission_number']); ?> -
                                            <?php echo htmlspecialchars($payment['class_name']); ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo htmlspecialchars($payment['fee_type_name']); ?></span>
                                </td>
                                <td>
                                    <span class="amount-display">₦<?php echo number_format($payment['amount'], 2); ?></span>
                                </td>
                                <td>
                                    <code><?php echo htmlspecialchars($payment['reference_number']); ?></code>
                                </td>
                                <td>
                                    <?php if ($payment['receipt_image']): ?>
                                        <img src="../<?php echo htmlspecialchars($payment['receipt_image']); ?>"
                                             class="payment-receipt"
                                             onclick="viewReceipt('<?php echo htmlspecialchars($payment['receipt_image']); ?>')"
                                             alt="Payment Receipt">
                                    <?php else: ?>
                                        <span class="text-muted">No receipt</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($payment['status'] === 'Pending'): ?>
                                        <span class="badge bg-warning status-badge">Pending</span>
                                    <?php elseif ($payment['status'] === 'Confirmed'): ?>
                                        <span class="badge bg-success status-badge">Confirmed</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger status-badge">Rejected</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?php echo date('M j, Y g:i A', strtotime($payment['created_at'])); ?></small>
                                    <?php if ($payment['confirmation_date']): ?>
                                        <br><small class="text-muted">
                                            Processed: <?php echo date('M j, Y', strtotime($payment['confirmation_date'])); ?>
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <?php if ($payment['status'] === 'Pending'): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="confirm">
                                                <input type="hidden" name="payment_id" value="<?php echo $payment['id']; ?>">
                                                <button type="submit" class="btn btn-success btn-sm" onclick="return confirm('Confirm this payment?')">
                                                    <i class="fas fa-check"></i> Confirm
                                                </button>
                                            </form>
                                            <button class="btn btn-danger btn-sm" onclick="rejectPayment(<?php echo $payment['id']; ?>)">
                                                <i class="fas fa-times"></i> Reject
                                            </button>
                                        <?php else: ?>
                                            <span class="text-muted">
                                                <?php if ($payment['confirmed_by_name']): ?>
                                                    By: <?php echo htmlspecialchars($payment['confirmed_by_name']); ?>
                                                <?php endif; ?>
                                            </span>
                                        <?php endif; ?>
                                        <button class="btn btn-info btn-sm" onclick="viewPaymentDetails(<?php echo $payment['id']; ?>)">
                                            <i class="fas fa-eye"></i> View
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Rejection Modal -->
    <div class="modal fade" id="rejectionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Reject Payment</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="rejectionForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="reject">
                        <input type="hidden" name="payment_id" id="rejectPaymentId">
                        <div class="mb-3">
                            <label class="form-label">Reason for Rejection</label>
                            <textarea name="rejection_reason" class="form-control" rows="3" required
                                      placeholder="Please provide a reason for rejecting this payment..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Reject Payment</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Receipt Modal -->
    <div class="modal fade" id="receiptModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Payment Receipt</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="receiptImage" src="" class="img-fluid" alt="Payment Receipt">
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#paymentsTable').DataTable({
                "pageLength": 25,
                "order": [[ 6, "desc" ]],
                "columnDefs": [
                    { "orderable": false, "targets": [4, 7] }
                ]
            });
        });

        function rejectPayment(paymentId) {
            document.getElementById('rejectPaymentId').value = paymentId;
            new bootstrap.Modal(document.getElementById('rejectionModal')).show();
        }

        function viewReceipt(receiptPath) {
            document.getElementById('receiptImage').src = '../' + receiptPath;
            new bootstrap.Modal(document.getElementById('receiptModal')).show();
        }

        function viewPaymentDetails(paymentId) {
            alert('View payment details for ID: ' + paymentId);
        }
    </script>
</body>
</html>
