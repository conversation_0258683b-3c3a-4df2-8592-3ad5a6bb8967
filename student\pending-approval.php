<?php
/**
 * Student Pending Approval Page
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a student
if (!isLoggedIn() || $_SESSION['user_type'] !== 'student') {
    header('Location: ../login.php');
    exit();
}

// If already approved, redirect to dashboard
if ($_SESSION['is_approved']) {
    header('Location: index.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get student information
$stmt = $conn->prepare("
    SELECT s.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, 
           u.date_of_birth, u.gender, u.user_code, u.created_at,
           c.class_name, l.level_name
    FROM students s
    JOIN users u ON s.user_id = u.id
    JOIN classes c ON s.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    WHERE s.user_id = ?
");
$stmt->execute([$user_id]);
$student = $stmt->fetch();

if (!$student) {
    header('Location: ../login.php');
    exit();
}

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Pending Approval - <?php echo htmlspecialchars($school_info['name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .pending-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            margin: 50px auto;
            max-width: 600px;
            overflow: hidden;
        }
        
        .pending-header {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .pending-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        
        .pending-body {
            padding: 40px 30px;
        }
        
        .info-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
        }
        
        .info-value {
            color: #6c757d;
        }
        
        .status-badge {
            background: #ffc107;
            color: #212529;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            display: inline-block;
        }
        
        .btn-refresh {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-refresh:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
            color: white;
        }
        
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -23px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
        }
        
        .timeline-item.pending::before {
            background: #ffc107;
        }
        
        .timeline-item.future::before {
            background: #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="pending-container">
            <div class="pending-header">
                <div class="pending-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h2>Account Pending Approval</h2>
                <p class="mb-0">Your student account is awaiting administrator approval</p>
            </div>
            
            <div class="pending-body">
                <div class="text-center mb-4">
                    <span class="status-badge">
                        <i class="fas fa-hourglass-half me-2"></i>Pending Review
                    </span>
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>What's Next?</strong><br>
                    Your registration has been received and is currently being reviewed by the school administration. 
                    You will receive access to your student portal once your account is approved.
                </div>
                
                <div class="info-card">
                    <h5 class="mb-3"><i class="fas fa-user me-2"></i>Your Information</h5>
                    <div class="info-row">
                        <span class="info-label">Full Name:</span>
                        <span class="info-value"><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Student ID:</span>
                        <span class="info-value"><?php echo htmlspecialchars($student['user_code']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Admission Number:</span>
                        <span class="info-value"><?php echo htmlspecialchars($student['admission_number']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Class:</span>
                        <span class="info-value"><?php echo htmlspecialchars($student['class_name']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Email:</span>
                        <span class="info-value"><?php echo htmlspecialchars($student['email']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Registration Date:</span>
                        <span class="info-value"><?php echo date('F j, Y', strtotime($student['created_at'])); ?></span>
                    </div>
                </div>
                
                <div class="info-card">
                    <h5 class="mb-3"><i class="fas fa-list-check me-2"></i>Approval Process</h5>
                    <div class="timeline">
                        <div class="timeline-item">
                            <strong>Registration Submitted</strong>
                            <div class="text-muted small">Your account has been created successfully</div>
                        </div>
                        <div class="timeline-item pending">
                            <strong>Under Review</strong>
                            <div class="text-muted small">Administrator is reviewing your information</div>
                        </div>
                        <div class="timeline-item future">
                            <strong>Account Activation</strong>
                            <div class="text-muted small">You'll receive access to your student portal</div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <button onclick="checkApprovalStatus()" class="btn btn-refresh me-3">
                        <i class="fas fa-sync-alt me-2"></i>Check Status
                    </button>
                    <a href="../logout.php" class="btn btn-outline-secondary">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </div>
                
                <div class="alert alert-warning mt-4">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Need Help?</strong><br>
                    If you have questions about your account status, please contact the school administration at 
                    <strong><?php echo htmlspecialchars($school_info['phone']); ?></strong> or 
                    <strong><?php echo htmlspecialchars($school_info['email']); ?></strong>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function checkApprovalStatus() {
            const btn = document.querySelector('.btn-refresh');
            const originalText = btn.innerHTML;
            
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Checking...';
            btn.disabled = true;
            
            fetch('check-approval-status.php')
                .then(response => response.json())
                .then(data => {
                    if (data.approved) {
                        window.location.href = 'index.php';
                    } else {
                        setTimeout(() => {
                            btn.innerHTML = originalText;
                            btn.disabled = false;
                        }, 1000);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }, 1000);
                });
        }
        
        // Auto-check status every 30 seconds
        setInterval(checkApprovalStatus, 30000);
    </script>
</body>
</html>
