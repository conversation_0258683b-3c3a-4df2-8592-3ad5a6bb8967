/**
 * Teacher Dashboard Enhancements
 * Additional CSS for interactive elements and animations
 */

/* Ripple Effect Animation */
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Pulse Animation for Important Elements */
@keyframes pulse {
    0% {
        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    }
    50% {
        box-shadow: 0 4px 20px rgba(255, 193, 7, 0.5);
    }
    100% {
        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    }
}

/* Bounce Animation for Hover Effects */
@keyframes bounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    80% {
        transform: translateY(-5px);
    }
}

/* Glow Effect for Interactive Cards */
@keyframes glow {
    0% {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    50% {
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    }
    100% {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
}

/* Enhanced Card Interactions */
.interactive-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.interactive-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.interactive-card:active {
    transform: translateY(-4px) scale(1.01);
}

/* Button Enhancements */
.btn-enhanced {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-enhanced:active {
    transform: translateY(0);
}

/* Loading Spinner for Async Operations */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
    animation: pulse 2s infinite;
}

/* Success State Animations */
.success-animation {
    animation: bounce 0.6s ease-in-out;
}

/* Error State Animations */
.error-shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

/* Gradient Text Effects */
.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

/* Card Hover Glow Effect */
.card-glow:hover {
    animation: glow 2s ease-in-out infinite;
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: all 0.3s ease;
    z-index: 1000;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    color: white;
}

/* Progress Bar Enhancements */
.progress-enhanced {
    height: 8px;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.progress-enhanced .progress-bar {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* Tooltip Enhancements */
.tooltip-enhanced {
    position: relative;
    cursor: help;
}

.tooltip-enhanced::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.tooltip-enhanced:hover::after {
    opacity: 1;
}

/* Mobile Responsive Enhancements */
@media (max-width: 768px) {
    .interactive-card:hover {
        transform: translateY(-4px) scale(1.01);
    }
    
    .fab {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .tooltip-enhanced::after {
        background: rgba(255, 255, 255, 0.9);
        color: #333;
    }
}

/* Print Styles */
@media print {
    .interactive-card,
    .btn-enhanced,
    .fab {
        transform: none !important;
        box-shadow: none !important;
        animation: none !important;
    }
}

/* Accessibility Enhancements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Indicators */
.interactive-card:focus,
.btn-enhanced:focus {
    outline: 3px solid rgba(102, 126, 234, 0.5);
    outline-offset: 2px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
