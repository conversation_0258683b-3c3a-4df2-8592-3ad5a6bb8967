<?php
/**
 * Manage Users Page for IGSS Management System
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
requireLogin();
if (!hasRole('admin')) {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$message = '';
$error = '';

// Automatically update teacher codes to new format on page load
try {
    $stmt = $conn->prepare("SELECT id, user_code FROM users WHERE user_type = 'teacher' AND user_code NOT LIKE 'TEACHER_%'");
    $stmt->execute();
    $oldTeachers = $stmt->fetchAll();

    if (count($oldTeachers) > 0) {
        foreach ($oldTeachers as $teacher) {
            $newCode = generateTeacherCode();
            $updateStmt = $conn->prepare("UPDATE users SET user_code = ? WHERE id = ?");
            $updateStmt->execute([$newCode, $teacher['id']]);
        }
        $message = count($oldTeachers) . ' teacher code(s) automatically updated to new format.';
    }
} catch (Exception $e) {
    // Silent fail for auto-update
}

// Handle user actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $user_id = $_POST['user_id'] ?? '';

    if ($action === 'approve' && $user_id) {
        $stmt = $conn->prepare("UPDATE users SET is_approved = 1 WHERE id = ?");
        if ($stmt->execute([$user_id])) {
            $message = 'User approved successfully.';
        } else {
            $error = 'Failed to approve user.';
        }
    } elseif ($action === 'reject' && $user_id) {
        $stmt = $conn->prepare("UPDATE users SET is_approved = 0, is_active = 0 WHERE id = ?");
        if ($stmt->execute([$user_id])) {
            $message = 'User rejected successfully.';
        } else {
            $error = 'Failed to reject user.';
        }
    } elseif ($action === 'activate' && $user_id) {
        $stmt = $conn->prepare("UPDATE users SET is_active = 1 WHERE id = ?");
        if ($stmt->execute([$user_id])) {
            $message = 'User activated successfully.';
        } else {
            $error = 'Failed to activate user.';
        }
    } elseif ($action === 'deactivate' && $user_id) {
        $stmt = $conn->prepare("UPDATE users SET is_active = 0 WHERE id = ?");
        if ($stmt->execute([$user_id])) {
            $message = 'User deactivated successfully.';
        } else {
            $error = 'Failed to deactivate user.';
        }
    } elseif ($action === 'delete' && $user_id) {
        try {
            $conn->beginTransaction();

            // Get user info before deletion
            $stmt = $conn->prepare("SELECT user_type, first_name, last_name FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            $userInfo = $stmt->fetch();

            if ($userInfo) {
                // Delete related records based on user type
                if ($userInfo['user_type'] === 'teacher') {
                    // Delete teacher-specific records
                    $conn->prepare("DELETE FROM subject_assignments WHERE teacher_id = ?")->execute([$user_id]);
                    $conn->prepare("DELETE FROM class_assignments WHERE teacher_id = ?")->execute([$user_id]);
                    $conn->prepare("DELETE FROM form_teachers WHERE teacher_id = ?")->execute([$user_id]);
                    $conn->prepare("DELETE FROM teachers WHERE user_id = ?")->execute([$user_id]);
                } elseif ($userInfo['user_type'] === 'student') {
                    // Delete student-specific records
                    $conn->prepare("DELETE FROM student_grades WHERE student_id = ?")->execute([$user_id]);
                    $conn->prepare("DELETE FROM attendance WHERE student_id = ?")->execute([$user_id]);
                    $conn->prepare("DELETE FROM fee_payments WHERE student_id = ?")->execute([$user_id]);
                    $conn->prepare("DELETE FROM parent_student_relationships WHERE student_id = ?")->execute([$user_id]);
                    $conn->prepare("DELETE FROM students WHERE user_id = ?")->execute([$user_id]);
                } elseif ($userInfo['user_type'] === 'parent') {
                    // Delete parent-specific records
                    $conn->prepare("DELETE FROM parent_student_relationships WHERE parent_id = ?")->execute([$user_id]);
                    $conn->prepare("DELETE FROM parents WHERE user_id = ?")->execute([$user_id]);
                }

                // Delete security answers
                $conn->prepare("DELETE FROM user_security_answers WHERE user_id = ?")->execute([$user_id]);

                // Finally delete the user
                $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
                $stmt->execute([$user_id]);

                $conn->commit();
                $message = 'User "' . htmlspecialchars($userInfo['first_name'] . ' ' . $userInfo['last_name']) . '" deleted successfully.';
            } else {
                $error = 'User not found.';
            }
        } catch (Exception $e) {
            $conn->rollBack();
            $error = 'Failed to delete user: ' . $e->getMessage();
        }
    }
}

// Get filter parameters
$user_type_filter = $_GET['user_type'] ?? '';
$status_filter = $_GET['status'] ?? '';
$search = $_GET['search'] ?? '';

// Build query
$where_conditions = ["u.user_type != 'admin'"];
$params = [];

if ($user_type_filter) {
    $where_conditions[] = "u.user_type = ?";
    $params[] = $user_type_filter;
}

if ($status_filter === 'pending') {
    $where_conditions[] = "u.is_approved = 0";
} elseif ($status_filter === 'approved') {
    $where_conditions[] = "u.is_approved = 1";
} elseif ($status_filter === 'active') {
    $where_conditions[] = "u.is_active = 1";
} elseif ($status_filter === 'inactive') {
    $where_conditions[] = "u.is_active = 0";
}

if ($search) {
    $where_conditions[] = "(u.first_name LIKE ? OR u.last_name LIKE ? OR u.email LIKE ? OR u.user_code LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

$where_clause = implode(' AND ', $where_conditions);

// Get users
$stmt = $conn->prepare("
    SELECT u.*,
           t.employee_id, t.qualification,
           s.admission_number, s.class_id, c.class_name,
           p.relationship_to_student
    FROM users u
    LEFT JOIN teachers t ON u.id = t.user_id
    LEFT JOIN students s ON u.id = s.user_id
    LEFT JOIN classes c ON s.class_id = c.id
    LEFT JOIN parents p ON u.id = p.user_id
    WHERE $where_clause
    ORDER BY u.created_at DESC
");
$stmt->execute($params);
$users = $stmt->fetchAll();

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Users - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }

        .sidebar {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        /* Custom white scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.8);
        }

        /* For Firefox */
        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.6) rgba(255, 255, 255, 0.2);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 80px;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.25) 100%);
            border-radius: 60px 0 0 60px;
        }

        .sidebar-header {
            padding: 25px 20px; /* Reduced padding */
            text-align: center;
            position: relative;
            background: rgba(255, 255, 255, 0.01); /* Minimal opacity */
            border-radius: 0 0 30px 30px;
            margin-bottom: 15px; /* Reduced margin */
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 700;
            font-size: 1.4rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header small {
            opacity: 0.9;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            margin-bottom: 80px; /* Space for back to home button */
        }

        .sidebar-menu li {
            margin: 1px 15px; /* Minimal margin for tight fit */
        }

        .sidebar-menu a {
            color: white;
            text-decoration: none;
            padding: 8px 20px; /* Smaller padding like before */
            display: block;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 25px;
            position: relative;
            font-weight: 500; /* Normal font weight */
            font-size: 0.85rem; /* Smaller font size */
            background: rgba(255, 255, 255, 0.05); /* Very subtle background */
            border: none; /* Remove border */
            margin-bottom: 1px; /* Minimal spacing */
        }

        .sidebar-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover::before, .sidebar-menu a.active::before {
            opacity: 1;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            transform: translateX(5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .sidebar-menu a.active {
            background: rgba(255, 255, 255, 0.2);
        }

        .back-to-home {
            position: absolute;
            bottom: 60px; /* Moved higher up */
            left: 15px;
            right: 15px;
            z-index: 1001;
        }

        .back-to-home .btn {
            background: rgba(255, 255, 255, 0.3); /* Even more visible white background */
            border: 3px solid rgba(255, 255, 255, 0.6); /* Thicker, more visible border */
            color: white;
            font-weight: 800;
            padding: 12px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            width: 100%;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            font-size: 0.95rem;
        }

        .back-to-home .btn:hover {
            background: rgba(255, 255, 255, 0.3); /* More visible hover effect */
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
            position: relative;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px 30px;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff9a56, #ff6b35, #f7931e);
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 25px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff9a56, #ff6b35, #f7931e);
        }

        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .action-buttons .btn {
            margin: 2px;
            padding: 5px 10px;
            font-size: 0.8rem;
        }

        .action-buttons .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            transition: all 0.3s ease;
        }

        .action-buttons .btn-danger:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }

        .action-buttons {
            white-space: nowrap;
        }

        .action-buttons form {
            display: inline-block;
            margin: 0 1px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-graduation-cap me-2"></i>IGSS Admin</h4>
            <small>Management System</small>
        </div>

        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
            <li><a href="manage-users.php" class="active"><i class="fas fa-users me-2"></i>Manage Users</a></li>
            <li><a href="academic-settings.php"><i class="fas fa-calendar-alt me-2"></i>Academic Settings</a></li>
            <li><a href="assign-subjects.php"><i class="fas fa-book me-2"></i>Assign Subjects</a></li>
            <li><a href="assign-form-teachers.php"><i class="fas fa-chalkboard-teacher me-2"></i>Form Teachers</a></li>
            <li><a href="manage-fees.php"><i class="fas fa-money-bill me-2"></i>Manage Fees</a></li>
            <li><a href="fee-confirmations.php"><i class="fas fa-money-check-alt me-2"></i>Fee Confirmations</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar me-2"></i>Reports</a></li>
            <li><a href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
            <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
        </ul>

        <!-- Back to Home Button -->
        <div class="back-to-home">
            <a href="../index.php" class="btn">
                <i class="fas fa-home me-2"></i>Back to Home
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><i class="fas fa-users me-2"></i>Manage Users</h4>
                    <small class="text-muted">Manage all system users and their permissions</small>
                </div>
                <a href="../register.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add New User
                </a>
            </div>
        </div>

        <!-- Alerts -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Filters -->
        <div class="content-card mb-4">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">User Type</label>
                    <select name="user_type" class="form-select">
                        <option value="">All Types</option>
                        <option value="teacher" <?php echo $user_type_filter === 'teacher' ? 'selected' : ''; ?>>Teachers</option>
                        <option value="student" <?php echo $user_type_filter === 'student' ? 'selected' : ''; ?>>Students</option>
                        <option value="parent" <?php echo $user_type_filter === 'parent' ? 'selected' : ''; ?>>Parents</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Status</label>
                    <select name="status" class="form-select">
                        <option value="">All Status</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending Approval</option>
                        <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>Approved</option>
                        <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                        <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Search</label>
                    <input type="text" name="search" class="form-control" placeholder="Search by name, email, or user code" value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>Filter
                    </button>
                </div>
            </form>
        </div>

        <!-- Users Table -->
        <div class="content-card">
            <div class="table-responsive">
                <table class="table table-hover" id="usersTable">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Type</th>
                            <th>Contact</th>
                            <th>Status</th>
                            <th>Additional Info</th>
                            <th>Joined</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar me-3">
                                            <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                                            <small class="text-muted"><?php echo htmlspecialchars($user['user_code']); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo ucfirst($user['user_type']); ?></span>
                                </td>
                                <td>
                                    <div><?php echo htmlspecialchars($user['email']); ?></div>
                                    <small class="text-muted"><?php echo htmlspecialchars($user['phone'] ?? ''); ?></small>
                                </td>
                                <td>
                                    <?php if (!$user['is_approved']): ?>
                                        <span class="badge bg-warning status-badge">Pending Approval</span>
                                    <?php elseif ($user['is_active']): ?>
                                        <span class="badge bg-success status-badge">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger status-badge">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($user['user_type'] === 'teacher' && $user['employee_id']): ?>
                                        <small>ID: <?php echo htmlspecialchars($user['employee_id']); ?></small><br>
                                        <small><?php echo htmlspecialchars($user['qualification'] ?? ''); ?></small>
                                    <?php elseif ($user['user_type'] === 'student' && $user['admission_number']): ?>
                                        <small>Adm: <?php echo htmlspecialchars($user['admission_number']); ?></small><br>
                                        <small><?php echo htmlspecialchars($user['class_name'] ?? ''); ?></small>
                                    <?php elseif ($user['user_type'] === 'parent' && $user['relationship_to_student']): ?>
                                        <small><?php echo htmlspecialchars($user['relationship_to_student'] ?? ''); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?php echo date('M j, Y', strtotime($user['created_at'])); ?></small>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <?php if (!$user['is_approved']): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="approve">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <button type="submit" class="btn btn-success btn-sm" onclick="return confirm('Approve this user?')">
                                                    <i class="fas fa-check"></i> Approve
                                                </button>
                                            </form>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="reject">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Reject this user?')">
                                                    <i class="fas fa-times"></i> Reject
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <?php if ($user['is_active']): ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="deactivate">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" class="btn btn-warning btn-sm" onclick="return confirm('Deactivate this user?')">
                                                        <i class="fas fa-pause"></i> Deactivate
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="activate">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" class="btn btn-success btn-sm" onclick="return confirm('Activate this user?')">
                                                        <i class="fas fa-play"></i> Activate
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                        <button class="btn btn-info btn-sm" onclick="viewUser(<?php echo $user['id']; ?>)">
                                            <i class="fas fa-eye"></i> View
                                        </button>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone and will remove all associated data.')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#usersTable').DataTable({
                "pageLength": 25,
                "order": [[ 5, "desc" ]],
                "columnDefs": [
                    { "orderable": false, "targets": 6 }
                ]
            });
        });

        function viewUser(userId) {
            // Implement user view modal or redirect to user details page
            alert('View user details for ID: ' + userId);
        }
    </script>
</body>
</html>
