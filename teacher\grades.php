<?php
/**
 * Grades Management for Teachers
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a teacher
if (!isLoggedIn() || $_SESSION['user_type'] !== 'teacher') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get teacher information
$stmt = $conn->prepare("
    SELECT t.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, u.user_code
    FROM teachers t
    JOIN users u ON t.user_id = u.id
    WHERE t.user_id = ?
");
$stmt->execute([$user_id]);
$teacher = $stmt->fetch();

if (!$teacher) {
    header('Location: ../login.php');
    exit();
}

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();

// Get teacher's assigned subjects with classes
$stmt = $conn->prepare("
    SELECT
        s.id as subject_id,
        s.subject_name,
        s.subject_code,
        c.id as class_id,
        c.class_name,
        l.level_name,
        COUNT(DISTINCT st.id) as student_count
    FROM teacher_subjects ts
    JOIN subjects s ON ts.subject_id = s.id
    JOIN classes c ON ts.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    LEFT JOIN students st ON c.id = st.class_id AND st.current_session_id = ?
    WHERE ts.teacher_id = ? AND ts.is_active = 1
    GROUP BY s.id, s.subject_name, s.subject_code, c.id, c.class_name, l.level_name
    ORDER BY l.id, c.class_name, s.subject_name
");
$stmt->execute([1, $user_id]); // Using session_id = 1 for now
$subject_classes = $stmt->fetchAll();

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grades - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #ff7b00 0%, #ff9500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }

        .sidebar {
            background: linear-gradient(135deg, #ff7b00 0%, #ff9500 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        /* Custom white scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.8);
        }

        /* For Firefox */
        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.6) rgba(255, 255, 255, 0.2);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 80px;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.25) 100%);
            border-radius: 60px 0 0 60px;
        }

        .sidebar-header {
            padding: 25px 20px;
            text-align: center;
            position: relative;
            background: rgba(255, 255, 255, 0.01);
            border-radius: 0 0 30px 30px;
            margin-bottom: 15px;
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 700;
            font-size: 1.4rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header small {
            opacity: 0.9;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            margin-bottom: 80px;
        }

        .sidebar-menu li {
            margin: 1px 15px;
        }

        .sidebar-menu a {
            color: white;
            text-decoration: none;
            padding: 8px 20px;
            display: block;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 25px;
            position: relative;
            font-weight: 500;
            font-size: 0.85rem;
            background: rgba(255, 255, 255, 0.05);
            border: none;
            margin-bottom: 1px;
        }

        .sidebar-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover::before, .sidebar-menu a.active::before {
            opacity: 1;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            transform: translateX(5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .sidebar-menu a.active {
            background: rgba(255, 255, 255, 0.2);
        }

        .back-to-home {
            position: absolute;
            bottom: 60px;
            left: 15px;
            right: 15px;
            z-index: 1001;
        }

        .back-to-home .btn {
            background: rgba(255, 255, 255, 0.3);
            border: 3px solid rgba(255, 255, 255, 0.6);
            color: white;
            font-weight: 800;
            padding: 12px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            width: 100%;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            font-size: 0.95rem;
        }

        .back-to-home .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
            position: relative;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px 30px;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 25px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .subject-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .subject-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .subject-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .subject-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .subject-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: #2c3e50;
            margin: 0;
        }

        .subject-code {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .class-info {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .student-count {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
        }

        .btn-grades {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-grades:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            color: white;
        }

        .grade-scale {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .grade-scale h6 {
            color: #667eea;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .grade-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(102, 126, 234, 0.2);
        }

        .grade-item:last-child {
            border-bottom: none;
        }

        .grade-letter {
            font-weight: 700;
            color: #2c3e50;
        }

        .grade-range {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .coming-soon {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .coming-soon i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .coming-soon h5 {
            margin-bottom: 15px;
            color: #495057;
        }

        .coming-soon p {
            font-size: 0.9rem;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-chalkboard-teacher me-2"></i>IGSS Teacher</h4>
            <small>Management Portal</small>
        </div>

        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
            <li><a href="my-classes.php"><i class="fas fa-users me-2"></i>My Classes</a></li>
            <li><a href="my-subjects.php"><i class="fas fa-book me-2"></i>My Subjects</a></li>
            <li><a href="attendance.php"><i class="fas fa-calendar-check me-2"></i>Attendance</a></li>
            <li><a href="grades.php" class="active"><i class="fas fa-graduation-cap me-2"></i>Grades</a></li>
            <li><a href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
            <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
        </ul>

        <!-- Back to Home Button -->
        <div class="back-to-home">
            <a href="../index.php" class="btn">
                <i class="fas fa-home me-2"></i>Back to Home
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Grades Management</h4>
                    <small class="text-muted">Enter and manage student grades</small>
                </div>
                <div>
                    <span class="badge bg-primary me-2">
                        Session: <?php echo $current_academic['session']; ?>
                    </span>
                    <span class="badge bg-info">
                        Term: <?php echo TERMS[$current_academic['term']]; ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Grade Scale -->
        <div class="content-card">
            <div class="grade-scale">
                <h6><i class="fas fa-chart-bar me-2"></i>IGSS Grading Scale</h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="grade-item">
                            <span class="grade-letter">A</span>
                            <span class="grade-range">70 - 100</span>
                        </div>
                        <div class="grade-item">
                            <span class="grade-letter">B</span>
                            <span class="grade-range">60 - 69</span>
                        </div>
                        <div class="grade-item">
                            <span class="grade-letter">C</span>
                            <span class="grade-range">50 - 59</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="grade-item">
                            <span class="grade-letter">D</span>
                            <span class="grade-range">45 - 49</span>
                        </div>
                        <div class="grade-item">
                            <span class="grade-letter">E</span>
                            <span class="grade-range">40 - 44</span>
                        </div>
                        <div class="grade-item">
                            <span class="grade-letter">F</span>
                            <span class="grade-range">0 - 39</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subject Classes for Grades -->
        <div class="content-card">
            <h5 class="mb-4"><i class="fas fa-book me-2"></i>Select Subject & Class for Grading</h5>

            <?php if (count($subject_classes) > 0): ?>
                <div class="row">
                    <?php foreach ($subject_classes as $sc): ?>
                        <div class="col-lg-4 col-md-6">
                            <div class="subject-card">
                                <div class="subject-header">
                                    <div>
                                        <h6 class="subject-title"><?php echo htmlspecialchars($sc['subject_name']); ?></h6>
                                    </div>
                                    <div class="subject-code"><?php echo htmlspecialchars($sc['subject_code']); ?></div>
                                </div>

                                <div class="class-info">
                                    <i class="fas fa-users me-1"></i>
                                    <?php echo htmlspecialchars($sc['class_name']); ?> - <?php echo htmlspecialchars($sc['level_name']); ?>
                                </div>

                                <div class="student-count">
                                    <i class="fas fa-user-graduate me-1"></i>
                                    <?php echo $sc['student_count']; ?> Students
                                </div>

                                <button class="btn btn-grades" onclick="enterGrades(<?php echo $sc['subject_id']; ?>, <?php echo $sc['class_id']; ?>)">
                                    <i class="fas fa-graduation-cap me-2"></i>Enter Grades
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center">
                    <i class="fas fa-book fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Subjects Assigned</h5>
                    <p class="text-muted">You don't have any subjects assigned for grading.</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Coming Soon Section -->
        <div class="content-card">
            <div class="coming-soon">
                <i class="fas fa-graduation-cap"></i>
                <h5>Grading System Coming Soon</h5>
                <p>
                    The comprehensive grading system is currently under development.<br>
                    Features will include:
                </p>
                <ul class="list-unstyled mt-3">
                    <li><i class="fas fa-check text-success me-2"></i>Continuous Assessment (CA) entry</li>
                    <li><i class="fas fa-check text-success me-2"></i>Examination scores recording</li>
                    <li><i class="fas fa-check text-success me-2"></i>Automatic grade calculation</li>
                    <li><i class="fas fa-check text-success me-2"></i>Grade reports and analytics</li>
                    <li><i class="fas fa-check text-success me-2"></i>Student performance tracking</li>
                    <li><i class="fas fa-check text-success me-2"></i>Parent notification system</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function enterGrades(subjectId, classId) {
            // Enhanced grade entry function
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-graduation-cap text-success me-2"></i>Grade Entry System
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center mb-4">
                                <i class="fas fa-graduation-cap fa-3x text-success mb-3"></i>
                                <h6>Comprehensive Grading System</h6>
                                <p class="text-muted">
                                    The advanced grading system is currently under development.<br>
                                    This will include all the features you need for effective grade management.
                                </p>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card h-100">
                                        <div class="card-body text-center">
                                            <i class="fas fa-edit fa-2x text-primary mb-3"></i>
                                            <h6>Continuous Assessment</h6>
                                            <p class="text-muted small">Enter CA scores, assignments, and quizzes</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card h-100">
                                        <div class="card-body text-center">
                                            <i class="fas fa-clipboard-list fa-2x text-warning mb-3"></i>
                                            <h6>Examination Scores</h6>
                                            <p class="text-muted small">Record mid-term and final exam results</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card h-100">
                                        <div class="card-body text-center">
                                            <i class="fas fa-calculator fa-2x text-info mb-3"></i>
                                            <h6>Auto Calculation</h6>
                                            <p class="text-muted small">Automatic grade calculation and GPA</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-4">
                                    <div class="card h-100">
                                        <div class="card-body text-center">
                                            <i class="fas fa-chart-bar fa-2x text-success mb-3"></i>
                                            <h6>Performance Analytics</h6>
                                            <p class="text-muted small">Student performance tracking and trends</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card h-100">
                                        <div class="card-body text-center">
                                            <i class="fas fa-file-pdf fa-2x text-danger mb-3"></i>
                                            <h6>Report Generation</h6>
                                            <p class="text-muted small">Generate grade reports and transcripts</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card h-100">
                                        <div class="card-body text-center">
                                            <i class="fas fa-bell fa-2x text-warning mb-3"></i>
                                            <h6>Parent Notifications</h6>
                                            <p class="text-muted small">Automatic grade notifications to parents</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-success mt-4">
                                <i class="fas fa-rocket me-2"></i>
                                <strong>Coming Very Soon!</strong> The grading system is in final testing phase.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-success" onclick="alert('You will be notified when the grading system is ready!')">
                                <i class="fas fa-bell me-2"></i>Notify Me When Ready
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            // Remove modal from DOM after it's hidden
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        // Enhanced hover effects for subject cards
        document.addEventListener('DOMContentLoaded', function() {
            const subjectCards = document.querySelectorAll('.subject-card');

            subjectCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Add click animation to grade buttons
            const gradeButtons = document.querySelectorAll('.btn-grades');
            gradeButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
