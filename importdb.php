<?php
/**
 * Database Import Script for IGSS Management System
 * Automatically imports database schema and initial data
 * One-click deployment without manual XAMPP/database setup
 */

require_once 'config/database.php';

$message = '';
$error = '';
$success = false;

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'import') {
        try {
            $database = new Database();

            // Test database connection first
            if (!$database->testConnection()) {
                throw new Exception("Cannot connect to database. Please check your database configuration.");
            }

            // Import main schema
            $schemaPath = __DIR__ . '/database/schema.sql';
            if (!file_exists($schemaPath)) {
                throw new Exception("Schema file not found: " . $schemaPath);
            }

            $result = $database->executeSQLFile($schemaPath);
            if (!$result) {
                throw new Exception("Failed to import main schema.");
            }

            // Import additional SQL files if they exist
            $additionalFiles = [
                'database/messaging_system.sql',
                'database/parent_approval_system.sql'
            ];

            foreach ($additionalFiles as $file) {
                $filePath = __DIR__ . '/' . $file;
                if (file_exists($filePath)) {
                    $database->executeSQLFile($filePath);
                }
            }

            // Ensure admin user exists with default setup
            $conn = $database->getConnection();

            // First check if admin user exists
            $stmt = $conn->prepare("SELECT id FROM users WHERE user_code = 'ADMIN001' AND user_type = 'admin'");
            $stmt->execute();
            $adminExists = $stmt->fetchColumn();

            // Use a simple default password that will be changed
            $defaultPassword = 'setup123';
            $passwordHash = password_hash($defaultPassword, PASSWORD_DEFAULT);

            if (!$adminExists) {
                // Create admin user if it doesn't exist
                $stmt = $conn->prepare("
                    INSERT INTO users (user_code, first_name, last_name, email, gender, user_type, password_hash, is_approved, is_active, created_at, updated_at)
                    VALUES ('ADMIN001', 'System', 'Administrator', '<EMAIL>', 'Male', 'admin', ?, 1, 1, NOW(), NOW())
                ");
                $stmt->execute([$passwordHash]);
            } else {
                // Update existing admin user to ensure it's ready for setup
                $stmt = $conn->prepare("
                    UPDATE users
                    SET password_hash = ?, is_approved = 1, is_active = 1, updated_at = NOW()
                    WHERE user_code = 'ADMIN001' AND user_type = 'admin'
                ");
                $stmt->execute([$passwordHash]);
            }

            // Mark that admin setup is needed
            session_start();
            $_SESSION['admin_setup_needed'] = true;

            $success = true;
            $message = "Database imported successfully! Now set up your admin password.";

        } catch (Exception $e) {
            $error = "Import failed: " . $e->getMessage();
        }
    } elseif ($action === 'reset') {
        try {
            $database = new Database();
            $conn = $database->getConnection();

            // Get list of all tables
            $stmt = $conn->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

            // Drop all tables
            $conn->exec("SET FOREIGN_KEY_CHECKS = 0");
            foreach ($tables as $table) {
                $conn->exec("DROP TABLE IF EXISTS `$table`");
            }
            $conn->exec("SET FOREIGN_KEY_CHECKS = 1");

            $message = "Database reset successfully. You can now import fresh data.";

        } catch (Exception $e) {
            $error = "Reset failed: " . $e->getMessage();
        }
    }
}

// Check current database status
$dbStatus = 'Not Connected';
$tableCount = 0;
$hasData = false;

try {
    $database = new Database();
    if ($database->testConnection()) {
        $dbStatus = 'Connected';
        $conn = $database->getConnection();

        // Count tables
        $stmt = $conn->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $tableCount = count($tables);

        // Check if users table exists and has data
        if (in_array('users', $tables)) {
            $stmt = $conn->query("SELECT COUNT(*) FROM users");
            $userCount = $stmt->fetchColumn();
            $hasData = $userCount > 0;
        }
    }
} catch (Exception $e) {
    $dbStatus = 'Error: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Import - IGSS Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }

        .import-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 800px;
            margin: 0 auto;
        }

        .import-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .import-header h2 {
            color: #333;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 4px solid #007bff;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-label {
            font-weight: 500;
            color: #333;
        }

        .status-value {
            font-weight: 600;
        }

        .status-connected {
            color: #28a745;
        }

        .status-error {
            color: #dc3545;
        }

        .btn-import {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 8px;
            padding: 12px 30px;
            font-weight: 500;
            color: white;
            transition: all 0.3s ease;
            margin-right: 10px;
        }

        .btn-import:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
            color: white;
        }

        .btn-reset {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            border-radius: 8px;
            padding: 12px 30px;
            font-weight: 500;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
            color: white;
        }

        .alert {
            border-radius: 8px;
            border: none;
            padding: 15px;
            margin-bottom: 20px;
        }

        .feature-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }

        .feature-list h5 {
            color: #333;
            margin-bottom: 15px;
        }

        .feature-list ul {
            margin-bottom: 0;
        }

        .feature-list li {
            margin-bottom: 5px;
            color: #666;
        }

        .warning-box {
            background-color: #fff3cd;
            color: #856404;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="import-container">
            <div class="import-header">
                <h2><i class="fas fa-database text-primary"></i> Database Import Tool</h2>
                <p class="text-muted">One-click database setup for IGSS Management System</p>
            </div>

            <?php if ($message): ?>
                <div class="alert <?php echo $success ? 'alert-success' : 'alert-info'; ?>">
                    <i class="fas <?php echo $success ? 'fa-check-circle' : 'fa-info-circle'; ?>"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Database Status -->
            <div class="status-card">
                <h5><i class="fas fa-info-circle"></i> Database Status</h5>
                <div class="status-item">
                    <span class="status-label">Connection Status:</span>
                    <span class="status-value <?php echo $dbStatus === 'Connected' ? 'status-connected' : 'status-error'; ?>">
                        <?php echo htmlspecialchars($dbStatus); ?>
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label">Tables Count:</span>
                    <span class="status-value"><?php echo $tableCount; ?></span>
                </div>
                <div class="status-item">
                    <span class="status-label">Has Data:</span>
                    <span class="status-value <?php echo $hasData ? 'status-connected' : 'status-error'; ?>">
                        <?php echo $hasData ? 'Yes' : 'No'; ?>
                    </span>
                </div>
            </div>

            <!-- Import Actions -->
            <div class="text-center">
                <form method="POST" action="" style="display: inline;">
                    <input type="hidden" name="action" value="import">
                    <button type="submit" class="btn btn-import"
                            <?php echo $hasData ? 'onclick="return confirm(\'This will overwrite existing data. Continue?\')"' : ''; ?>>
                        <i class="fas fa-download"></i> Import Database
                    </button>
                </form>

                <?php if ($tableCount > 0): ?>
                <form method="POST" action="" style="display: inline;">
                    <input type="hidden" name="action" value="reset">
                    <button type="submit" class="btn btn-reset"
                            onclick="return confirm('This will delete ALL data. Are you sure?')">
                        <i class="fas fa-trash"></i> Reset Database
                    </button>
                </form>
                <?php endif; ?>
            </div>

            <?php if ($success): ?>
            <div class="warning-box">
                <i class="fas fa-check-circle"></i>
                <strong>✅ Database Import Successful!</strong>
                <p class="mt-2 mb-3">Your database has been imported and admin user created. Now set your admin password.</p>
                <div class="text-center">
                    <a href="admin-setup.php" class="btn btn-primary">
                        <i class="fas fa-key me-2"></i>Set Admin Password
                    </a>
                </div>
                <div class="mt-3">
                    <div class="alert alert-info">
                        <strong>Next Steps:</strong><br>
                        1. Click "Set Admin Password" above<br>
                        2. Choose your admin email and password<br>
                        3. Login with User Code: <strong>ADMIN001</strong> and your password<br>
                        4. Delete this file (importdb.php) for security
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Features List -->
            <div class="feature-list">
                <h5><i class="fas fa-star"></i> What This Import Includes</h5>
                <div class="row">
                    <div class="col-md-6">
                        <ul>
                            <li>Complete database schema</li>
                            <li>User roles and permissions</li>
                            <li>Academic levels and classes</li>
                            <li>Subject configurations</li>
                            <li>Fee structure setup</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul>
                            <li>Default admin account</li>
                            <li>Security questions</li>
                            <li>System settings</li>
                            <li>Messaging system</li>
                            <li>Parent approval system</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <a href="login.php" class="text-decoration-none">
                    <i class="fas fa-arrow-left"></i> Go to Login
                </a>
                <span class="mx-3">|</span>
                <a href="index.php" class="text-decoration-none">
                    <i class="fas fa-home"></i> Home Page
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>