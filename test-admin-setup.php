<?php
/**
 * Test Admin Setup Process
 * Use this to verify the admin setup works correctly
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h2>Admin Setup Test</h2>";

try {
    $conn = getDBConnection();
    
    // Check if admin user exists
    $stmt = $conn->prepare("SELECT * FROM users WHERE user_code = 'ADMIN001' AND user_type = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<h3>✅ Admin User Found</h3>";
        echo "<p><strong>User Code:</strong> " . htmlspecialchars($admin['user_code']) . "</p>";
        echo "<p><strong>Email:</strong> " . htmlspecialchars($admin['email']) . "</p>";
        echo "<p><strong>Name:</strong> " . htmlspecialchars($admin['first_name'] . ' ' . $admin['last_name']) . "</p>";
        echo "<p><strong>Is Approved:</strong> " . ($admin['is_approved'] ? 'Yes' : 'No') . "</p>";
        echo "<p><strong>Is Active:</strong> " . ($admin['is_active'] ? 'Yes' : 'No') . "</p>";
        echo "<p><strong>Created:</strong> " . htmlspecialchars($admin['created_at']) . "</p>";
        
        // Test different passwords
        $testPasswords = ['admin123', 'password', 'admin@1234'];
        
        echo "<h3>Password Tests</h3>";
        foreach ($testPasswords as $testPassword) {
            $isValid = password_verify($testPassword, $admin['password_hash']);
            $status = $isValid ? '✅ Valid' : '❌ Invalid';
            echo "<p><strong>'{$testPassword}':</strong> {$status}</p>";
        }
        
        // Test authentication function
        echo "<h3>Authentication Function Tests</h3>";
        foreach ($testPasswords as $testPassword) {
            $authResult = authenticateUserWithRole('ADMIN001', $testPassword, 'admin');
            $status = $authResult ? '✅ Success' : '❌ Failed';
            echo "<p><strong>authenticateUserWithRole('ADMIN001', '{$testPassword}', 'admin'):</strong> {$status}</p>";
        }
        
    } else {
        echo "<h3>❌ Admin User Not Found</h3>";
        echo "<p>The admin user does not exist in the database.</p>";
    }
    
    // Check session status
    echo "<h3>Session Status</h3>";
    session_start();
    if (isset($_SESSION['setup_required'])) {
        echo "<p><strong>Setup Required:</strong> Yes</p>";
    } else {
        echo "<p><strong>Setup Required:</strong> No</p>";
    }
    
    if (isset($_SESSION['temp_admin_password'])) {
        echo "<p><strong>Temp Password:</strong> " . htmlspecialchars($_SESSION['temp_admin_password']) . "</p>";
        
        // Test temp password
        if ($admin) {
            $tempValid = password_verify($_SESSION['temp_admin_password'], $admin['password_hash']);
            $status = $tempValid ? '✅ Valid' : '❌ Invalid';
            echo "<p><strong>Temp Password Valid:</strong> {$status}</p>";
        }
    } else {
        echo "<p><strong>Temp Password:</strong> Not set</p>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Error</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<h3>Quick Actions</h3>";
echo "<p><a href='admin-setup.php'>Go to Admin Setup</a></p>";
echo "<p><a href='direct-reset.php'>Direct Password Reset</a></p>";
echo "<p><a href='login.php'>Go to Login</a></p>";
echo "<p><a href='importdb.php'>Import Database</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
