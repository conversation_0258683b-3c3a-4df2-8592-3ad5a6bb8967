-- Create Missing Tables for IGSS Management System
-- Run this SQL script in php<PERSON>yAdmin or MySQL command line

USE igss_management;

-- Create teacher_subjects table
CREATE TABLE IF NOT EXISTS teacher_subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    teacher_id INT NOT NULL,
    subject_id INT NOT NULL,
    class_id INT NOT NULL,
    session_id INT NOT NULL DEFAULT 1,
    assigned_by INT NOT NULL,
    assigned_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_teacher_subjects_teacher (teacher_id),
    INDEX idx_teacher_subjects_class (class_id),
    UNIQUE KEY unique_teacher_subject_class (teacher_id, subject_id, class_id, session_id)
);

-- Create form_teachers table
CREATE TABLE IF NOT EXISTS form_teachers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    teacher_id INT NOT NULL,
    class_id INT NOT NULL,
    session_id INT NOT NULL DEFAULT 1,
    assigned_by INT NOT NULL,
    assigned_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_form_teachers_teacher (teacher_id),
    INDEX idx_form_teachers_class (class_id),
    UNIQUE KEY unique_class_session (class_id, session_id)
);

-- Create fee_types table
CREATE TABLE IF NOT EXISTS fee_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    fee_name VARCHAR(100) NOT NULL,
    fee_description TEXT,
    amount_js DECIMAL(10,2) NOT NULL,
    amount_ss DECIMAL(10,2) NOT NULL,
    is_mandatory BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_fee_types_active (is_active)
);

-- Insert default fee types
INSERT IGNORE INTO fee_types (fee_name, fee_description, amount_js, amount_ss, is_mandatory, created_by) VALUES
('School Fee', 'Main school fee for academic session', 150000.00, 200000.00, TRUE, 1),
('Development Levy', 'School development and infrastructure fee', 25000.00, 30000.00, TRUE, 1),
('Sports Fee', 'Sports and recreational activities fee', 5000.00, 7500.00, FALSE, 1),
('Library Fee', 'Library maintenance and book fee', 3000.00, 5000.00, FALSE, 1),
('Laboratory Fee', 'Science laboratory equipment and materials', 10000.00, 15000.00, TRUE, 1),
('Computer Fee', 'Computer lab and ICT training fee', 8000.00, 12000.00, FALSE, 1),
('Examination Fee', 'Internal and external examination fee', 15000.00, 20000.00, TRUE, 1);

-- Create academic_sessions table if it doesn't exist
CREATE TABLE IF NOT EXISTS academic_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_name VARCHAR(20) NOT NULL UNIQUE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_current BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default academic session
INSERT IGNORE INTO academic_sessions (session_name, start_date, end_date, is_current) VALUES
('2024/2025', '2024-09-01', '2025-07-31', TRUE);

-- Create terms table if it doesn't exist
CREATE TABLE IF NOT EXISTS terms (
    id INT PRIMARY KEY AUTO_INCREMENT,
    term_name VARCHAR(20) NOT NULL,
    term_number INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    session_id INT NOT NULL,
    is_current BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default terms
INSERT IGNORE INTO terms (term_name, term_number, start_date, end_date, session_id, is_current) VALUES
('First Term', 1, '2024-09-01', '2024-12-15', 1, TRUE),
('Second Term', 2, '2025-01-15', '2025-04-15', 1, FALSE),
('Third Term', 3, '2025-05-01', '2025-07-31', 1, FALSE);

SELECT 'All missing tables created successfully!' as Status;
