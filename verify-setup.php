<?php
/**
 * Database Setup Verification Script
 * Use this to diagnose and fix admin setup issues
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

$message = '';
$error = '';

// Handle fix actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create_admin') {
        try {
            $conn = getDBConnection();
            
            // Delete existing admin if any
            $stmt = $conn->prepare("DELETE FROM users WHERE user_code = 'ADMIN001' AND user_type = 'admin'");
            $stmt->execute();
            
            // Create new admin user
            $tempPassword = 'admin123'; // Simple default for testing
            $passwordHash = password_hash($tempPassword, PASSWORD_DEFAULT);
            
            $stmt = $conn->prepare("
                INSERT INTO users (user_code, first_name, last_name, email, gender, user_type, password_hash, is_approved, is_active, created_at, updated_at)
                VALUES ('ADMIN001', 'System', 'Administrator', '<EMAIL>', 'Male', 'admin', ?, 1, 1, NOW(), NOW())
            ");
            
            if ($stmt->execute([$passwordHash])) {
                $message = "Admin user created successfully! Login with ADMIN001 / admin123";
            } else {
                $error = "Failed to create admin user.";
            }
        } catch (Exception $e) {
            $error = "Error: " . $e->getMessage();
        }
    } elseif ($action === 'reset_setup') {
        session_start();
        $_SESSION['setup_required'] = true;
        $_SESSION['temp_admin_password'] = 'temp_' . bin2hex(random_bytes(4));
        $message = "Setup session reset. You can now go to admin-setup.php";
    }
}

echo "<!DOCTYPE html>
<html>
<head>
    <title>Database Setup Verification</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .btn { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔧 Database Setup Verification</h1>";

if ($message) {
    echo "<div class='status success'>✅ $message</div>";
}
if ($error) {
    echo "<div class='status error'>❌ $error</div>";
}

try {
    $conn = getDBConnection();
    echo "<div class='status success'>✅ Database connection successful</div>";
    
    // Check if users table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<div class='status success'>✅ Users table exists</div>";
        
        // Check admin user
        $stmt = $conn->prepare("SELECT * FROM users WHERE user_code = 'ADMIN001' AND user_type = 'admin'");
        $stmt->execute();
        $admin = $stmt->fetch();
        
        if ($admin) {
            echo "<div class='status success'>✅ Admin user found</div>";
            echo "<table>
                    <tr><th>Property</th><th>Value</th></tr>
                    <tr><td>User Code</td><td>{$admin['user_code']}</td></tr>
                    <tr><td>Name</td><td>{$admin['first_name']} {$admin['last_name']}</td></tr>
                    <tr><td>Email</td><td>{$admin['email']}</td></tr>
                    <tr><td>Is Approved</td><td>" . ($admin['is_approved'] ? 'Yes' : 'No') . "</td></tr>
                    <tr><td>Is Active</td><td>" . ($admin['is_active'] ? 'Yes' : 'No') . "</td></tr>
                    <tr><td>Created</td><td>{$admin['created_at']}</td></tr>
                  </table>";
            
            // Test authentication
            $testPasswords = ['admin123', 'password', 'admin@1234'];
            echo "<h3>🔐 Password Tests</h3>";
            $validPassword = false;
            foreach ($testPasswords as $testPass) {
                $isValid = password_verify($testPass, $admin['password_hash']);
                $status = $isValid ? '✅' : '❌';
                echo "<p>$status Password '$testPass': " . ($isValid ? 'Valid' : 'Invalid') . "</p>";
                if ($isValid) $validPassword = $testPass;
            }
            
            if ($validPassword) {
                echo "<div class='status success'>✅ Found working password: $validPassword</div>";
                echo "<p><strong>You can login with:</strong> ADMIN001 / $validPassword</p>";
            } else {
                echo "<div class='status warning'>⚠️ No standard passwords work. Use the reset option below.</div>";
            }
            
        } else {
            echo "<div class='status error'>❌ Admin user not found</div>";
        }
        
    } else {
        echo "<div class='status error'>❌ Users table does not exist</div>";
    }
    
    // Check session status
    session_start();
    echo "<h3>📋 Session Status</h3>";
    if (isset($_SESSION['setup_required'])) {
        echo "<p>✅ Setup Required: Yes</p>";
    } else {
        echo "<p>❌ Setup Required: No</p>";
    }
    
    if (isset($_SESSION['temp_admin_password'])) {
        echo "<p>✅ Temp Password: {$_SESSION['temp_admin_password']}</p>";
    } else {
        echo "<p>❌ Temp Password: Not set</p>";
    }
    
} catch (Exception $e) {
    echo "<div class='status error'>❌ Database Error: " . $e->getMessage() . "</div>";
}

echo "<h3>🛠️ Quick Fixes</h3>
      <form method='POST' style='display: inline;'>
          <input type='hidden' name='action' value='create_admin'>
          <button type='submit' class='btn btn-success'>Create/Reset Admin User</button>
      </form>
      
      <form method='POST' style='display: inline;'>
          <input type='hidden' name='action' value='reset_setup'>
          <button type='submit' class='btn'>Reset Setup Session</button>
      </form>
      
      <h3>🔗 Quick Links</h3>
      <a href='admin-setup.php' class='btn'>Admin Setup</a>
      <a href='direct-reset.php' class='btn'>Direct Reset</a>
      <a href='login.php' class='btn'>Login Page</a>
      <a href='importdb.php' class='btn btn-danger'>Import Database</a>
      
      <div class='status info'>
          <strong>💡 Troubleshooting Tips:</strong><br>
          1. If admin user doesn't exist, click 'Create/Reset Admin User'<br>
          2. If setup session is broken, click 'Reset Setup Session'<br>
          3. Try Direct Reset if Admin Setup fails<br>
          4. Check database connection settings in config/database.php<br>
          5. Ensure MySQL/MariaDB is running
      </div>
    </div>
</body>
</html>";
?>
