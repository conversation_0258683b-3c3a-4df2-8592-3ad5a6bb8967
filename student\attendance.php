<?php
/**
 * Student Attendance View
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a student
if (!isLoggedIn() || $_SESSION['user_type'] !== 'student') {
    header('Location: ../login.php');
    exit();
}

// Check if student account is approved
if (!$_SESSION['is_approved']) {
    header('Location: pending-approval.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get student information
$stmt = $conn->prepare("
    SELECT s.*, u.first_name, u.last_name, u.middle_name, u.user_code,
           c.class_name, l.level_name
    FROM students s
    JOIN users u ON s.user_id = u.id
    JOIN classes c ON s.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    WHERE s.user_id = ?
");
$stmt->execute([$user_id]);
$student = $stmt->fetch();

if (!$student) {
    header('Location: ../login.php');
    exit();
}

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();

// Get attendance records for current term
$stmt = $conn->prepare("
    SELECT a.*, DATE_FORMAT(a.attendance_date, '%Y-%m-%d') as formatted_date,
           DATE_FORMAT(a.attendance_date, '%W, %M %d, %Y') as display_date,
           CONCAT(u.first_name, ' ', u.last_name) as marked_by_name
    FROM attendance a
    LEFT JOIN teachers t ON a.marked_by = t.id
    LEFT JOIN users u ON t.user_id = u.id
    WHERE a.student_id = ? AND a.term_id = ?
    ORDER BY a.attendance_date DESC
");
$stmt->execute([$student['id'], $current_academic['current_term_id'] ?? 1]);
$attendance_records = $stmt->fetchAll();

// Calculate attendance statistics
$total_days = count($attendance_records);
$present_days = 0;
$absent_days = 0;
$late_days = 0;
$excused_days = 0;

foreach ($attendance_records as $record) {
    switch ($record['status']) {
        case 'Present':
            $present_days++;
            break;
        case 'Absent':
            $absent_days++;
            break;
        case 'Late':
            $late_days++;
            break;
        case 'Excused':
            $excused_days++;
            break;
    }
}

$attendance_percentage = $total_days > 0 ? round(($present_days + $late_days + $excused_days) / $total_days * 100, 1) : 0;

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Attendance - <?php echo htmlspecialchars($school_info['name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .navbar-custom .navbar-brand {
            color: #ff6b35 !important;
            font-weight: 700;
        }
        
        .navbar-custom .nav-link {
            color: #333 !important;
            font-weight: 500;
        }
        
        .main-container {
            padding: 2rem 0;
        }
        
        .page-header {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 107, 53, 0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 107, 53, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }
        
        .stat-card.present::before { background: #28a745; }
        .stat-card.absent::before { background: #dc3545; }
        .stat-card.late::before { background: #ffc107; }
        .stat-card.excused::before { background: #17a2b8; }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .stat-card.present .stat-number { color: #28a745; }
        .stat-card.absent .stat-number { color: #dc3545; }
        .stat-card.late .stat-number { color: #ffc107; }
        .stat-card.excused .stat-number { color: #17a2b8; }
        
        .attendance-table {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 107, 53, 0.1);
        }
        
        .table {
            margin-bottom: 0;
        }
        
        .table th {
            border-top: none;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            color: #495057;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem;
        }
        
        .status-present { background: #d4edda; color: #155724; }
        .status-absent { background: #f8d7da; color: #721c24; }
        .status-late { background: #fff3cd; color: #856404; }
        .status-excused { background: #d1ecf1; color: #0c5460; }
        
        .percentage-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            margin: 0 auto 1rem;
        }
        
        .percentage-excellent { background: linear-gradient(135deg, #28a745, #20c997); }
        .percentage-good { background: linear-gradient(135deg, #17a2b8, #138496); }
        .percentage-average { background: linear-gradient(135deg, #ffc107, #e0a800); }
        .percentage-poor { background: linear-gradient(135deg, #dc3545, #c82333); }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>
                Student Portal
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($student['first_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="index.php"><i class="fas fa-home me-2"></i>Dashboard</a></li>
                        <li><a class="dropdown-item" href="grades.php"><i class="fas fa-chart-line me-2"></i>Grades</a></li>
                        <li><a class="dropdown-item" href="fees.php"><i class="fas fa-money-bill me-2"></i>Fees</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-1">
                            <i class="fas fa-calendar-check me-2 text-primary"></i>
                            My Attendance Record
                        </h2>
                        <p class="text-muted mb-0">
                            <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?> - 
                            <?php echo htmlspecialchars($student['class_name']); ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="percentage-circle <?php 
                            if ($attendance_percentage >= 90) echo 'percentage-excellent';
                            elseif ($attendance_percentage >= 80) echo 'percentage-good';
                            elseif ($attendance_percentage >= 70) echo 'percentage-average';
                            else echo 'percentage-poor';
                        ?>">
                            <?php echo $attendance_percentage; ?>%
                        </div>
                        <small class="text-muted">Overall Attendance</small>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card present">
                    <div class="stat-number"><?php echo $present_days; ?></div>
                    <div class="text-muted">Present Days</div>
                </div>
                <div class="stat-card absent">
                    <div class="stat-number"><?php echo $absent_days; ?></div>
                    <div class="text-muted">Absent Days</div>
                </div>
                <div class="stat-card late">
                    <div class="stat-number"><?php echo $late_days; ?></div>
                    <div class="text-muted">Late Days</div>
                </div>
                <div class="stat-card excused">
                    <div class="stat-number"><?php echo $excused_days; ?></div>
                    <div class="text-muted">Excused Days</div>
                </div>
            </div>

            <!-- Attendance Records Table -->
            <div class="attendance-table">
                <h4 class="mb-3">
                    <i class="fas fa-list me-2"></i>
                    Detailed Attendance Records
                </h4>
                
                <?php if (!empty($attendance_records)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Marked By</th>
                                    <th>Remarks</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($attendance_records as $record): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo $record['display_date']; ?></strong>
                                        </td>
                                        <td>
                                            <span class="status-badge status-<?php echo strtolower($record['status']); ?>">
                                                <?php echo $record['status']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($record['marked_by_name'] ?? 'System'); ?></td>
                                        <td><?php echo htmlspecialchars($record['remarks'] ?? '-'); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Attendance Records</h5>
                        <p class="text-muted">No attendance records found for the current term.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
