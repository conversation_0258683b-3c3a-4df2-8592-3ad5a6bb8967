<?php
/**
 * Class Details Page for Teachers
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a teacher
if (!isLoggedIn() || $_SESSION['user_type'] !== 'teacher') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get teacher information
$stmt = $conn->prepare("
    SELECT t.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, u.user_code
    FROM teachers t
    JOIN users u ON t.user_id = u.id
    WHERE t.user_id = ?
");
$stmt->execute([$user_id]);
$teacher = $stmt->fetch();

if (!$teacher) {
    header('Location: ../login.php');
    exit();
}

// Get class ID from URL
$class_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$class_id) {
    header('Location: my-classes.php');
    exit();
}

// Verify teacher has access to this class
$stmt = $conn->prepare("
    SELECT DISTINCT c.id
    FROM subject_assignments sa
    JOIN classes c ON sa.class_id = c.id
    WHERE sa.teacher_id = ? AND sa.is_active = 1 AND c.id = ?
");
$stmt->execute([$teacher['id'], $class_id]);
$has_access = $stmt->fetchColumn();

if (!$has_access) {
    header('Location: my-classes.php');
    exit();
}

// Get class details
$stmt = $conn->prepare("
    SELECT 
        c.id,
        c.class_name,
        c.class_type,
        l.level_name,
        l.id as level_id
    FROM classes c
    JOIN levels l ON c.level_id = l.id
    WHERE c.id = ?
");
$stmt->execute([$class_id]);
$class = $stmt->fetch();

// Get students in this class
$stmt = $conn->prepare("
    SELECT 
        s.id,
        s.admission_number,
        u.first_name,
        u.last_name,
        u.middle_name,
        u.email,
        u.phone,
        u.gender,
        s.date_of_birth
    FROM students s
    JOIN users u ON s.user_id = u.id
    WHERE s.class_id = ? AND s.current_session_id = ?
    ORDER BY u.last_name, u.first_name
");
$stmt->execute([$class_id, 1]); // Using session_id = 1 for now
$students = $stmt->fetchAll();

// Get subjects taught in this class by this teacher
$stmt = $conn->prepare("
    SELECT 
        s.id,
        s.subject_name,
        s.subject_code
    FROM subject_assignments sa
    JOIN subjects s ON sa.subject_id = s.id
    WHERE sa.teacher_id = ? AND sa.class_id = ? AND sa.is_active = 1
    ORDER BY s.subject_name
");
$stmt->execute([$teacher['id'], $class_id]);
$subjects = $stmt->fetchAll();

// Check if teacher is form teacher for this class
$stmt = $conn->prepare("
    SELECT id FROM form_teachers 
    WHERE teacher_id = ? AND class_id = ? AND is_active = 1
");
$stmt->execute([$teacher['id'], $class_id]);
$is_form_teacher = $stmt->fetchColumn() ? true : false;

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();
$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Class Details - <?php echo htmlspecialchars($class['class_name']); ?> - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #ff7b00 0%, #ff9500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }

        .sidebar {
            background: linear-gradient(135deg, #ff7b00 0%, #ff9500 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        .sidebar::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.8);
        }

        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.6) rgba(255, 255, 255, 0.2);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 80px;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.25) 100%);
            border-radius: 60px 0 0 60px;
        }

        .sidebar-header {
            padding: 25px 20px;
            text-align: center;
            position: relative;
            background: rgba(255, 255, 255, 0.01);
            border-radius: 0 0 30px 30px;
            margin-bottom: 15px;
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 700;
            font-size: 1.4rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header small {
            opacity: 0.9;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            margin-bottom: 80px;
        }

        .sidebar-menu li {
            margin: 1px 15px;
        }

        .sidebar-menu a {
            color: white;
            text-decoration: none;
            padding: 8px 20px;
            display: block;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 25px;
            position: relative;
            font-weight: 500;
            font-size: 0.85rem;
            background: rgba(255, 255, 255, 0.05);
            border: none;
            margin-bottom: 1px;
        }

        .sidebar-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover::before, .sidebar-menu a.active::before {
            opacity: 1;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            transform: translateX(5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .sidebar-menu a.active {
            background: rgba(255, 255, 255, 0.2);
        }

        .back-to-home {
            position: absolute;
            bottom: 60px;
            left: 15px;
            right: 15px;
            z-index: 1001;
        }

        .back-to-home .btn {
            background: rgba(255, 255, 255, 0.3);
            border: 3px solid rgba(255, 255, 255, 0.6);
            color: white;
            font-weight: 800;
            padding: 12px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            width: 100%;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            font-size: 0.95rem;
        }

        .back-to-home .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
            position: relative;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px 30px;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 25px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .form-teacher-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
        }

        .student-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .student-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .student-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .subject-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            margin: 2px;
            display: inline-block;
        }

        .btn-action {
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 2px;
        }

        .btn-action:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-chalkboard-teacher me-2"></i>IGSS Teacher</h4>
            <small>Management Portal</small>
        </div>

        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
            <li><a href="my-classes.php" class="active"><i class="fas fa-users me-2"></i>My Classes</a></li>
            <li><a href="my-subjects.php"><i class="fas fa-book me-2"></i>My Subjects</a></li>
            <li><a href="attendance.php"><i class="fas fa-calendar-check me-2"></i>Attendance</a></li>
            <li><a href="grades.php"><i class="fas fa-graduation-cap me-2"></i>Grades</a></li>
            <li><a href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
            <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
        </ul>

        <!-- Back to Home Button -->
        <div class="back-to-home">
            <a href="../index.php" class="btn">
                <i class="fas fa-home me-2"></i>Back to Home
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">
                        <i class="fas fa-users me-2"></i><?php echo htmlspecialchars($class['class_name']); ?>
                        <?php if ($is_form_teacher): ?>
                            <span class="badge bg-warning ms-2">
                                <i class="fas fa-star me-1"></i>Form Class
                            </span>
                        <?php endif; ?>
                    </h4>
                    <small class="text-muted"><?php echo htmlspecialchars($class['level_name']); ?> - Class Details</small>
                </div>
                <div>
                    <a href="my-classes.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Classes
                    </a>
                </div>
            </div>
        </div>

        <!-- Class Overview -->
        <div class="row">
            <div class="col-md-4">
                <div class="content-card">
                    <h5 class="mb-3"><i class="fas fa-info-circle me-2"></i>Class Overview</h5>
                    <div class="mb-3">
                        <strong>Class Name:</strong> <?php echo htmlspecialchars($class['class_name']); ?>
                    </div>
                    <div class="mb-3">
                        <strong>Level:</strong> <?php echo htmlspecialchars($class['level_name']); ?>
                    </div>
                    <div class="mb-3">
                        <strong>Total Students:</strong> <?php echo count($students); ?>
                    </div>
                    <div class="mb-3">
                        <strong>Subjects Teaching:</strong> <?php echo count($subjects); ?>
                    </div>
                    <?php if ($is_form_teacher): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-star me-2"></i>You are the form teacher for this class
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="col-md-8">
                <div class="content-card">
                    <h5 class="mb-3"><i class="fas fa-book me-2"></i>Subjects You Teach</h5>
                    <?php if (count($subjects) > 0): ?>
                        <div class="mb-3">
                            <?php foreach ($subjects as $subject): ?>
                                <span class="subject-badge">
                                    <?php echo htmlspecialchars($subject['subject_name']); ?>
                                    (<?php echo htmlspecialchars($subject['subject_code']); ?>)
                                </span>
                            <?php endforeach; ?>
                        </div>
                        <div class="d-flex gap-2 flex-wrap">
                            <a href="attendance.php?class_id=<?php echo $class_id; ?>" class="btn btn-primary btn-action">
                                <i class="fas fa-calendar-check me-1"></i>Mark Attendance
                            </a>
                            <a href="grades.php?class_id=<?php echo $class_id; ?>" class="btn btn-success btn-action">
                                <i class="fas fa-graduation-cap me-1"></i>Enter Grades
                            </a>
                            <?php if ($is_form_teacher): ?>
                                <button class="btn btn-warning btn-action" onclick="showFormTeacherActions()">
                                    <i class="fas fa-star me-1"></i>Form Teacher Actions
                                </button>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center text-muted">
                            <i class="fas fa-book fa-2x mb-3"></i>
                            <p>No subjects assigned for this class</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Students List -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>Students (<?php echo count($students); ?>)</h5>
                <div class="input-group" style="width: 300px;">
                    <input type="text" class="form-control" id="studentSearch" placeholder="Search students...">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                </div>
            </div>

            <?php if (count($students) > 0): ?>
                <div class="row" id="studentsContainer">
                    <?php foreach ($students as $student): ?>
                        <div class="col-lg-6 col-md-12 student-item">
                            <div class="student-card">
                                <div class="d-flex align-items-center">
                                    <div class="student-avatar me-3">
                                        <?php echo strtoupper(substr($student['first_name'], 0, 1) . substr($student['last_name'], 0, 1)); ?>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1 student-name">
                                            <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                                        </h6>
                                        <small class="text-muted">
                                            Admission No: <?php echo htmlspecialchars($student['admission_number']); ?>
                                        </small>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($student['email']); ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-<?php echo $student['gender'] === 'Male' ? 'primary' : 'pink'; ?>">
                                            <?php echo htmlspecialchars($student['gender']); ?>
                                        </span>
                                        <br>
                                        <small class="text-muted">
                                            <?php
                                            if ($student['date_of_birth']) {
                                                $age = date_diff(date_create($student['date_of_birth']), date_create('today'))->y;
                                                echo "Age: " . $age;
                                            }
                                            ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Students Found</h5>
                    <p class="text-muted">There are currently no students enrolled in this class.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Student search functionality
        document.getElementById('studentSearch').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const studentItems = document.querySelectorAll('.student-item');

            studentItems.forEach(item => {
                const studentName = item.querySelector('.student-name').textContent.toLowerCase();
                const admissionNo = item.textContent.toLowerCase();

                if (studentName.includes(searchTerm) || admissionNo.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // Form teacher actions
        function showFormTeacherActions() {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-star text-warning me-2"></i>Form Teacher Actions
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body text-center">
                                            <i class="fas fa-chart-line fa-2x text-primary mb-3"></i>
                                            <h6>Class Performance</h6>
                                            <p class="text-muted">View overall class performance and analytics</p>
                                            <button class="btn btn-primary btn-sm" onclick="alert('Feature coming soon!')">
                                                View Reports
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body text-center">
                                            <i class="fas fa-calendar-alt fa-2x text-success mb-3"></i>
                                            <h6>Attendance Summary</h6>
                                            <p class="text-muted">Monitor class attendance patterns</p>
                                            <button class="btn btn-success btn-sm" onclick="alert('Feature coming soon!')">
                                                View Attendance
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body text-center">
                                            <i class="fas fa-bell fa-2x text-warning mb-3"></i>
                                            <h6>Parent Communication</h6>
                                            <p class="text-muted">Send notifications to parents</p>
                                            <button class="btn btn-warning btn-sm" onclick="alert('Feature coming soon!')">
                                                Send Notice
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body text-center">
                                            <i class="fas fa-clipboard-list fa-2x text-info mb-3"></i>
                                            <h6>Class Management</h6>
                                            <p class="text-muted">Manage class activities and events</p>
                                            <button class="btn btn-info btn-sm" onclick="alert('Feature coming soon!')">
                                                Manage Class
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            // Remove modal from DOM after it's hidden
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        // Add hover effects to student cards
        document.addEventListener('DOMContentLoaded', function() {
            const studentCards = document.querySelectorAll('.student-card');

            studentCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
