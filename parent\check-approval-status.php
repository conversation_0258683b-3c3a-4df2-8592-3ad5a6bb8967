<?php
/**
 * Check Approval Status API Endpoint
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in and is a parent
if (!isLoggedIn() || $_SESSION['user_type'] !== 'parent') {
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

try {
    // Check current approval status
    $stmt = $conn->prepare("SELECT is_approved FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        echo json_encode(['error' => 'User not found']);
        exit();
    }
    
    echo json_encode([
        'approved' => (bool)$user['is_approved'],
        'status' => $user['is_approved'] ? 'approved' : 'pending'
    ]);
    
} catch (Exception $e) {
    echo json_encode(['error' => 'Database error']);
}
?>
