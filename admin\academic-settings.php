<?php
/**
 * Academic Settings Page for IGSS Management System
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
requireLogin();
if (!hasRole('admin')) {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'update_session') {
        $current_session = sanitizeInput($_POST['current_session']);
        $current_term = sanitizeInput($_POST['current_term']);

        try {
            $conn->beginTransaction();

            // Update current session
            $stmt = $conn->prepare("
                INSERT INTO system_settings (setting_key, setting_value, updated_by)
                VALUES ('current_session', ?, 1)
                ON DUPLICATE KEY UPDATE setting_value = ?, updated_by = 1
            ");
            $stmt->execute([$current_session, $current_session]);

            // Update current term
            $stmt = $conn->prepare("
                INSERT INTO system_settings (setting_key, setting_value, updated_by)
                VALUES ('current_term', ?, 1)
                ON DUPLICATE KEY UPDATE setting_value = ?, updated_by = 1
            ");
            $stmt->execute([$current_term, $current_term]);

            $conn->commit();
            $message = 'Academic session and term updated successfully.';
        } catch (Exception $e) {
            $conn->rollBack();
            $error = 'Failed to update academic settings: ' . $e->getMessage();
        }
    } elseif ($action === 'add_class') {
        $class_name = sanitizeInput($_POST['class_name']);
        $level_id = $_POST['level_id'];
        $class_type = sanitizeInput($_POST['class_type']);

        try {
            $stmt = $conn->prepare("INSERT INTO classes (class_name, level_id, class_type) VALUES (?, ?, ?)");
            $stmt->execute([$class_name, $level_id, $class_type]);
            $message = 'Class added successfully.';
        } catch (Exception $e) {
            $error = 'Failed to add class: ' . $e->getMessage();
        }
    } elseif ($action === 'add_subject') {
        $subject_name = sanitizeInput($_POST['subject_name']);
        $subject_code = sanitizeInput($_POST['subject_code']);
        $level_id = $_POST['level_id'];
        $class_type = sanitizeInput($_POST['class_type']);

        try {
            $stmt = $conn->prepare("INSERT INTO subjects (subject_name, subject_code, level_id, class_type) VALUES (?, ?, ?, ?)");
            $stmt->execute([$subject_name, $subject_code, $level_id, $class_type]);
            $message = 'Subject added successfully.';
        } catch (Exception $e) {
            $error = 'Failed to add subject: ' . $e->getMessage();
        }
    }
}

// Get current academic info
$current_academic = getCurrentAcademicInfo();

// Get levels
$stmt = $conn->prepare("SELECT * FROM levels ORDER BY id");
$stmt->execute();
$levels = $stmt->fetchAll();

// Get classes
$stmt = $conn->prepare("
    SELECT c.*, l.level_name
    FROM classes c
    JOIN levels l ON c.level_id = l.id
    ORDER BY l.id, c.class_name
");
$stmt->execute();
$classes = $stmt->fetchAll();

// Get subjects
$stmt = $conn->prepare("
    SELECT s.*, l.level_name
    FROM subjects s
    JOIN levels l ON s.level_id = l.id
    ORDER BY l.id, s.subject_name
");
$stmt->execute();
$subjects = $stmt->fetchAll();

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Academic Settings - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }

        .sidebar {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        /* Custom white scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.8);
        }

        /* For Firefox */
        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.6) rgba(255, 255, 255, 0.2);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 80px;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.25) 100%);
            border-radius: 60px 0 0 60px;
        }

        .sidebar-header {
            padding: 25px 20px; /* Reduced padding */
            text-align: center;
            position: relative;
            background: rgba(255, 255, 255, 0.01); /* Minimal opacity */
            border-radius: 0 0 30px 30px;
            margin-bottom: 15px; /* Reduced margin */
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 700;
            font-size: 1.4rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header small {
            opacity: 0.9;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            margin-bottom: 80px; /* Space for back to home button */
        }

        .sidebar-menu li {
            margin: 1px 15px; /* Minimal margin for tight fit */
        }

        .sidebar-menu a {
            color: white;
            text-decoration: none;
            padding: 8px 20px; /* Smaller padding like before */
            display: block;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 25px;
            position: relative;
            font-weight: 500; /* Normal font weight */
            font-size: 0.85rem; /* Smaller font size */
            background: rgba(255, 255, 255, 0.05); /* Very subtle background */
            border: none; /* Remove border */
            margin-bottom: 1px; /* Minimal spacing */
        }

        .sidebar-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover::before, .sidebar-menu a.active::before {
            opacity: 1;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            transform: translateX(5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .sidebar-menu a.active {
            background: rgba(255, 255, 255, 0.2);
        }

        .back-to-home {
            position: absolute;
            bottom: 60px; /* Moved higher up */
            left: 15px;
            right: 15px;
            z-index: 1001;
        }

        .back-to-home .btn {
            background: rgba(255, 255, 255, 0.2); /* More visible white background */
            border: 2px solid rgba(255, 255, 255, 0.4); /* More visible white border */
            color: white;
            font-weight: 700;
            padding: 15px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            width: 100%;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .back-to-home .btn:hover {
            background: rgba(255, 255, 255, 0.3); /* More visible hover effect */
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
            position: relative;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px 30px;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff9a56, #ff6b35, #f7931e);
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 25px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff9a56, #ff6b35, #f7931e);
        }

        .nav-tabs .nav-link {
            border: none;
            color: #666;
            font-weight: 500;
        }

        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
        }

        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
        }

        .badge-level {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-graduation-cap me-2"></i>IGSS Admin</h4>
            <small>Management System</small>
        </div>

        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
            <li><a href="manage-users.php"><i class="fas fa-users me-2"></i>Manage Users</a></li>
            <li><a href="academic-settings.php" class="active"><i class="fas fa-calendar-alt me-2"></i>Academic Settings</a></li>
            <li><a href="assign-subjects.php"><i class="fas fa-book me-2"></i>Assign Subjects</a></li>
            <li><a href="assign-form-teachers.php"><i class="fas fa-chalkboard-teacher me-2"></i>Form Teachers</a></li>
            <li><a href="manage-fees.php"><i class="fas fa-money-bill me-2"></i>Manage Fees</a></li>
            <li><a href="fee-confirmations.php"><i class="fas fa-money-check-alt me-2"></i>Fee Confirmations</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar me-2"></i>Reports</a></li>
            <li><a href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
            <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
        </ul>

        <!-- Back to Home Button -->
        <div class="back-to-home">
            <a href="../index.php" class="btn">
                <i class="fas fa-home me-2"></i>Back to Home
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h4 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Academic Settings</h4>
            <small class="text-muted">Manage academic sessions, terms, classes, and subjects</small>
        </div>

        <!-- Alerts -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Tabs -->
        <div class="content-card">
            <ul class="nav nav-tabs mb-4" id="academicTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="session-tab" data-bs-toggle="tab" data-bs-target="#session" type="button" role="tab">
                        <i class="fas fa-calendar me-2"></i>Session & Term
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="classes-tab" data-bs-toggle="tab" data-bs-target="#classes" type="button" role="tab">
                        <i class="fas fa-school me-2"></i>Classes
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="subjects-tab" data-bs-toggle="tab" data-bs-target="#subjects" type="button" role="tab">
                        <i class="fas fa-book me-2"></i>Subjects
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="academicTabsContent">
                <!-- Session & Term Tab -->
                <div class="tab-pane fade show active" id="session" role="tabpanel">
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="fas fa-edit me-2"></i>Update Current Session & Term</h5>
                            <form method="POST">
                                <input type="hidden" name="action" value="update_session">
                                <div class="mb-3">
                                    <label class="form-label">Current Academic Session</label>
                                    <input type="text" name="current_session" class="form-control"
                                           value="<?php echo htmlspecialchars($current_academic['session']); ?>"
                                           placeholder="e.g., 2024/2025" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Current Term</label>
                                    <select name="current_term" class="form-select" required>
                                        <option value="1" <?php echo $current_academic['term'] == '1' ? 'selected' : ''; ?>>First Term</option>
                                        <option value="2" <?php echo $current_academic['term'] == '2' ? 'selected' : ''; ?>>Second Term</option>
                                        <option value="3" <?php echo $current_academic['term'] == '3' ? 'selected' : ''; ?>>Third Term</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Update Session & Term
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-info-circle me-2"></i>Current Settings</h5>
                            <div class="alert alert-info">
                                <p><strong>Academic Session:</strong> <?php echo htmlspecialchars($current_academic['session']); ?></p>
                                <p class="mb-0"><strong>Current Term:</strong> <?php echo TERMS[$current_academic['term']]; ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Classes Tab -->
                <div class="tab-pane fade" id="classes" role="tabpanel">
                    <div class="row">
                        <div class="col-md-4">
                            <h5><i class="fas fa-plus me-2"></i>Add New Class</h5>
                            <form method="POST">
                                <input type="hidden" name="action" value="add_class">
                                <div class="mb-3">
                                    <label class="form-label">Class Name</label>
                                    <input type="text" name="class_name" class="form-control" placeholder="e.g., JSS1A" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Level</label>
                                    <select name="level_id" class="form-select" required>
                                        <option value="">Select Level</option>
                                        <?php foreach ($levels as $level): ?>
                                            <option value="<?php echo $level['id']; ?>"><?php echo htmlspecialchars($level['level_name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Class Type</label>
                                    <select name="class_type" class="form-select" required>
                                        <option value="General">General</option>
                                        <option value="Science">Science</option>
                                        <option value="Art">Art</option>
                                        <option value="Commercial">Commercial</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>Add Class
                                </button>
                            </form>
                        </div>
                        <div class="col-md-8">
                            <h5><i class="fas fa-list me-2"></i>Existing Classes</h5>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Class Name</th>
                                            <th>Level</th>
                                            <th>Type</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($classes as $class): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($class['class_name']); ?></td>
                                                <td><span class="badge bg-primary badge-level"><?php echo htmlspecialchars($class['level_name']); ?></span></td>
                                                <td><span class="badge bg-secondary"><?php echo htmlspecialchars($class['class_type']); ?></span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="editClass(<?php echo $class['id']; ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteClass(<?php echo $class['id']; ?>)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subjects Tab -->
                <div class="tab-pane fade" id="subjects" role="tabpanel">
                    <div class="row">
                        <div class="col-md-4">
                            <h5><i class="fas fa-plus me-2"></i>Add New Subject</h5>
                            <form method="POST">
                                <input type="hidden" name="action" value="add_subject">
                                <div class="mb-3">
                                    <label class="form-label">Subject Name</label>
                                    <input type="text" name="subject_name" class="form-control" placeholder="e.g., Mathematics" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Subject Code</label>
                                    <input type="text" name="subject_code" class="form-control" placeholder="e.g., MTH" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Level</label>
                                    <select name="level_id" class="form-select" required>
                                        <option value="">Select Level</option>
                                        <?php foreach ($levels as $level): ?>
                                            <option value="<?php echo $level['id']; ?>"><?php echo htmlspecialchars($level['level_name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Class Type</label>
                                    <select name="class_type" class="form-select" required>
                                        <option value="All">All Classes</option>
                                        <option value="General">General Only</option>
                                        <option value="Science">Science Only</option>
                                        <option value="Art">Art Only</option>
                                        <option value="Commercial">Commercial Only</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>Add Subject
                                </button>
                            </form>
                        </div>
                        <div class="col-md-8">
                            <h5><i class="fas fa-list me-2"></i>Existing Subjects</h5>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Subject Name</th>
                                            <th>Code</th>
                                            <th>Level</th>
                                            <th>Class Type</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($subjects as $subject): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                                <td><code><?php echo htmlspecialchars($subject['subject_code']); ?></code></td>
                                                <td><span class="badge bg-primary badge-level"><?php echo htmlspecialchars($subject['level_name']); ?></span></td>
                                                <td><span class="badge bg-secondary"><?php echo htmlspecialchars($subject['class_type']); ?></span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="editSubject(<?php echo $subject['id']; ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteSubject(<?php echo $subject['id']; ?>)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function editClass(classId) {
            alert('Edit class functionality will be implemented. Class ID: ' + classId);
        }

        function deleteClass(classId) {
            if (confirm('Are you sure you want to delete this class?')) {
                alert('Delete class functionality will be implemented. Class ID: ' + classId);
            }
        }

        function editSubject(subjectId) {
            alert('Edit subject functionality will be implemented. Subject ID: ' + subjectId);
        }

        function deleteSubject(subjectId) {
            if (confirm('Are you sure you want to delete this subject?')) {
                alert('Delete subject functionality will be implemented. Subject ID: ' + subjectId);
            }
        }
    </script>
</body>
</html>
