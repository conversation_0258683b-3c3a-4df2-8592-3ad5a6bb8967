<?php
/**
 * Authentication and Role-Based Access Control Testing
 * IGSS Management System
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

session_start();

$testResults = [];
$message = '';
$error = '';

// Test login functionality
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_login'])) {
    $user_code = sanitizeInput($_POST['user_code']);
    $password = $_POST['password'];
    $user_type = $_POST['user_type'];

    try {
        $result = authenticateUserWithRole($user_code, $password, $user_type);

        if ($result) {
            $message = "Login successful for {$user_type}: {$user_code}";

            // Test role-based access
            $access_tests = [];

            switch ($user_type) {
                case 'admin':
                    $access_tests = [
                        'admin/dashboard.php' => 'Admin Dashboard',
                        'admin/manage-users.php' => 'User Management',
                        'admin/fee-confirmations.php' => 'Fee Confirmations'
                    ];
                    break;
                case 'teacher':
                    $access_tests = [
                        'teacher/dashboard.php' => 'Teacher Dashboard',
                        'teacher/grades-new.php' => 'Enhanced Grading',
                        'teacher/attendance.php' => 'Attendance Management'
                    ];
                    break;
                case 'parent':
                    $access_tests = [
                        'parent/index.php' => 'Parent Dashboard',
                        'parent/pay-fees.php' => 'Payment System',
                        'parent/view-grades.php' => 'View Student Grades'
                    ];
                    break;
                case 'student':
                    $access_tests = [
                        'student/index.php' => 'Student Dashboard',
                        'student/grades.php' => 'View Grades',
                        'student/fees.php' => 'Payment Status'
                    ];
                    break;
            }

            $testResults['access_tests'] = $access_tests;
        } else {
            $error = "Login failed for {$user_type}: {$user_code}";
        }
    } catch (Exception $e) {
        $error = "Authentication error: " . $e->getMessage();
    }
}

// Test logout
if (isset($_POST['test_logout'])) {
    session_destroy();
    session_start();
    $message = "Logout successful";
}

// Get sample users for testing
$conn = getDBConnection();
$sample_users = [];

try {
    // Get sample users from each role
    $user_types = ['admin', 'teacher', 'parent', 'student'];

    foreach ($user_types as $type) {
        $stmt = $conn->prepare("
            SELECT user_code, first_name, last_name, user_type
            FROM users
            WHERE user_type = ? AND is_active = 1
            LIMIT 3
        ");
        $stmt->execute([$type]);
        $users = $stmt->fetchAll();
        $sample_users[$type] = $users;
    }
} catch (Exception $e) {
    $error = "Failed to fetch sample users: " . $e->getMessage();
}

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Testing - IGSS Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }
        .test-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .user-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .user-card:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .user-card.admin { border-left-color: #dc3545; }
        .user-card.teacher { border-left-color: #28a745; }
        .user-card.parent { border-left-color: #007bff; }
        .user-card.student { border-left-color: #ffc107; }
        .btn-test {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        .access-test {
            background: #e9ecef;
            border-radius: 8px;
            padding: 10px 15px;
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <div class="text-center mb-4">
                <h2><i class="fas fa-shield-alt text-primary"></i> Authentication & Access Control Testing</h2>
                <p class="text-muted">Test login functionality and role-based access control</p>
            </div>

            <!-- Messages -->
            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Current Session Status -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h5><i class="fas fa-user-circle me-2"></i>Current Session</h5>
                            <?php if (isLoggedIn()): ?>
                                <p class="mb-1"><strong>User:</strong> <?php echo $_SESSION['first_name'] . ' ' . $_SESSION['last_name']; ?></p>
                                <p class="mb-1"><strong>Role:</strong> <?php echo ucfirst($_SESSION['user_type']); ?></p>
                                <p class="mb-1"><strong>User Code:</strong> <?php echo $_SESSION['user_code']; ?></p>
                                <form method="POST" style="display: inline;">
                                    <button type="submit" name="test_logout" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-sign-out-alt me-1"></i>Logout
                                    </button>
                                </form>
                            <?php else: ?>
                                <p class="text-muted">No active session</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h5><i class="fas fa-info-circle me-2"></i>Test Information</h5>
                            <p class="mb-1">Default admin password: <code>admin123</code></p>
                            <p class="mb-1">Click on user cards below to auto-fill login form</p>
                            <p class="mb-0">Test different roles to verify access control</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Login Testing Form -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-sign-in-alt me-2"></i>Login Test Form</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="loginForm">
                                <div class="mb-3">
                                    <label class="form-label">User Type</label>
                                    <select name="user_type" id="userType" class="form-select" required>
                                        <option value="">Select User Type</option>
                                        <option value="admin">Admin</option>
                                        <option value="teacher">Teacher</option>
                                        <option value="parent">Parent</option>
                                        <option value="student">Student</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">User Code</label>
                                    <input type="text" name="user_code" id="userCode" class="form-control" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Password</label>
                                    <input type="password" name="password" id="password" class="form-control" required>
                                </div>
                                <button type="submit" name="test_login" class="btn btn-test">
                                    <i class="fas fa-sign-in-alt me-2"></i>Test Login
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Access Test Results -->
                    <?php if (!empty($testResults['access_tests'])): ?>
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5><i class="fas fa-lock me-2"></i>Access Control Test Results</h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($testResults['access_tests'] as $url => $name): ?>
                            <div class="access-test">
                                <span><?php echo htmlspecialchars($name); ?></span>
                                <div>
                                    <span class="status-indicator status-success" title="Access Granted"></span>
                                    <a href="<?php echo $url; ?>" target="_blank" class="btn btn-sm btn-outline-primary ms-2">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-users me-2"></i>Sample Users for Testing</h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($sample_users as $type => $users): ?>
                            <h6 class="text-<?php echo $type === 'admin' ? 'danger' : ($type === 'teacher' ? 'success' : ($type === 'parent' ? 'primary' : 'warning')); ?>">
                                <i class="fas fa-<?php echo $type === 'admin' ? 'user-shield' : ($type === 'teacher' ? 'chalkboard-teacher' : ($type === 'parent' ? 'users' : 'user-graduate')); ?> me-1"></i>
                                <?php echo ucfirst($type); ?>s
                            </h6>
                            <?php if (!empty($users)): ?>
                                <?php foreach ($users as $user): ?>
                                <div class="user-card <?php echo $type; ?>"
                                     onclick="fillLoginForm('<?php echo $type; ?>', '<?php echo $user['user_code']; ?>')">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($user['user_code']); ?></small>
                                        </div>
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-muted">No <?php echo $type; ?>s found</p>
                            <?php endif; ?>
                            <hr>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Access Links -->
            <div class="text-center mt-4">
                <h5>Quick Access Links</h5>
                <div class="row">
                    <div class="col-md-3">
                        <a href="login.php" class="btn btn-test w-100 mb-2">
                            <i class="fas fa-sign-in-alt me-1"></i>Main Login
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="comprehensive-test.php" class="btn btn-test w-100 mb-2">
                            <i class="fas fa-vial me-1"></i>System Tests
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="run-database-updates.php" class="btn btn-test w-100 mb-2">
                            <i class="fas fa-database me-1"></i>DB Updates
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="importdb.php" class="btn btn-test w-100 mb-2">
                            <i class="fas fa-download me-1"></i>Import DB
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function fillLoginForm(userType, userCode) {
            document.getElementById('userType').value = userType;
            document.getElementById('userCode').value = userCode;

            // Set default passwords
            let defaultPassword = '';
            switch(userType) {
                case 'admin':
                    defaultPassword = 'admin123';
                    break;
                case 'teacher':
                    defaultPassword = 'teacher123';
                    break;
                case 'parent':
                    defaultPassword = 'parent123';
                    break;
                case 'student':
                    defaultPassword = 'student123';
                    break;
            }

            document.getElementById('password').value = defaultPassword;

            // Highlight the form
            const form = document.getElementById('loginForm');
            form.style.border = '2px solid #667eea';
            form.style.borderRadius = '10px';
            form.style.padding = '10px';

            setTimeout(() => {
                form.style.border = '';
                form.style.borderRadius = '';
                form.style.padding = '';
            }, 2000);
        }
    </script>
</body>
</html>