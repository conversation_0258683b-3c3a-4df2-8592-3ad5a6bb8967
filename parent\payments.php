<?php
/**
 * Fee Payments Page for Parents
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a parent
if (!isLoggedIn() || $_SESSION['user_type'] !== 'parent') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get parent information including approval status
$stmt = $conn->prepare("
    SELECT p.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, u.user_code, u.is_approved
    FROM parents p
    JOIN users u ON p.user_id = u.id
    WHERE p.user_id = ?
");
$stmt->execute([$user_id]);
$parent = $stmt->fetch();

if (!$parent) {
    header('Location: ../login.php');
    exit();
}

// Check if parent account is approved
if (!$parent['is_approved']) {
    header('Location: pending-approval.php');
    exit();
}

$message = '';
$error = '';

// Handle fee payment submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_payment'])) {
    $student_id = (int)$_POST['student_id'];
    $amount_paid = (float)$_POST['amount_paid'];
    $payment_method = sanitizeInput($_POST['payment_method']);
    $bank_name = sanitizeInput($_POST['bank_name']);
    $teller_number = sanitizeInput($_POST['teller_number']);
    $payment_date = sanitizeInput($_POST['payment_date']);
    
    // Verify parent has access to this student
    $stmt = $conn->prepare("
        SELECT COUNT(*) FROM parent_student_relationships 
        WHERE parent_id = ? AND student_id = ?
    ");
    $stmt->execute([$parent['id'], $student_id]);
    $has_access = $stmt->fetchColumn();
    
    if (!$has_access) {
        $error = 'You do not have access to make payments for this student.';
    } elseif ($amount_paid <= 0) {
        $error = 'Please enter a valid payment amount.';
    } else {
        try {
            $conn->beginTransaction();

            // Handle receipt upload
            $receipt_path = null;
            if (isset($_FILES['receipt_image']) && $_FILES['receipt_image']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = '../uploads/receipts/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                // Validate file type
                $allowed_types = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
                $file_type = $_FILES['receipt_image']['type'];

                if (!in_array($file_type, $allowed_types)) {
                    throw new Exception('Invalid file type. Please upload JPG, PNG, or PDF files only.');
                }

                // Validate file size (5MB max)
                if ($_FILES['receipt_image']['size'] > 5 * 1024 * 1024) {
                    throw new Exception('File size too large. Maximum size is 5MB.');
                }

                $file_extension = pathinfo($_FILES['receipt_image']['name'], PATHINFO_EXTENSION);
                $receipt_filename = 'receipt_' . $student_id . '_' . time() . '.' . $file_extension;
                $receipt_path = $upload_dir . $receipt_filename;

                if (!move_uploaded_file($_FILES['receipt_image']['tmp_name'], $receipt_path)) {
                    throw new Exception('Failed to upload receipt image.');
                }

                $receipt_path = 'uploads/receipts/' . $receipt_filename; // Store relative path
            }

            // Get current academic session
            $current_academic = getCurrentAcademicInfo();

            // Insert payment record
            $stmt = $conn->prepare("
                INSERT INTO fee_payments
                (student_id, session_id, term_id, amount_paid, payment_date, payment_method,
                 bank_name, teller_number, receipt_image, uploaded_by, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Pending', NOW())
            ");
            $stmt->execute([
                $student_id,
                $current_academic['session_id'],
                $current_academic['term_id'],
                $amount_paid,
                $payment_date,
                $payment_method,
                $bank_name,
                $teller_number,
                $receipt_path,
                $parent['id']
            ]);

            $conn->commit();
            $message = 'Payment submitted successfully! Your payment evidence has been uploaded and will be reviewed by the administration.';

        } catch (Exception $e) {
            $conn->rollback();
            $error = 'Failed to submit payment: ' . $e->getMessage();
        }
    }
}

// Get all linked wards
$stmt = $conn->prepare("
    SELECT 
        s.id as student_id,
        s.admission_number,
        u.first_name,
        u.last_name,
        c.class_name,
        l.level_name,
        psr.relationship_type,
        psr.is_primary
    FROM parent_student_relationships psr
    JOIN students s ON psr.student_id = s.id
    JOIN users u ON s.user_id = u.id
    JOIN classes c ON s.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    WHERE psr.parent_id = ?
    ORDER BY psr.is_primary DESC, u.first_name ASC
");
$stmt->execute([$parent['id']]);
$wards = $stmt->fetchAll();

// Get payment history for all wards
$payment_history = [];
if (!empty($wards)) {
    $ward_ids = array_column($wards, 'student_id');
    $placeholders = str_repeat('?,', count($ward_ids) - 1) . '?';
    
    $stmt = $conn->prepare("
        SELECT 
            fp.*,
            u.first_name,
            u.last_name,
            s.admission_number,
            asess.session_name,
            t.term_name
        FROM fee_payments fp
        JOIN students s ON fp.student_id = s.id
        JOIN users u ON s.user_id = u.id
        LEFT JOIN academic_sessions asess ON fp.session_id = asess.id
        LEFT JOIN terms t ON fp.term_id = t.id
        WHERE fp.student_id IN ($placeholders)
        ORDER BY fp.created_at DESC
        LIMIT 20
    ");
    $stmt->execute($ward_ids);
    $payment_history = $stmt->fetchAll();
}

// Get fee summary for each ward
$fee_summaries = [];
foreach ($wards as $ward) {
    // Get student level to determine expected fees
    $stmt = $conn->prepare("
        SELECT l.level_code
        FROM students s
        JOIN classes c ON s.class_id = c.id
        JOIN levels l ON c.level_id = l.id
        WHERE s.id = ?
    ");
    $stmt->execute([$ward['student_id']]);
    $level_data = $stmt->fetch();

    // Get total expected fees based on level
    $stmt = $conn->prepare("
        SELECT SUM(CASE WHEN ? = 'JS' THEN amount_js ELSE amount_ss END) as expected_fees
        FROM fee_types
        WHERE is_active = 1 AND is_mandatory = 1
    ");
    $stmt->execute([$level_data['level_code'] ?? 'JS']);
    $expected_data = $stmt->fetch();

    // Get paid and pending amounts
    $stmt = $conn->prepare("
        SELECT
            COALESCE(SUM(CASE WHEN status = 'Confirmed' THEN amount_paid ELSE 0 END), 0) as paid_amount,
            COALESCE(SUM(CASE WHEN status = 'Pending' THEN amount_paid ELSE 0 END), 0) as pending_amount
        FROM fee_payments
        WHERE student_id = ?
    ");
    $stmt->execute([$ward['student_id']]);
    $payment_data = $stmt->fetch();

    $total_fees = $expected_data['expected_fees'] ?? 0;
    $paid_amount = $payment_data['paid_amount'] ?? 0;
    $pending_amount = $payment_data['pending_amount'] ?? 0;

    $fee_summaries[$ward['student_id']] = [
        'total_fees' => $total_fees,
        'paid_amount' => $paid_amount,
        'pending_amount' => $pending_amount,
        'outstanding' => max(0, $total_fees - $paid_amount)
    ];
}

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();
$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fee Payments - <?php echo $school_info['name']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 50%, #ff4500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }
        
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .main-container {
            padding: 20px;
            position: relative;
            z-index: 1;
        }
        
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff8c00, #ff6b35);
        }
        
        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff8c00, #ff6b35);
        }
        
        .fee-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .fee-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #28a745, #20c997);
        }
        
        .fee-card:hover {
            transform: translateY(-5px) scale(1.01);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .btn-orange {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            border: none;
            color: white;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 140, 0, 0.3);
        }
        
        .btn-orange:hover {
            background: linear-gradient(135deg, #ff9500 0%, #ff7635 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 140, 0, 0.4);
        }

        .btn-action {
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 2px;
        }

        .btn-action:hover {
            transform: translateY(-2px);
        }

        .fee-amount {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ff8c00;
        }

        .fee-status {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-confirmed {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-outstanding {
            background: #f8d7da;
            color: #721c24;
        }

        .payment-history-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #28a745;
            transition: all 0.3s ease;
        }

        .payment-history-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .payment-history-item.pending {
            border-left-color: #ffc107;
        }

        .payment-history-item.rejected {
            border-left-color: #dc3545;
        }

        .receipt-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            background: rgba(248, 249, 250, 0.5);
        }

        .receipt-upload-area:hover {
            border-color: #ff8c00;
            background: rgba(255, 140, 0, 0.05);
        }

        .receipt-upload-area.dragover {
            border-color: #ff6b35;
            background: rgba(255, 107, 53, 0.1);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 3rem;
            color: #ff8c00;
            margin-bottom: 15px;
        }

        .file-info {
            background: rgba(255, 140, 0, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }

        .preview-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-users me-2 text-orange"></i>
                IGSS Parent Portal
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle fw-semibold" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($parent['first_name'] . ' ' . $parent['last_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="index.php"><i class="fas fa-home me-2"></i>Dashboard</a></li>
                        <li><a class="dropdown-item" href="ward-management.php"><i class="fas fa-users me-2"></i>Ward Management</a></li>
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
                        <li><a class="dropdown-item" href="messages.php"><i class="fas fa-envelope me-2"></i>Messages</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-2">
                            <i class="fas fa-credit-card me-2"></i>Fee Payments
                        </h2>
                        <p class="text-muted mb-0">
                            Manage fee payments for all your wards
                        </p>
                    </div>
                    <div>
                        <a href="index.php" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                        <button class="btn btn-orange" data-bs-toggle="modal" data-bs-target="#makePaymentModal">
                            <i class="fas fa-plus me-2"></i>Make Payment
                        </button>
                    </div>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($wards)): ?>
                <!-- Fee Summary for Each Ward -->
                <div class="content-card">
                    <h5 class="mb-4">
                        <i class="fas fa-chart-pie me-2"></i>Fee Summary by Ward
                    </h5>

                    <div class="row">
                        <?php foreach ($wards as $ward): ?>
                            <?php $summary = $fee_summaries[$ward['student_id']]; ?>
                            <div class="col-lg-6 col-md-12">
                                <div class="fee-card">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div>
                                            <h6 class="mb-1">
                                                <?php echo htmlspecialchars($ward['first_name'] . ' ' . $ward['last_name']); ?>
                                            </h6>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($ward['admission_number']); ?> -
                                                <?php echo htmlspecialchars($ward['class_name']); ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <?php if ($summary['outstanding'] > 0): ?>
                                                <span class="fee-status status-outstanding">Outstanding</span>
                                            <?php else: ?>
                                                <span class="fee-status status-confirmed">Paid Up</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="row text-center mb-3">
                                        <div class="col-3">
                                            <div class="fee-amount">₦<?php echo number_format($summary['total_fees'], 0); ?></div>
                                            <small class="text-muted">Total Fees</small>
                                        </div>
                                        <div class="col-3">
                                            <div class="fee-amount text-success">₦<?php echo number_format($summary['paid_amount'], 0); ?></div>
                                            <small class="text-muted">Paid</small>
                                        </div>
                                        <div class="col-3">
                                            <div class="fee-amount text-warning">₦<?php echo number_format($summary['pending_amount'], 0); ?></div>
                                            <small class="text-muted">Pending</small>
                                        </div>
                                        <div class="col-3">
                                            <div class="fee-amount text-danger">₦<?php echo number_format($summary['outstanding'], 0); ?></div>
                                            <small class="text-muted">Outstanding</small>
                                        </div>
                                    </div>

                                    <div class="progress mb-3" style="height: 8px;">
                                        <?php
                                        $paid_percentage = $summary['total_fees'] > 0 ? ($summary['paid_amount'] / $summary['total_fees']) * 100 : 0;
                                        $pending_percentage = $summary['total_fees'] > 0 ? ($summary['pending_amount'] / $summary['total_fees']) * 100 : 0;
                                        ?>
                                        <div class="progress-bar bg-success" style="width: <?php echo $paid_percentage; ?>%"></div>
                                        <div class="progress-bar bg-warning" style="width: <?php echo $pending_percentage; ?>%"></div>
                                    </div>

                                    <div class="d-flex gap-2">
                                        <?php if ($summary['outstanding'] > 0): ?>
                                            <button class="btn btn-orange btn-sm btn-action"
                                                    onclick="makePayment(<?php echo $ward['student_id']; ?>, '<?php echo htmlspecialchars($ward['first_name'] . ' ' . $ward['last_name']); ?>', <?php echo $summary['outstanding']; ?>)">
                                                <i class="fas fa-credit-card me-1"></i>Pay Now
                                            </button>
                                        <?php endif; ?>
                                        <a href="payment-history.php?student_id=<?php echo $ward['student_id']; ?>"
                                           class="btn btn-outline-primary btn-sm btn-action">
                                            <i class="fas fa-history me-1"></i>View History
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Recent Payment History -->
                <div class="content-card">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>Recent Payment History
                        </h5>
                        <a href="payment-history.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>View All
                        </a>
                    </div>

                    <?php if (!empty($payment_history)): ?>
                        <?php foreach ($payment_history as $payment): ?>
                            <div class="payment-history-item <?php echo strtolower($payment['status']); ?>">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">
                                            <?php echo htmlspecialchars($payment['first_name'] . ' ' . $payment['last_name']); ?>
                                            <small class="text-muted">
                                                (<?php echo htmlspecialchars($payment['admission_number']); ?>)
                                            </small>
                                        </h6>
                                        <p class="mb-1">
                                            <strong>Amount:</strong> ₦<?php echo number_format($payment['amount_paid'], 2); ?>
                                        </p>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            <?php echo date('M d, Y', strtotime($payment['payment_date'])); ?>
                                            <?php if ($payment['payment_method']): ?>
                                                | <i class="fas fa-credit-card me-1"></i>
                                                <?php echo htmlspecialchars($payment['payment_method']); ?>
                                            <?php endif; ?>
                                            <?php if ($payment['bank_name']): ?>
                                                | <?php echo htmlspecialchars($payment['bank_name']); ?>
                                            <?php endif; ?>
                                            <?php if ($payment['receipt_image']): ?>
                                                | <a href="../<?php echo htmlspecialchars($payment['receipt_image']); ?>"
                                                     target="_blank" class="text-primary">
                                                    <i class="fas fa-file-image me-1"></i>View Receipt
                                                </a>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="fee-status status-<?php echo strtolower($payment['status']); ?>">
                                            <?php echo htmlspecialchars($payment['status']); ?>
                                        </span>
                                        <br>
                                        <small class="text-muted">
                                            <?php echo date('M d, Y', strtotime($payment['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-history fa-2x mb-3"></i>
                            <p>No payment history available</p>
                        </div>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <div class="content-card text-center">
                    <i class="fas fa-users fa-4x text-muted mb-4"></i>
                    <h4 class="text-muted mb-3">No Wards Linked</h4>
                    <p class="text-muted mb-4">
                        You need to link at least one ward before you can make fee payments.
                    </p>
                    <a href="index.php" class="btn btn-orange btn-lg">
                        <i class="fas fa-plus me-2"></i>Link Your First Ward
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Make Payment Modal -->
    <div class="modal fade" id="makePaymentModal" tabindex="-1" aria-labelledby="makePaymentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="makePaymentModalLabel">
                        <i class="fas fa-credit-card me-2"></i>Make Fee Payment
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" name="submit_payment" value="1">

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Payment Instructions:</strong> Fill in the payment details below and upload your payment receipt/evidence.
                            Your payment will be reviewed by the administration before confirmation.
                        </div>

                        <!-- School Account Information -->
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-university me-2"></i>School Account Details</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Account Name:</strong> Intellectual Giant Secondary School<br>
                                    <strong>Bank:</strong> First Bank Nigeria<br>
                                    <strong>Account Number:</strong> **********
                                </div>
                                <div class="col-md-6">
                                    <strong>Account Name:</strong> IGSS Development Fund<br>
                                    <strong>Bank:</strong> GTBank<br>
                                    <strong>Account Number:</strong> **********
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="student_id" class="form-label">
                                        <i class="fas fa-user-graduate me-1"></i>Select Ward *
                                    </label>
                                    <select class="form-select" id="student_id" name="student_id" required>
                                        <option value="">Choose a ward...</option>
                                        <?php foreach ($wards as $ward): ?>
                                            <option value="<?php echo $ward['student_id']; ?>">
                                                <?php echo htmlspecialchars($ward['first_name'] . ' ' . $ward['last_name'] . ' (' . $ward['admission_number'] . ')'); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="amount_paid" class="form-label">
                                        <i class="fas fa-money-bill me-1"></i>Amount Paid *
                                    </label>
                                    <input type="number" class="form-control" id="amount_paid" name="amount_paid"
                                           step="0.01" min="1" placeholder="0.00" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="payment_method" class="form-label">
                                        <i class="fas fa-credit-card me-1"></i>Payment Method *
                                    </label>
                                    <select class="form-select" id="payment_method" name="payment_method" required>
                                        <option value="">Select method...</option>
                                        <option value="Bank Transfer">Bank Transfer</option>
                                        <option value="Bank Deposit">Bank Deposit</option>
                                        <option value="Online Payment">Online Payment</option>
                                        <option value="Cash">Cash</option>
                                        <option value="Cheque">Cheque</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="payment_date" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>Payment Date *
                                    </label>
                                    <input type="date" class="form-control" id="payment_date" name="payment_date"
                                           value="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bank_name" class="form-label">
                                        <i class="fas fa-university me-1"></i>Bank Name
                                    </label>
                                    <input type="text" class="form-control" id="bank_name" name="bank_name"
                                           placeholder="e.g., First Bank">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="teller_number" class="form-label">
                                        <i class="fas fa-receipt me-1"></i>Teller/Reference Number
                                    </label>
                                    <input type="text" class="form-control" id="teller_number" name="teller_number"
                                           placeholder="Transaction reference">
                                </div>
                            </div>
                        </div>

                        <!-- Receipt Upload -->
                        <div class="mb-3">
                            <label for="receipt_image" class="form-label">
                                <i class="fas fa-upload me-1"></i>Upload Payment Receipt/Evidence *
                            </label>

                            <div class="receipt-upload-area" id="uploadArea">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h6>Drag & Drop your receipt here</h6>
                                <p class="text-muted mb-3">or click to browse files</p>
                                <input type="file" class="form-control d-none" id="receipt_image" name="receipt_image"
                                       accept="image/*,.pdf" required>
                                <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('receipt_image').click()">
                                    <i class="fas fa-folder-open me-2"></i>Choose File
                                </button>
                                <div class="form-text mt-3">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Upload a clear image or PDF of your payment receipt, bank teller, or transfer confirmation.
                                    Maximum file size: 5MB. Supported formats: JPG, PNG, PDF.
                                </div>
                            </div>

                            <!-- File Info Display -->
                            <div id="fileInfo" class="file-info" style="display: none;">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div>
                                        <i class="fas fa-file me-2"></i>
                                        <span id="fileName"></span>
                                        <small class="text-muted ms-2" id="fileSize"></small>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Preview Area -->
                        <div id="receiptPreview" class="preview-container" style="display: none;">
                            <label class="form-label">
                                <i class="fas fa-eye me-1"></i>Receipt Preview:
                            </label>
                            <div class="text-center">
                                <img id="previewImage" src="" alt="Receipt Preview"
                                     style="max-width: 100%; max-height: 300px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                                <div id="previewPdf" style="display: none;">
                                    <i class="fas fa-file-pdf fa-4x text-danger mb-3"></i>
                                    <h6 class="text-muted">PDF file ready for upload</h6>
                                    <p class="mb-0">Click submit to upload your PDF receipt</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="submit" class="btn btn-orange">
                            <i class="fas fa-credit-card me-2"></i>Submit Payment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function makePayment(studentId, studentName, outstandingAmount) {
            // Pre-fill the modal with student information
            document.getElementById('student_id').value = studentId;
            document.getElementById('amount_paid').value = outstandingAmount;

            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('makePaymentModal'));
            modal.show();
        }

        // File upload functionality
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('receipt_image');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const previewDiv = document.getElementById('receiptPreview');
        const previewImage = document.getElementById('previewImage');
        const previewPdf = document.getElementById('previewPdf');

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect(files[0]);
            }
        });

        // File input change handler
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                handleFileSelect(file);
            }
        });

        // Handle file selection
        function handleFileSelect(file) {
            // Show file info
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.style.display = 'block';

            // Show preview
            previewDiv.style.display = 'block';

            if (file.type.startsWith('image/')) {
                previewImage.style.display = 'block';
                previewPdf.style.display = 'none';

                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImage.src = e.target.result;
                };
                reader.readAsDataURL(file);
            } else if (file.type === 'application/pdf') {
                previewImage.style.display = 'none';
                previewPdf.style.display = 'block';
            }
        }

        // Remove file function
        function removeFile() {
            fileInput.value = '';
            fileInfo.style.display = 'none';
            previewDiv.style.display = 'none';
        }

        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Enhanced hover effects
        document.addEventListener('DOMContentLoaded', function() {
            const feeCards = document.querySelectorAll('.fee-card');

            feeCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Add click animations to action buttons
            const actionButtons = document.querySelectorAll('.btn-action');
            actionButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // Form validation
            const paymentForm = document.querySelector('#makePaymentModal form');
            if (paymentForm) {
                paymentForm.addEventListener('submit', function(e) {
                    const amount = parseFloat(document.getElementById('amount_paid').value);
                    const receiptFile = document.getElementById('receipt_image').files[0];

                    if (amount <= 0) {
                        e.preventDefault();
                        alert('Please enter a valid payment amount.');
                        return false;
                    }

                    if (!receiptFile) {
                        e.preventDefault();
                        alert('Please upload a payment receipt or evidence.');
                        return false;
                    }

                    // Validate file size (5MB)
                    if (receiptFile.size > 5 * 1024 * 1024) {
                        e.preventDefault();
                        alert('File size too large. Maximum size is 5MB.');
                        return false;
                    }

                    // Validate file type
                    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
                    if (!allowedTypes.includes(receiptFile.type)) {
                        e.preventDefault();
                        alert('Invalid file type. Please upload JPG, PNG, or PDF files only.');
                        return false;
                    }

                    // Show loading state
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...';
                    submitBtn.disabled = true;

                    // Re-enable after 10 seconds as fallback (longer for file upload)
                    setTimeout(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }, 10000);
                });
            }
        });
    </script>
</body>
</html>
