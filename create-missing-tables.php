<?php
/**
 * Create Missing Tables Script
 * This script creates the missing tables needed for the admin panel
 */

require_once 'config/database.php';

echo "🔧 Creating missing tables for IGSS Management System...\n\n";

try {
    $conn = getDBConnection();
    
    // Check and create teacher_subjects table
    $stmt = $conn->query("SHOW TABLES LIKE 'teacher_subjects'");
    if ($stmt->rowCount() == 0) {
        echo "📋 Creating teacher_subjects table...\n";
        $conn->exec("
            CREATE TABLE teacher_subjects (
                id INT PRIMARY KEY AUTO_INCREMENT,
                teacher_id INT NOT NULL,
                subject_id INT NOT NULL,
                class_id INT NOT NULL,
                session_id INT NOT NULL DEFAULT 1,
                assigned_by INT NOT NULL,
                assigned_date DATE NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_teacher_subjects_teacher (teacher_id),
                INDEX idx_teacher_subjects_class (class_id),
                UNIQUE KEY unique_teacher_subject_class (teacher_id, subject_id, class_id, session_id)
            )
        ");
        echo "✅ teacher_subjects table created\n";
    } else {
        echo "✅ teacher_subjects table already exists\n";
    }
    
    // Check and create form_teachers table
    $stmt = $conn->query("SHOW TABLES LIKE 'form_teachers'");
    if ($stmt->rowCount() == 0) {
        echo "📋 Creating form_teachers table...\n";
        $conn->exec("
            CREATE TABLE form_teachers (
                id INT PRIMARY KEY AUTO_INCREMENT,
                teacher_id INT NOT NULL,
                class_id INT NOT NULL,
                session_id INT NOT NULL DEFAULT 1,
                assigned_by INT NOT NULL,
                assigned_date DATE NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_form_teachers_teacher (teacher_id),
                INDEX idx_form_teachers_class (class_id),
                UNIQUE KEY unique_class_session (class_id, session_id)
            )
        ");
        echo "✅ form_teachers table created\n";
    } else {
        echo "✅ form_teachers table already exists\n";
    }
    
    // Check and create fee_types table
    $stmt = $conn->query("SHOW TABLES LIKE 'fee_types'");
    if ($stmt->rowCount() == 0) {
        echo "📋 Creating fee_types table...\n";
        $conn->exec("
            CREATE TABLE fee_types (
                id INT PRIMARY KEY AUTO_INCREMENT,
                fee_name VARCHAR(100) NOT NULL,
                fee_description TEXT,
                amount_js DECIMAL(10,2) NOT NULL,
                amount_ss DECIMAL(10,2) NOT NULL,
                is_mandatory BOOLEAN DEFAULT TRUE,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_fee_types_active (is_active)
            )
        ");
        
        // Insert default fee types
        $conn->exec("
            INSERT INTO fee_types (fee_name, fee_description, amount_js, amount_ss, is_mandatory, created_by) VALUES
            ('School Fee', 'Main school fee for academic session', 150000.00, 200000.00, TRUE, 1),
            ('Development Levy', 'School development and infrastructure fee', 25000.00, 30000.00, TRUE, 1),
            ('Sports Fee', 'Sports and recreational activities fee', 5000.00, 7500.00, FALSE, 1),
            ('Library Fee', 'Library maintenance and book fee', 3000.00, 5000.00, FALSE, 1),
            ('Laboratory Fee', 'Science laboratory equipment and materials', 10000.00, 15000.00, TRUE, 1),
            ('Computer Fee', 'Computer lab and ICT training fee', 8000.00, 12000.00, FALSE, 1),
            ('Examination Fee', 'Internal and external examination fee', 15000.00, 20000.00, TRUE, 1)
        ");
        echo "✅ fee_types table created with default data\n";
    } else {
        echo "✅ fee_types table already exists\n";
    }
    
    echo "\n🎉 All missing tables have been created successfully!\n";
    echo "🌐 You can now use all admin panel features.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "💡 Make sure the database is properly set up and accessible.\n";
}

echo "\n✨ Setup complete!\n";
?>
