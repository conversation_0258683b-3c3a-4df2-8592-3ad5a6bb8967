-- Electronic Management System for Intellectual Giant Secondary School
-- Database Schema

-- Create database
CREATE DATABASE IF NOT EXISTS igss_management;
USE igss_management;

-- Academic Sessions Table
CREATE TABLE academic_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_name VARCHAR(20) NOT NULL UNIQUE, -- e.g., '2024/2025'
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Terms Table
CREATE TABLE terms (
    id INT PRIMARY KEY AUTO_INCREMENT,
    term_name VARCHAR(20) NOT NULL, -- 'First Term', 'Second Term', 'Third Term'
    term_number INT NOT NULL CHECK (term_number IN (1, 2, 3)),
    session_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES academic_sessions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_term_session (term_number, session_id)
);

-- Levels Table
CREATE TABLE levels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    level_name VARCHAR(50) NOT NULL, -- 'Junior Secondary', 'Senior Secondary'
    level_code VARCHAR(10) NOT NULL UNIQUE, -- 'JS', 'SS'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Classes Table
CREATE TABLE classes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    class_name VARCHAR(20) NOT NULL, -- 'JSS1', 'JSS2', 'JSS3', 'SS1', 'SS2', 'SS3'
    level_id INT NOT NULL,
    class_type ENUM('General', 'Science', 'Art') DEFAULT 'General',
    capacity INT DEFAULT 40,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (level_id) REFERENCES levels(id) ON DELETE CASCADE,
    UNIQUE KEY unique_class_level (class_name, level_id, class_type)
);

-- Subjects Table
CREATE TABLE subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    subject_name VARCHAR(100) NOT NULL,
    subject_code VARCHAR(10) NOT NULL UNIQUE,
    level_id INT NOT NULL,
    class_type ENUM('General', 'Science', 'Art', 'All') DEFAULT 'All',
    is_core BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (level_id) REFERENCES levels(id) ON DELETE CASCADE
);

-- Security Questions Table
CREATE TABLE security_questions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    question TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Users Table (Base table for all user types)
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_code VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    middle_name VARCHAR(50),
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    address TEXT,
    date_of_birth DATE,
    gender ENUM('Male', 'Female') NOT NULL,
    user_type ENUM('admin', 'teacher', 'student', 'parent') NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_approved BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    profile_image VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- User Security Answers Table
CREATE TABLE user_security_answers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    question_id INT NOT NULL,
    answer_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES security_questions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_question (user_id, question_id)
);

-- Teachers Table (Extended information for teachers)
CREATE TABLE teachers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL UNIQUE,
    employee_id VARCHAR(20) NOT NULL UNIQUE,
    qualification VARCHAR(255),
    specialization VARCHAR(100),
    hire_date DATE,
    salary DECIMAL(10,2),
    is_form_teacher BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Students Table (Extended information for students)
CREATE TABLE students (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL UNIQUE,
    admission_number VARCHAR(20) NOT NULL UNIQUE,
    class_id INT NOT NULL,
    admission_date DATE NOT NULL,
    guardian_name VARCHAR(100),
    guardian_phone VARCHAR(20),
    guardian_email VARCHAR(100),
    emergency_contact VARCHAR(20),
    medical_info TEXT,
    is_boarding BOOLEAN DEFAULT FALSE,
    current_session_id INT,
    current_term_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE RESTRICT,
    FOREIGN KEY (current_session_id) REFERENCES academic_sessions(id) ON DELETE SET NULL,
    FOREIGN KEY (current_term_id) REFERENCES terms(id) ON DELETE SET NULL
);

-- Parents Table (Extended information for parents/guardians)
CREATE TABLE parents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL UNIQUE,
    occupation VARCHAR(100),
    workplace VARCHAR(255),
    relationship_to_student VARCHAR(50), -- 'Father', 'Mother', 'Guardian', etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Parent-Student Relationships
CREATE TABLE parent_student_relationships (
    id INT PRIMARY KEY AUTO_INCREMENT,
    parent_id INT NOT NULL,
    student_id INT NOT NULL,
    relationship_type VARCHAR(50) NOT NULL, -- 'Father', 'Mother', 'Guardian', etc.
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES parents(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    UNIQUE KEY unique_parent_student (parent_id, student_id)
);

-- Class Assignments (Form Teachers)
CREATE TABLE class_assignments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    teacher_id INT NOT NULL,
    class_id INT NOT NULL,
    session_id INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    assigned_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES academic_sessions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_class_session (class_id, session_id)
);

-- Subject Assignments (Teachers to Subjects)
CREATE TABLE subject_assignments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    teacher_id INT NOT NULL,
    subject_id INT NOT NULL,
    class_id INT NOT NULL,
    session_id INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    assigned_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES academic_sessions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_subject_class_session (subject_id, class_id, session_id)
);

-- Attendance Table
CREATE TABLE attendance (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    class_id INT NOT NULL,
    session_id INT NOT NULL,
    term_id INT NOT NULL,
    attendance_date DATE NOT NULL,
    status ENUM('Present', 'Absent', 'Late', 'Excused') NOT NULL,
    marked_by INT NOT NULL, -- teacher_id
    remarks TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES academic_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (marked_by) REFERENCES teachers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_student_date (student_id, attendance_date)
);

-- Student Grades Table
CREATE TABLE student_grades (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    class_id INT NOT NULL,
    session_id INT NOT NULL,
    term_id INT NOT NULL,
    first_test DECIMAL(5,2) DEFAULT 0,
    second_test DECIMAL(5,2) DEFAULT 0,
    exam_score DECIMAL(5,2) DEFAULT 0,
    total_score DECIMAL(5,2) GENERATED ALWAYS AS (first_test + second_test + exam_score) STORED,
    grade CHAR(1) GENERATED ALWAYS AS (
        CASE
            WHEN (first_test + second_test + exam_score) >= 70 THEN 'A'
            WHEN (first_test + second_test + exam_score) >= 60 THEN 'B'
            WHEN (first_test + second_test + exam_score) >= 50 THEN 'C'
            WHEN (first_test + second_test + exam_score) >= 45 THEN 'D'
            WHEN (first_test + second_test + exam_score) >= 40 THEN 'E'
            ELSE 'F'
        END
    ) STORED,
    remark VARCHAR(20) GENERATED ALWAYS AS (
        CASE
            WHEN (first_test + second_test + exam_score) >= 70 THEN 'Excellent'
            WHEN (first_test + second_test + exam_score) >= 60 THEN 'Very Good'
            WHEN (first_test + second_test + exam_score) >= 50 THEN 'Good'
            WHEN (first_test + second_test + exam_score) >= 45 THEN 'Pass'
            WHEN (first_test + second_test + exam_score) >= 40 THEN 'Fair'
            ELSE 'Fail'
        END
    ) STORED,
    teacher_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES academic_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_student_subject_term (student_id, subject_id, term_id)
);

-- Student Offenses Table
CREATE TABLE student_offenses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    offense_type VARCHAR(100) NOT NULL,
    offense_description TEXT NOT NULL,
    offense_date DATE NOT NULL,
    reported_by INT NOT NULL, -- teacher_id
    action_taken TEXT,
    severity ENUM('Minor', 'Major', 'Serious') NOT NULL,
    session_id INT NOT NULL,
    term_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (reported_by) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES academic_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE
);

-- Fee Payments Table
CREATE TABLE fee_payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    session_id INT NOT NULL,
    term_id INT NOT NULL,
    amount_paid DECIMAL(10,2) NOT NULL,
    payment_date DATE NOT NULL,
    payment_method VARCHAR(50) NOT NULL, -- 'Bank Transfer', 'Cash', 'Cheque'
    bank_name VARCHAR(100),
    teller_number VARCHAR(50),
    receipt_image VARCHAR(255), -- uploaded receipt/teller
    uploaded_by INT NOT NULL, -- parent_id
    confirmed_by INT, -- admin user_id
    confirmation_date DATETIME,
    status ENUM('Pending', 'Confirmed', 'Rejected') DEFAULT 'Pending',
    admin_remarks TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES academic_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES parents(id) ON DELETE CASCADE,
    FOREIGN KEY (confirmed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Student Promotions Table
CREATE TABLE student_promotions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    from_class_id INT NOT NULL,
    to_class_id INT,
    session_id INT NOT NULL,
    promotion_status ENUM('Promoted', 'Repeated', 'Pending') NOT NULL,
    average_score DECIMAL(5,2),
    total_subjects INT,
    failed_subjects INT,
    promoted_by INT NOT NULL, -- teacher_id (form teacher)
    promotion_date DATE NOT NULL,
    remarks TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (from_class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (to_class_id) REFERENCES classes(id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES academic_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (promoted_by) REFERENCES teachers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_student_session_promotion (student_id, session_id)
);

-- Teacher Subject Assignments Table
CREATE TABLE teacher_subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    teacher_id INT NOT NULL,
    subject_id INT NOT NULL,
    class_id INT NOT NULL,
    session_id INT NOT NULL,
    assigned_by INT NOT NULL, -- admin user_id
    assigned_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES academic_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_teacher_subject_class (teacher_id, subject_id, class_id, session_id)
);

-- Form Teachers Table
CREATE TABLE form_teachers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    teacher_id INT NOT NULL,
    class_id INT NOT NULL,
    session_id INT NOT NULL,
    assigned_by INT NOT NULL, -- admin user_id
    assigned_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES academic_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_class_session (class_id, session_id)
);

-- Fee Types Table
CREATE TABLE fee_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    fee_name VARCHAR(100) NOT NULL,
    fee_description TEXT,
    amount_js DECIMAL(10,2) NOT NULL, -- Amount for Junior Secondary
    amount_ss DECIMAL(10,2) NOT NULL, -- Amount for Senior Secondary
    is_mandatory BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- System Settings Table
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_by INT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert Initial Data

-- Insert Levels
INSERT INTO levels (level_name, level_code) VALUES
('Junior Secondary', 'JS'),
('Senior Secondary', 'SS');

-- Insert Classes
INSERT INTO classes (class_name, level_id, class_type) VALUES
('JSS1', 1, 'General'),
('JSS2', 1, 'General'),
('JSS3', 1, 'General'),
('SS1', 2, 'Science'),
('SS1', 2, 'Art'),
('SS2', 2, 'Science'),
('SS2', 2, 'Art'),
('SS3', 2, 'Science'),
('SS3', 2, 'Art');

-- Insert Junior Secondary Subjects
INSERT INTO subjects (subject_name, subject_code, level_id, class_type, is_core) VALUES
('Mathematics', 'MATH', 1, 'All', TRUE),
('English Language', 'ENG', 1, 'All', TRUE),
('Social Studies', 'SOC', 1, 'All', TRUE),
('Business Studies', 'BUS', 1, 'All', TRUE),
('Agricultural Science', 'AGR', 1, 'All', TRUE),
('Basic Science', 'BSC', 1, 'All', TRUE),
('Home Economics', 'HEC', 1, 'All', TRUE),
('Christian Religious Studies', 'CRS', 1, 'All', TRUE),
('Basic Technology', 'BTE', 1, 'All', TRUE),
('Civic Education', 'CIV', 1, 'All', TRUE),
('Computer Studies', 'COM', 1, 'All', TRUE);

-- Insert Senior Secondary Science Subjects
INSERT INTO subjects (subject_name, subject_code, level_id, class_type, is_core) VALUES
('Mathematics', 'MATH_SS', 2, 'Science', TRUE),
('English Language', 'ENG_SS', 2, 'Science', TRUE),
('Further Mathematics', 'FMATH', 2, 'Science', TRUE),
('Physics', 'PHY', 2, 'Science', TRUE),
('Chemistry', 'CHE', 2, 'Science', TRUE),
('Economics', 'ECO_SC', 2, 'Science', TRUE),
('Agricultural Science', 'AGR_SS', 2, 'Science', TRUE),
('Geography', 'GEO', 2, 'Science', TRUE),
('Biology', 'BIO', 2, 'Science', TRUE);

-- Insert Senior Secondary Art Subjects
INSERT INTO subjects (subject_name, subject_code, level_id, class_type, is_core) VALUES
('Mathematics', 'MATH_ART', 2, 'Art', TRUE),
('English Language', 'ENG_ART', 2, 'Art', TRUE),
('Commerce', 'COMM', 2, 'Art', TRUE),
('Financial Accounting', 'ACC', 2, 'Art', TRUE),
('Literature in English', 'LIT', 2, 'Art', TRUE),
('Government', 'GOV', 2, 'Art', TRUE),
('Economics', 'ECO_ART', 2, 'Art', TRUE);

-- Insert Security Questions
INSERT INTO security_questions (question) VALUES
('What is your mother\'s maiden name?'),
('What was the name of your first pet?'),
('In which city were you born?'),
('What is your favorite color?'),
('What was the name of your primary school?'),
('What is your favorite food?'),
('What was your childhood nickname?'),
('What is the name of your best friend?');

-- Create Default Admin User
INSERT INTO users (user_code, first_name, last_name, email, gender, user_type, password_hash, is_approved, is_active)
VALUES ('ADMIN001', 'System', 'Administrator', '<EMAIL>', 'Male', 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', TRUE, TRUE);

-- Insert Initial Fee Types
INSERT INTO fee_types (fee_name, fee_description, amount_js, amount_ss, is_mandatory, created_by) VALUES
('School Fee', 'Main school fee for academic session', 150000.00, 200000.00, TRUE, 1),
('Development Levy', 'School development and infrastructure fee', 25000.00, 30000.00, TRUE, 1),
('Sports Fee', 'Sports and recreational activities fee', 5000.00, 7500.00, FALSE, 1),
('Library Fee', 'Library maintenance and book fee', 3000.00, 5000.00, FALSE, 1),
('Laboratory Fee', 'Science laboratory equipment and materials', 10000.00, 15000.00, TRUE, 1),
('Computer Fee', 'Computer lab and ICT training fee', 8000.00, 12000.00, FALSE, 1),
('Examination Fee', 'Internal and external examination fee', 15000.00, 20000.00, TRUE, 1);

-- Insert Initial System Settings
INSERT INTO system_settings (setting_key, setting_value, description, updated_by) VALUES
('school_name', 'Intellectual Giant Secondary School', 'Official school name', 1),
('school_address', 'Port-Harcourt, Rivers State, Nigeria', 'School address', 1),
('school_phone', '+234 ************', 'School contact phone', 1),
('school_email', '<EMAIL>', 'School contact email', 1),
('current_session', '2024/2025', 'Current academic session', 1),
('current_term', '1', 'Current term (1, 2, or 3)', 1),
('school_logo', 'assets/images/logo.png', 'Path to school logo', 1),
('fee_amount_js', '150000', 'School fee for Junior Secondary (Naira)', 1),
('fee_amount_ss', '200000', 'School fee for Senior Secondary (Naira)', 1),
('promotion_pass_mark', '40', 'Minimum average for promotion', 1);

-- Create Indexes for Performance
CREATE INDEX idx_users_user_type ON users(user_type);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_user_code ON users(user_code);
CREATE INDEX idx_students_admission_number ON students(admission_number);
CREATE INDEX idx_students_class_id ON students(class_id);
CREATE INDEX idx_attendance_date ON attendance(attendance_date);
CREATE INDEX idx_attendance_student_date ON attendance(student_id, attendance_date);
CREATE INDEX idx_grades_student_term ON student_grades(student_id, term_id);
CREATE INDEX idx_grades_subject_class ON student_grades(subject_id, class_id);
CREATE INDEX idx_fee_payments_student ON fee_payments(student_id);
CREATE INDEX idx_fee_payments_status ON fee_payments(status);
CREATE INDEX idx_teacher_subjects_teacher ON teacher_subjects(teacher_id);
CREATE INDEX idx_teacher_subjects_class ON teacher_subjects(class_id);
CREATE INDEX idx_form_teachers_teacher ON form_teachers(teacher_id);
CREATE INDEX idx_form_teachers_class ON form_teachers(class_id);
CREATE INDEX idx_fee_types_active ON fee_types(is_active);
