<?php
/**
 * Reports Page for IGSS Management System
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
requireLogin();
if (!hasRole('admin')) {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$current_academic = getCurrentAcademicInfo();

// Get report data
$reports_data = [];

// Set default session ID for queries that need it
$current_session_id = 1;

// User Statistics
try {
    $stmt = $conn->prepare("
        SELECT user_type,
               COUNT(*) as total,
               SUM(CASE WHEN is_approved = 1 THEN 1 ELSE 0 END) as approved,
               SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active
        FROM users
        WHERE user_type != 'admin'
        GROUP BY user_type
    ");
    $stmt->execute();
    $reports_data['users'] = $stmt->fetchAll();
} catch (Exception $e) {
    $reports_data['users'] = [];
}

// Fee Payment Statistics
try {
    $stmt = $conn->prepare("
        SELECT 'School Fee' as fee_type_name,
               COUNT(fp.id) as payment_count,
               SUM(fp.amount_paid) as total_amount,
               SUM(CASE WHEN fp.status = 'Confirmed' THEN fp.amount_paid ELSE 0 END) as confirmed_amount,
               SUM(CASE WHEN fp.status = 'Pending' THEN fp.amount_paid ELSE 0 END) as pending_amount,
               SUM(CASE WHEN fp.status = 'Rejected' THEN fp.amount_paid ELSE 0 END) as rejected_amount
        FROM fee_payments fp
        JOIN academic_sessions acs ON fp.session_id = acs.id
        WHERE acs.session_name = ?
    ");
    $stmt->execute([$current_academic['session']]);
    $reports_data['fees'] = $stmt->fetchAll();
} catch (Exception $e) {
    $reports_data['fees'] = [];
}

// Class Statistics
try {
    $stmt = $conn->prepare("
        SELECT c.class_name, l.level_name,
               COUNT(s.id) as student_count,
               CONCAT(u.first_name, ' ', u.last_name) as form_teacher
        FROM classes c
        JOIN levels l ON c.level_id = l.id
        LEFT JOIN students s ON c.id = s.class_id
        LEFT JOIN form_teachers ft ON c.id = ft.class_id AND ft.session_id = ?
        LEFT JOIN users u ON ft.teacher_id = u.id
        GROUP BY c.id, c.class_name, l.level_name, u.first_name, u.last_name
        ORDER BY l.id, c.class_name
    ");
    $stmt->execute([$current_session_id]);
    $reports_data['classes'] = $stmt->fetchAll();
} catch (Exception $e) {
    $reports_data['classes'] = [];
}

// Subject Assignment Statistics
try {
    $stmt = $conn->prepare("
        SELECT s.subject_name, s.subject_code,
               COUNT(ts.id) as assignment_count,
               COUNT(DISTINCT ts.teacher_id) as teacher_count,
               COUNT(DISTINCT ts.class_id) as class_count
        FROM subjects s
        LEFT JOIN teacher_subjects ts ON s.id = ts.subject_id
        GROUP BY s.id, s.subject_name, s.subject_code
        ORDER BY s.subject_name
    ");
    $stmt->execute();
    $reports_data['subjects'] = $stmt->fetchAll();
} catch (Exception $e) {
    $reports_data['subjects'] = [];
}

// Recent Activities
try {
    $stmt = $conn->prepare("
        SELECT 'User Registration' as activity_type,
               CONCAT(first_name, ' ', last_name, ' (', user_type, ')') as description,
               created_at as activity_date
        FROM users
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)

        UNION ALL

        SELECT 'Fee Payment' as activity_type,
               CONCAT('Payment of ₦', FORMAT(amount_paid, 2), ' by ', u.first_name, ' ', u.last_name) as description,
               fp.created_at as activity_date
        FROM fee_payments fp
        JOIN students s ON fp.student_id = s.id
        JOIN users u ON s.user_id = u.id
        WHERE fp.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)

        ORDER BY activity_date DESC
        LIMIT 20
    ");
    $stmt->execute();
    $reports_data['activities'] = $stmt->fetchAll();
} catch (Exception $e) {
    $reports_data['activities'] = [];
}

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        body {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }

        .sidebar {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        /* Custom white scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.8);
        }

        /* For Firefox */
        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.6) rgba(255, 255, 255, 0.2);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 80px;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.25) 100%);
            border-radius: 60px 0 0 60px;
        }

        .sidebar-header {
            padding: 25px 20px; /* Reduced padding */
            text-align: center;
            position: relative;
            background: rgba(255, 255, 255, 0.01); /* Minimal opacity */
            border-radius: 0 0 30px 30px;
            margin-bottom: 15px; /* Reduced margin */
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 700;
            font-size: 1.4rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header small {
            opacity: 0.9;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            margin-bottom: 80px; /* Space for back to home button */
        }

        .sidebar-menu li {
            margin: 1px 15px; /* Minimal margin for tight fit */
        }

        .sidebar-menu a {
            color: white;
            text-decoration: none;
            padding: 8px 20px; /* Smaller padding like other pages */
            display: block;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 25px;
            position: relative;
            font-weight: 500; /* Normal font weight */
            font-size: 0.85rem; /* Smaller font size */
            background: rgba(255, 255, 255, 0.05); /* Very subtle background */
            margin-bottom: 1px; /* Minimal spacing */
        }

        .sidebar-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover::before, .sidebar-menu a.active::before {
            opacity: 1;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            transform: translateX(8px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.15);
        }

        .sidebar-menu a.active {
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .back-to-home {
            position: absolute;
            bottom: 60px; /* Moved higher up */
            left: 15px;
            right: 15px;
            z-index: 1001;
        }

        .back-to-home .btn {
            background: rgba(255, 255, 255, 0.3); /* Even more visible white background */
            border: 3px solid rgba(255, 255, 255, 0.6); /* Thicker, more visible border */
            color: white;
            font-weight: 800;
            padding: 12px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            width: 100%;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            font-size: 0.95rem;
        }

        .back-to-home .btn:hover {
            background: rgba(255, 255, 255, 0.3); /* More visible hover effect */
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
            position: relative;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px 30px;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff9a56, #ff6b35, #f7931e);
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 25px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff9a56, #ff6b35, #f7931e);
        }

        .report-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .report-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .activity-item {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .export-buttons {
            margin-bottom: 20px;
        }

        .export-buttons .btn {
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-graduation-cap me-2"></i>IGSS Admin</h4>
            <small>Management System</small>
        </div>

        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
            <li><a href="manage-users.php"><i class="fas fa-users me-2"></i>Manage Users</a></li>
            <li><a href="academic-settings.php"><i class="fas fa-calendar-alt me-2"></i>Academic Settings</a></li>
            <li><a href="assign-subjects.php"><i class="fas fa-book me-2"></i>Assign Subjects</a></li>
            <li><a href="assign-form-teachers.php"><i class="fas fa-chalkboard-teacher me-2"></i>Form Teachers</a></li>
            <li><a href="manage-fees.php"><i class="fas fa-money-bill me-2"></i>Manage Fees</a></li>
            <li><a href="fee-confirmations.php"><i class="fas fa-money-check-alt me-2"></i>Fee Confirmations</a></li>
            <li><a href="reports.php" class="active"><i class="fas fa-chart-bar me-2"></i>Reports</a></li>
            <li><a href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
            <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
        </ul>

        <!-- Back to Home Button -->
        <div class="back-to-home">
            <a href="../index.php" class="btn">
                <i class="fas fa-home me-2"></i>Back to Home
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><i class="fas fa-chart-bar me-2"></i>System Reports</h4>
                    <small class="text-muted">Comprehensive reports and analytics</small>
                </div>
                <div>
                    <span class="badge bg-primary me-2">Session: <?php echo $current_academic['session']; ?></span>
                    <span class="badge bg-info">Term: <?php echo TERMS[$current_academic['term']]; ?></span>
                </div>
            </div>
        </div>

        <!-- Export Buttons -->
        <div class="export-buttons">
            <button class="btn btn-success" onclick="exportReport('pdf')">
                <i class="fas fa-file-pdf me-2"></i>Export PDF
            </button>
            <button class="btn btn-primary" onclick="exportReport('excel')">
                <i class="fas fa-file-excel me-2"></i>Export Excel
            </button>
            <button class="btn btn-info" onclick="printReport()">
                <i class="fas fa-print me-2"></i>Print Report
            </button>
        </div>

        <!-- User Statistics -->
        <div class="report-card">
            <h5 class="mb-4"><i class="fas fa-users me-2"></i>User Statistics</h5>
            <div class="stats-grid">
                <?php foreach ($reports_data['users'] as $user_stat): ?>
                    <div class="stat-item">
                        <div class="stat-number"><?php echo $user_stat['total']; ?></div>
                        <div class="stat-label">Total <?php echo ucfirst($user_stat['user_type']); ?>s</div>
                        <small class="text-muted">
                            <?php echo $user_stat['approved']; ?> approved,
                            <?php echo $user_stat['active']; ?> active
                        </small>
                    </div>
                <?php endforeach; ?>
            </div>

            <div class="chart-container">
                <canvas id="userChart"></canvas>
            </div>
        </div>

        <!-- Fee Payment Statistics -->
        <div class="report-card">
            <h5 class="mb-4"><i class="fas fa-money-check-alt me-2"></i>Fee Payment Statistics (<?php echo $current_academic['session']; ?>)</h5>

            <?php if (!empty($reports_data['fees'])): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Fee Type</th>
                                <th>Total Payments</th>
                                <th>Total Amount</th>
                                <th>Confirmed</th>
                                <th>Pending</th>
                                <th>Rejected</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($reports_data['fees'] as $fee_stat): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($fee_stat['fee_type_name']); ?></td>
                                    <td><?php echo $fee_stat['payment_count'] ?? 0; ?></td>
                                    <td class="fw-bold text-success">₦<?php echo number_format($fee_stat['total_amount'] ?? 0, 2); ?></td>
                                    <td class="text-success">₦<?php echo number_format($fee_stat['confirmed_amount'] ?? 0, 2); ?></td>
                                    <td class="text-warning">₦<?php echo number_format($fee_stat['pending_amount'] ?? 0, 2); ?></td>
                                    <td class="text-danger">₦<?php echo number_format($fee_stat['rejected_amount'] ?? 0, 2); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <div class="chart-container">
                    <canvas id="feeChart"></canvas>
                </div>
            <?php else: ?>
                <div class="text-center text-muted py-4">
                    <i class="fas fa-money-check-alt fa-3x mb-3"></i>
                    <p>No fee payment data available for this session.</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Class Statistics -->
        <div class="report-card">
            <h5 class="mb-4"><i class="fas fa-school me-2"></i>Class Statistics</h5>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Class</th>
                            <th>Level</th>
                            <th>Students</th>
                            <th>Form Teacher</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($reports_data['classes'] as $class_stat): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($class_stat['class_name']); ?></td>
                                <td><span class="badge bg-primary"><?php echo htmlspecialchars($class_stat['level_name']); ?></span></td>
                                <td><span class="badge bg-success"><?php echo $class_stat['student_count']; ?> students</span></td>
                                <td>
                                    <?php if ($class_stat['form_teacher']): ?>
                                        <?php echo htmlspecialchars($class_stat['form_teacher']); ?>
                                    <?php else: ?>
                                        <span class="text-muted">Not assigned</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Subject Assignment Statistics -->
        <div class="report-card">
            <h5 class="mb-4"><i class="fas fa-book me-2"></i>Subject Assignment Statistics</h5>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Subject</th>
                            <th>Code</th>
                            <th>Assignments</th>
                            <th>Teachers</th>
                            <th>Classes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($reports_data['subjects'] as $subject_stat): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($subject_stat['subject_name']); ?></td>
                                <td><code><?php echo htmlspecialchars($subject_stat['subject_code']); ?></code></td>
                                <td><span class="badge bg-info"><?php echo $subject_stat['assignment_count']; ?></span></td>
                                <td><span class="badge bg-success"><?php echo $subject_stat['teacher_count']; ?></span></td>
                                <td><span class="badge bg-warning"><?php echo $subject_stat['class_count']; ?></span></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="report-card">
            <h5 class="mb-4"><i class="fas fa-clock me-2"></i>Recent Activities (Last 30 Days)</h5>
            <?php if (!empty($reports_data['activities'])): ?>
                <?php foreach ($reports_data['activities'] as $activity): ?>
                    <div class="activity-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong><?php echo htmlspecialchars($activity['activity_type']); ?></strong>
                                <p class="mb-0 text-muted"><?php echo htmlspecialchars($activity['description']); ?></p>
                            </div>
                            <small class="text-muted"><?php echo timeAgo($activity['activity_date']); ?></small>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="text-center text-muted py-4">
                    <i class="fas fa-clock fa-3x mb-3"></i>
                    <p>No recent activities to display.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // User Statistics Chart
        const userCtx = document.getElementById('userChart').getContext('2d');
        const userChart = new Chart(userCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    <?php foreach ($reports_data['users'] as $user_stat): ?>
                        '<?php echo ucfirst($user_stat['user_type']); ?>s',
                    <?php endforeach; ?>
                ],
                datasets: [{
                    data: [
                        <?php foreach ($reports_data['users'] as $user_stat): ?>
                            <?php echo $user_stat['total']; ?>,
                        <?php endforeach; ?>
                    ],
                    backgroundColor: ['#667eea', '#28a745', '#17a2b8', '#ffc107'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        <?php if (!empty($reports_data['fees'])): ?>
        // Fee Payment Chart
        const feeCtx = document.getElementById('feeChart').getContext('2d');
        const feeChart = new Chart(feeCtx, {
            type: 'bar',
            data: {
                labels: [
                    <?php foreach ($reports_data['fees'] as $fee_stat): ?>
                        '<?php echo htmlspecialchars($fee_stat['fee_type_name']); ?>',
                    <?php endforeach; ?>
                ],
                datasets: [{
                    label: 'Total Amount (₦)',
                    data: [
                        <?php foreach ($reports_data['fees'] as $fee_stat): ?>
                            <?php echo $fee_stat['total_amount'] ?? 0; ?>,
                        <?php endforeach; ?>
                    ],
                    backgroundColor: '#667eea',
                    borderColor: '#667eea',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₦' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
        <?php endif; ?>

        function exportReport(format) {
            alert('Export to ' + format.toUpperCase() + ' functionality will be implemented.');
        }

        function printReport() {
            window.print();
        }
    </script>
</body>
</html>
