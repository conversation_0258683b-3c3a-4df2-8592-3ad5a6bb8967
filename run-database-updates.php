<?php
/**
 * Database Update Script for Enhanced Grading System
 * IGSS Management System
 */

require_once 'config/database.php';

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_updates'])) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        // Check if the new columns already exist
        $stmt = $conn->query("DESCRIBE student_grades");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (!in_array('assessment_score', $columns)) {
            // Add new columns for enhanced grading system
            $sql = "
                ALTER TABLE student_grades
                ADD COLUMN assessment_score DECIMAL(5,2) DEFAULT 0 COMMENT 'Assessment score out of 40',
                ADD COLUMN exam_score_new DECIMAL(5,2) DEFAULT 0 COMMENT 'Exam score out of 60',
                ADD COLUMN teacher_remarks TEXT NULL COMMENT 'Teacher comments and feedback for the student'
            ";
            $conn->exec($sql);

            // Add computed columns for total score and grade
            $sql = "
                ALTER TABLE student_grades
                ADD COLUMN total_score DECIMAL(5,2) GENERATED ALWAYS AS (assessment_score + exam_score_new) STORED COMMENT 'Total score out of 100',
                ADD COLUMN grade CHAR(1) GENERATED ALWAYS AS (
                    CASE
                        WHEN (assessment_score + exam_score_new) >= 70 THEN 'A'
                        WHEN (assessment_score + exam_score_new) >= 60 THEN 'B'
                        WHEN (assessment_score + exam_score_new) >= 50 THEN 'C'
                        WHEN (assessment_score + exam_score_new) >= 45 THEN 'D'
                        WHEN (assessment_score + exam_score_new) >= 40 THEN 'E'
                        ELSE 'F'
                    END
                ) STORED COMMENT 'Letter grade based on total score',
                ADD COLUMN remark VARCHAR(20) GENERATED ALWAYS AS (
                    CASE
                        WHEN (assessment_score + exam_score_new) >= 70 THEN 'Excellent'
                        WHEN (assessment_score + exam_score_new) >= 60 THEN 'Very Good'
                        WHEN (assessment_score + exam_score_new) >= 50 THEN 'Good'
                        WHEN (assessment_score + exam_score_new) >= 45 THEN 'Pass'
                        WHEN (assessment_score + exam_score_new) >= 40 THEN 'Fair'
                        ELSE 'Fail'
                    END
                ) STORED COMMENT 'Grade remark based on total score'
            ";
            $conn->exec($sql);

            $message = 'Database updated successfully! Enhanced grading system is now active.';
        } else {
            $message = 'Enhanced grading system is already installed.';
        }

        // Create teacher comments table if it doesn't exist
        $sql = "
            CREATE TABLE IF NOT EXISTS teacher_comments (
                id INT PRIMARY KEY AUTO_INCREMENT,
                student_id INT NOT NULL,
                subject_id INT NOT NULL,
                class_id INT NOT NULL,
                session_id INT NOT NULL,
                term_id INT NOT NULL,
                teacher_id INT NOT NULL,
                comment_type ENUM('academic', 'behavioral', 'general') DEFAULT 'academic',
                comment_text TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
                FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
                FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
                FOREIGN KEY (session_id) REFERENCES academic_sessions(id) ON DELETE CASCADE,
                FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE,
                FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
                UNIQUE KEY unique_student_subject_term_comment (student_id, subject_id, term_id, comment_type)
            )
        ";
        $conn->exec($sql);

    } catch (Exception $e) {
        $error = 'Database update failed: ' . $e->getMessage();
    }
}

// Check current database status
$dbStatus = 'Not Connected';
$hasEnhancedGrading = false;

try {
    $database = new Database();
    if ($database->testConnection()) {
        $dbStatus = 'Connected';
        $conn = $database->getConnection();

        // Check if enhanced grading columns exist
        $stmt = $conn->query("DESCRIBE student_grades");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $hasEnhancedGrading = in_array('assessment_score', $columns);
    }
} catch (Exception $e) {
    $dbStatus = 'Error: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Updates - IGSS Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }
        .update-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 800px;
            margin: 0 auto;
        }
        .btn-update {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-update:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="update-container">
            <div class="text-center mb-4">
                <h2><i class="fas fa-database text-success"></i> Database Updates</h2>
                <p class="text-muted">Enhanced Grading System Installation</p>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h5>Database Status</h5>
                            <p class="mb-1"><strong>Connection:</strong> <?php echo $dbStatus; ?></p>
                            <p class="mb-0"><strong>Enhanced Grading:</strong>
                                <?php echo $hasEnhancedGrading ?
                                    '<span class="text-success">Installed</span>' :
                                    '<span class="text-warning">Not Installed</span>'; ?>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h5>What This Update Includes</h5>
                            <ul class="mb-0">
                                <li>Assessment (40) + Exam (60) scoring</li>
                                <li>Automatic grade calculation</li>
                                <li>Teacher remarks system</li>
                                <li>Enhanced reporting</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (!$hasEnhancedGrading): ?>
            <form method="POST" action="">
                <div class="text-center">
                    <button type="submit" name="run_updates" class="btn btn-update btn-lg">
                        <i class="fas fa-rocket me-2"></i>Install Enhanced Grading System
                    </button>
                </div>
            </form>
            <?php else: ?>
            <div class="text-center">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>Enhanced grading system is already installed and ready to use!
                </div>
                <a href="teacher/grades-new.php" class="btn btn-update">
                    <i class="fas fa-graduation-cap me-2"></i>Test Enhanced Grading
                </a>
            </div>
            <?php endif; ?>

            <div class="text-center mt-4">
                <a href="comprehensive-test.php" class="text-decoration-none me-3">
                    <i class="fas fa-vial"></i> Run System Tests
                </a>
                <a href="importdb.php" class="text-decoration-none me-3">
                    <i class="fas fa-database"></i> Database Import
                </a>
                <a href="login.php" class="text-decoration-none">
                    <i class="fas fa-sign-in-alt"></i> Login
                </a>
            </div>
        </div>
    </div>
</body>
</html>