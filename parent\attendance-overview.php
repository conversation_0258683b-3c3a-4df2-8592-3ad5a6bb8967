<?php
/**
 * Parent Attendance Overview
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a parent
if (!isLoggedIn() || $_SESSION['user_type'] !== 'parent') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get parent information
$stmt = $conn->prepare("
    SELECT p.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, u.user_code, u.is_approved
    FROM parents p
    JOIN users u ON p.user_id = u.id
    WHERE p.user_id = ?
");
$stmt->execute([$user_id]);
$parent = $stmt->fetch();

if (!$parent) {
    header('Location: ../login.php');
    exit();
}

// Check if parent account is approved
if (!$parent['is_approved']) {
    header('Location: pending-approval.php');
    exit();
}

// Get all linked wards with attendance data
$stmt = $conn->prepare("
    SELECT
        s.id as student_id,
        s.admission_number,
        u.first_name,
        u.last_name,
        u.middle_name,
        c.class_name,
        l.level_name,
        psr.relationship_type,
        psr.is_primary
    FROM parent_student_relationships psr
    JOIN students s ON psr.student_id = s.id
    JOIN users u ON s.user_id = u.id
    JOIN classes c ON s.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    WHERE psr.parent_id = ?
    ORDER BY psr.is_primary DESC, u.first_name ASC
");
$stmt->execute([$parent['id']]);
$wards = $stmt->fetchAll();

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();

// Function to generate sample attendance data (in real system, this would come from database)
function generateAttendanceData($student_id) {
    $total_days = 60; // Sample total school days
    $present_days = rand(50, 58); // Random present days for demo
    $absent_days = $total_days - $present_days;
    $percentage = round(($present_days / $total_days) * 100, 1);
    
    return [
        'total_days' => $total_days,
        'present_days' => $present_days,
        'absent_days' => $absent_days,
        'percentage' => $percentage,
        'recent_absences' => [
            ['date' => '2024-01-15', 'reason' => 'Sick'],
            ['date' => '2024-01-10', 'reason' => 'Family Emergency'],
            ['date' => '2024-01-05', 'reason' => 'Medical Appointment']
        ]
    ];
}

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Overview - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 50%, #ff4500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }

        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 1050;
        }

        .navbar-custom .dropdown-menu {
            z-index: 1060;
            position: absolute;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            margin-top: 8px;
            min-width: 220px;
        }

        .navbar-custom .dropdown-item {
            padding: 12px 20px;
            border-radius: 10px;
            margin: 4px 8px;
            transition: all 0.3s ease;
        }

        .navbar-custom .dropdown-item:hover {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            color: white;
            transform: translateX(5px);
        }

        .navbar-custom .dropdown-item i {
            width: 20px;
            text-align: center;
        }

        .main-container {
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .attendance-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .attendance-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff8c00, #ff6b35);
        }

        .attendance-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .ward-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            margin-right: 20px;
            box-shadow: 0 4px 15px rgba(255, 140, 0, 0.3);
        }

        .attendance-percentage {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .percentage-excellent {
            color: #28a745;
        }

        .percentage-good {
            color: #ffc107;
        }

        .percentage-poor {
            color: #dc3545;
        }

        .progress-custom {
            height: 12px;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
        }

        .progress-bar-custom {
            border-radius: 10px;
            transition: width 0.6s ease;
        }

        .breadcrumb {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 15px 20px;
            margin-bottom: 20px;
        }

        .breadcrumb-item a {
            color: white;
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: rgba(255, 255, 255, 0.8);
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .absence-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #dc3545;
        }

        .btn-orange {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            border: none;
            color: white;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 140, 0, 0.3);
        }

        .btn-orange:hover {
            background: linear-gradient(135deg, #ff9500 0%, #ff7635 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 140, 0, 0.4);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-users me-2 text-orange"></i>
                IGSS Parent Portal
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle fw-semibold" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($parent['first_name'] . ' ' . $parent['last_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="index.php"><i class="fas fa-home me-2"></i>Dashboard</a></li>
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
                        <li><a class="dropdown-item" href="ward-management.php"><i class="fas fa-users me-2"></i>Ward Management</a></li>
                        <li><a class="dropdown-item" href="payments.php"><i class="fas fa-credit-card me-2"></i>Fee Payments</a></li>
                        <li><a class="dropdown-item" href="messages.php"><i class="fas fa-envelope me-2"></i>Messages</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <div class="container">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php"><i class="fas fa-home me-1"></i>Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page"><i class="fas fa-calendar-check me-1"></i>Attendance Overview</li>
                </ol>
            </nav>

            <!-- Page Header -->
            <div class="attendance-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2">
                            <i class="fas fa-calendar-check me-2"></i>
                            Attendance Overview
                        </h3>
                        <p class="text-muted mb-0">
                            <i class="fas fa-calendar me-2"></i>
                            Academic Session: <?php echo $current_academic['session']; ?> | 
                            Term: <?php echo TERMS[$current_academic['term']]; ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="index.php" class="btn btn-orange">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <?php if (!empty($wards)): ?>
                <?php foreach ($wards as $ward): ?>
                    <?php $attendance = generateAttendanceData($ward['student_id']); ?>
                    <div class="attendance-card">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center mb-4">
                                    <div class="ward-avatar">
                                        <?php echo strtoupper(substr($ward['first_name'], 0, 1) . substr($ward['last_name'], 0, 1)); ?>
                                    </div>
                                    <div>
                                        <h5 class="mb-1">
                                            <?php echo htmlspecialchars($ward['first_name'] . ' ' . $ward['last_name']); ?>
                                        </h5>
                                        <p class="text-muted mb-0">
                                            <i class="fas fa-graduation-cap me-1"></i>
                                            <?php echo htmlspecialchars($ward['class_name'] . ' - ' . $ward['level_name']); ?>
                                        </p>
                                        <p class="text-muted mb-0">
                                            <i class="fas fa-id-card me-1"></i>
                                            <?php echo htmlspecialchars($ward['admission_number']); ?>
                                        </p>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="stat-card">
                                            <div class="stat-number text-success"><?php echo $attendance['present_days']; ?></div>
                                            <div class="stat-label">Days Present</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="stat-card">
                                            <div class="stat-number text-danger"><?php echo $attendance['absent_days']; ?></div>
                                            <div class="stat-label">Days Absent</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="stat-card">
                                            <div class="stat-number text-info"><?php echo $attendance['total_days']; ?></div>
                                            <div class="stat-label">Total Days</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="attendance-percentage <?php 
                                        echo $attendance['percentage'] >= 90 ? 'percentage-excellent' : 
                                             ($attendance['percentage'] >= 75 ? 'percentage-good' : 'percentage-poor'); 
                                    ?>">
                                        <?php echo $attendance['percentage']; ?>%
                                    </div>
                                    <p class="text-muted mb-3">Attendance Rate</p>
                                    
                                    <div class="progress-custom mb-3">
                                        <div class="progress-bar-custom bg-<?php 
                                            echo $attendance['percentage'] >= 90 ? 'success' : 
                                                 ($attendance['percentage'] >= 75 ? 'warning' : 'danger'); 
                                        ?>" style="width: <?php echo $attendance['percentage']; ?>%"></div>
                                    </div>
                                    
                                    <small class="text-muted">
                                        <?php 
                                        if ($attendance['percentage'] >= 90) {
                                            echo '<i class="fas fa-star text-success me-1"></i>Excellent Attendance';
                                        } elseif ($attendance['percentage'] >= 75) {
                                            echo '<i class="fas fa-exclamation-triangle text-warning me-1"></i>Good Attendance';
                                        } else {
                                            echo '<i class="fas fa-exclamation-circle text-danger me-1"></i>Needs Improvement';
                                        }
                                        ?>
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Absences -->
                        <?php if (!empty($attendance['recent_absences'])): ?>
                            <hr class="my-4">
                            <h6 class="mb-3">
                                <i class="fas fa-calendar-times me-2"></i>
                                Recent Absences
                            </h6>
                            <div class="row">
                                <?php foreach ($attendance['recent_absences'] as $absence): ?>
                                    <div class="col-md-4">
                                        <div class="absence-item">
                                            <div class="fw-bold"><?php echo date('M j, Y', strtotime($absence['date'])); ?></div>
                                            <small class="text-muted"><?php echo htmlspecialchars($absence['reason']); ?></small>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>

                        <div class="text-end mt-3">
                            <a href="ward-attendance.php?id=<?php echo $ward['student_id']; ?>" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-chart-line me-1"></i>View Detailed Report
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="attendance-card">
                    <div class="text-center py-5">
                        <i class="fas fa-user-plus fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">No Wards Linked</h5>
                        <p class="text-muted mb-4">
                            You need to link your children's accounts to view their attendance records.
                        </p>
                        <a href="index.php" class="btn btn-orange">
                            <i class="fas fa-plus me-2"></i>Link New Ward
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
