<?php
/**
 * Student Attendance View for Parents
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a parent
if (!isLoggedIn() || $_SESSION['user_type'] !== 'parent') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];
$student_id = $_GET['id'] ?? 0;

// Get parent information
$stmt = $conn->prepare("
    SELECT p.*, u.first_name as parent_first_name, u.last_name as parent_last_name
    FROM parents p
    JOIN users u ON p.user_id = u.id
    WHERE p.user_id = ?
");
$stmt->execute([$user_id]);
$parent = $stmt->fetch();

if (!$parent) {
    header('Location: ../login.php');
    exit();
}

// Verify parent has access to this student
$stmt = $conn->prepare("
    SELECT COUNT(*) FROM parent_student_relationships 
    WHERE parent_id = ? AND student_id = ?
");
$stmt->execute([$parent['id'], $student_id]);
$has_access = $stmt->fetchColumn() > 0;

if (!$has_access) {
    header('Location: index.php');
    exit();
}

// Get student information
$stmt = $conn->prepare("
    SELECT s.*, u.first_name, u.last_name, u.middle_name, u.user_code,
           c.class_name, l.level_name
    FROM students s
    JOIN users u ON s.user_id = u.id
    JOIN classes c ON s.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    WHERE s.id = ?
");
$stmt->execute([$student_id]);
$student = $stmt->fetch();

if (!$student) {
    header('Location: index.php');
    exit();
}

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();

// Get attendance summary for current term
$stmt = $conn->prepare("
    SELECT 
        COUNT(*) as total_days,
        SUM(CASE WHEN status = 'Present' THEN 1 ELSE 0 END) as present_days,
        SUM(CASE WHEN status = 'Absent' THEN 1 ELSE 0 END) as absent_days,
        SUM(CASE WHEN status = 'Late' THEN 1 ELSE 0 END) as late_days,
        SUM(CASE WHEN status = 'Excused' THEN 1 ELSE 0 END) as excused_days
    FROM attendance 
    WHERE student_id = ? AND term_id = ?
");
$stmt->execute([$student['id'], $current_academic['current_term_id'] ?? 1]);
$attendance_summary = $stmt->fetch();

// Calculate attendance percentage
$total_days = $attendance_summary['total_days'] ?? 0;
$present_days = $attendance_summary['present_days'] ?? 0;
$attendance_percentage = $total_days > 0 ? round(($present_days / $total_days) * 100, 1) : 0;

// Get recent attendance records
$stmt = $conn->prepare("
    SELECT a.*, DATE_FORMAT(a.attendance_date, '%M %d, %Y') as formatted_date,
           DATE_FORMAT(a.attendance_date, '%W') as day_name
    FROM attendance a
    WHERE a.student_id = ? AND a.term_id = ?
    ORDER BY a.attendance_date DESC
    LIMIT 30
");
$stmt->execute([$student['id'], $current_academic['current_term_id'] ?? 1]);
$attendance_records = $stmt->fetchAll();

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?> - Attendance</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 50%, #ff4500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container-custom {
            padding: 20px;
        }
        
        .attendance-header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .attendance-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .attendance-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .attendance-item:hover {
            border-color: #ff8c00;
            box-shadow: 0 2px 8px rgba(255, 140, 0, 0.2);
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.85rem;
        }
        
        .status-present { background: #d4edda; color: #155724; }
        .status-absent { background: #f8d7da; color: #721c24; }
        .status-late { background: #fff3cd; color: #856404; }
        .status-excused { background: #d1ecf1; color: #0c5460; }
        
        .stat-card {
            text-align: center;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 15px;
            color: white;
        }
        
        .attendance-stat { background: linear-gradient(135deg, #28a745, #20c997); }
        .absent-stat { background: linear-gradient(135deg, #dc3545, #c82333); }
        .late-stat { background: linear-gradient(135deg, #ffc107, #e0a800); }
        .excused-stat { background: linear-gradient(135deg, #17a2b8, #138496); }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .student-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #ff8c00;
            font-size: 1.1rem;
        }
        
        .attendance-calendar {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 5px;
            margin-top: 20px;
        }
        
        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        
        .calendar-header {
            background: #f8f9fa;
            color: #495057;
            font-weight: bold;
        }
        
        .calendar-present { background: #d4edda; color: #155724; }
        .calendar-absent { background: #f8d7da; color: #721c24; }
        .calendar-late { background: #fff3cd; color: #856404; }
        .calendar-excused { background: #d1ecf1; color: #0c5460; }
        .calendar-empty { background: #f8f9fa; color: #6c757d; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="student-details.php?id=<?php echo $student['id']; ?>">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Student Details
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($parent['parent_first_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="index.php"><i class="fas fa-home me-2"></i>Dashboard</a></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-custom">
        <div class="container">
            <!-- Student Header -->
            <div class="attendance-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-2">
                            Attendance Record: <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                        </h2>
                        <p class="text-muted mb-1">
                            <i class="fas fa-graduation-cap me-2"></i>
                            <?php echo htmlspecialchars($student['class_name'] . ' - ' . $student['level_name']); ?>
                        </p>
                        <p class="text-muted mb-0">
                            <span class="student-code"><?php echo htmlspecialchars($student['user_code']); ?></span>
                        </p>
                    </div>
                    <div class="col-md-4 text-center">
                        <h6 class="mb-2">Attendance Rate</h6>
                        <div class="display-4 text-success"><?php echo $attendance_percentage; ?>%</div>
                        <small class="text-muted"><?php echo $present_days; ?> of <?php echo $total_days; ?> days</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Attendance Statistics -->
                <div class="col-md-3">
                    <div class="stat-card attendance-stat">
                        <div class="stat-number"><?php echo $attendance_summary['present_days'] ?? 0; ?></div>
                        <h6>Present Days</h6>
                        <small>Regular attendance</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card absent-stat">
                        <div class="stat-number"><?php echo $attendance_summary['absent_days'] ?? 0; ?></div>
                        <h6>Absent Days</h6>
                        <small>Missed classes</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card late-stat">
                        <div class="stat-number"><?php echo $attendance_summary['late_days'] ?? 0; ?></div>
                        <h6>Late Days</h6>
                        <small>Arrived late</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card excused-stat">
                        <div class="stat-number"><?php echo $attendance_summary['excused_days'] ?? 0; ?></div>
                        <h6>Excused Days</h6>
                        <small>Authorized absence</small>
                    </div>
                </div>
            </div>

            <!-- Recent Attendance Records -->
            <div class="attendance-card">
                <h5 class="mb-4">
                    <i class="fas fa-calendar-check me-2"></i>
                    Recent Attendance Records
                </h5>
                
                <?php if (!empty($attendance_records)): ?>
                    <?php foreach ($attendance_records as $record): ?>
                        <div class="attendance-item">
                            <div class="row align-items-center">
                                <div class="col-md-3">
                                    <h6 class="mb-1"><?php echo $record['formatted_date']; ?></h6>
                                    <small class="text-muted"><?php echo $record['day_name']; ?></small>
                                </div>
                                <div class="col-md-3">
                                    <span class="status-badge status-<?php echo strtolower($record['status']); ?>">
                                        <i class="fas fa-<?php 
                                            echo $record['status'] === 'Present' ? 'check' : 
                                                ($record['status'] === 'Absent' ? 'times' : 
                                                ($record['status'] === 'Late' ? 'clock' : 'exclamation')); 
                                        ?> me-1"></i>
                                        <?php echo $record['status']; ?>
                                    </span>
                                </div>
                                <div class="col-md-6">
                                    <?php if ($record['remarks']): ?>
                                        <small class="text-muted">
                                            <i class="fas fa-comment me-1"></i>
                                            <?php echo htmlspecialchars($record['remarks']); ?>
                                        </small>
                                    <?php else: ?>
                                        <small class="text-muted">No remarks</small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No attendance records available</h6>
                        <p class="text-muted">Attendance records will appear here once classes begin</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Attendance Summary -->
            <?php if (!empty($attendance_records)): ?>
            <div class="attendance-card">
                <h5 class="mb-4">
                    <i class="fas fa-chart-pie me-2"></i>
                    Attendance Summary
                </h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Present</span>
                                <span class="fw-bold text-success"><?php echo $attendance_summary['present_days']; ?> days</span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-success" style="width: <?php echo $attendance_percentage; ?>%"></div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Absent</span>
                                <span class="fw-bold text-danger"><?php echo $attendance_summary['absent_days']; ?> days</span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-danger" style="width: <?php echo $total_days > 0 ? round(($attendance_summary['absent_days'] / $total_days) * 100, 1) : 0; ?>%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Late</span>
                                <span class="fw-bold text-warning"><?php echo $attendance_summary['late_days']; ?> days</span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-warning" style="width: <?php echo $total_days > 0 ? round(($attendance_summary['late_days'] / $total_days) * 100, 1) : 0; ?>%"></div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Excused</span>
                                <span class="fw-bold text-info"><?php echo $attendance_summary['excused_days']; ?> days</span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-info" style="width: <?php echo $total_days > 0 ? round(($attendance_summary['excused_days'] / $total_days) * 100, 1) : 0; ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="attendance-card">
                <h5 class="mb-4">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
                
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="student-details.php?id=<?php echo $student['id']; ?>" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user me-2"></i>Student Profile
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="student-grades.php?id=<?php echo $student['id']; ?>" class="btn btn-outline-success w-100">
                            <i class="fas fa-chart-line me-2"></i>Grades
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="pay-fees.php?student_id=<?php echo $student['id']; ?>" class="btn btn-warning w-100">
                            <i class="fas fa-credit-card me-2"></i>Pay Fees
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="index.php" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-home me-2"></i>Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
