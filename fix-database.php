<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Database - IGSS Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .fix-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            width: 100%;
            overflow: hidden;
        }
        .fix-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .fix-body {
            padding: 40px;
        }
        .btn-fix {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }
        .btn-fix:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <div class="fix-container">
        <div class="fix-header">
            <i class="fas fa-tools fa-3x mb-3"></i>
            <h2>Database Fix Tool</h2>
            <p>Fix missing tables for IGSS Management System</p>
        </div>
        
        <div class="fix-body">
            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_database'])) {
                try {
                    require_once 'config/database.php';
                    $conn = getDBConnection();
                    
                    echo '<div class="alert alert-info"><i class="fas fa-cog fa-spin me-2"></i>Creating missing tables...</div>';
                    
                    // Create teacher_subjects table
                    $conn->exec("
                        CREATE TABLE IF NOT EXISTS teacher_subjects (
                            id INT PRIMARY KEY AUTO_INCREMENT,
                            teacher_id INT NOT NULL,
                            subject_id INT NOT NULL,
                            class_id INT NOT NULL,
                            session_id INT NOT NULL DEFAULT 1,
                            assigned_by INT NOT NULL,
                            assigned_date DATE NOT NULL,
                            is_active BOOLEAN DEFAULT TRUE,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            INDEX idx_teacher_subjects_teacher (teacher_id),
                            INDEX idx_teacher_subjects_class (class_id),
                            UNIQUE KEY unique_teacher_subject_class (teacher_id, subject_id, class_id, session_id)
                        )
                    ");
                    
                    // Create form_teachers table
                    $conn->exec("
                        CREATE TABLE IF NOT EXISTS form_teachers (
                            id INT PRIMARY KEY AUTO_INCREMENT,
                            teacher_id INT NOT NULL,
                            class_id INT NOT NULL,
                            session_id INT NOT NULL DEFAULT 1,
                            assigned_by INT NOT NULL,
                            assigned_date DATE NOT NULL,
                            is_active BOOLEAN DEFAULT TRUE,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            INDEX idx_form_teachers_teacher (teacher_id),
                            INDEX idx_form_teachers_class (class_id),
                            UNIQUE KEY unique_class_session (class_id, session_id)
                        )
                    ");
                    
                    // Create fee_types table
                    $conn->exec("
                        CREATE TABLE IF NOT EXISTS fee_types (
                            id INT PRIMARY KEY AUTO_INCREMENT,
                            fee_name VARCHAR(100) NOT NULL,
                            fee_description TEXT,
                            amount_js DECIMAL(10,2) NOT NULL,
                            amount_ss DECIMAL(10,2) NOT NULL,
                            is_mandatory BOOLEAN DEFAULT TRUE,
                            is_active BOOLEAN DEFAULT TRUE,
                            created_by INT NOT NULL DEFAULT 1,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            INDEX idx_fee_types_active (is_active)
                        )
                    ");
                    
                    // Insert default fee types
                    $conn->exec("
                        INSERT IGNORE INTO fee_types (fee_name, fee_description, amount_js, amount_ss, is_mandatory, created_by) VALUES
                        ('School Fee', 'Main school fee for academic session', 150000.00, 200000.00, TRUE, 1),
                        ('Development Levy', 'School development and infrastructure fee', 25000.00, 30000.00, TRUE, 1),
                        ('Sports Fee', 'Sports and recreational activities fee', 5000.00, 7500.00, FALSE, 1),
                        ('Library Fee', 'Library maintenance and book fee', 3000.00, 5000.00, FALSE, 1),
                        ('Laboratory Fee', 'Science laboratory equipment and materials', 10000.00, 15000.00, TRUE, 1),
                        ('Computer Fee', 'Computer lab and ICT training fee', 8000.00, 12000.00, FALSE, 1),
                        ('Examination Fee', 'Internal and external examination fee', 15000.00, 20000.00, TRUE, 1)
                    ");
                    
                    echo '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>All missing tables created successfully!</div>';
                    echo '<div class="text-center mt-4">';
                    echo '<a href="admin/assign-subjects.php" class="btn btn-primary me-2"><i class="fas fa-book me-2"></i>Test Assign Subjects</a>';
                    echo '<a href="admin/dashboard.php" class="btn btn-success"><i class="fas fa-tachometer-alt me-2"></i>Go to Admin Dashboard</a>';
                    echo '</div>';
                    
                } catch (Exception $e) {
                    echo '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            } else {
                ?>
                <div class="text-center mb-4">
                    <h4>Fix Missing Database Tables</h4>
                    <p class="text-muted">This will create the missing tables needed for the admin panel to work properly.</p>
                </div>
                
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>What this will do:</strong>
                    <ul class="mb-0 mt-2">
                        <li>Create <code>teacher_subjects</code> table for subject assignments</li>
                        <li>Create <code>form_teachers</code> table for form teacher assignments</li>
                        <li>Create <code>fee_types</code> table for fee management</li>
                        <li>Insert default fee types data</li>
                    </ul>
                </div>
                
                <form method="POST">
                    <button type="submit" name="fix_database" class="btn btn-primary btn-fix">
                        <i class="fas fa-wrench me-2"></i>Fix Database Tables
                    </button>
                </form>
                
                <div class="text-center mt-4">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        This is safe to run multiple times - existing tables won't be affected
                    </small>
                </div>
                <?php
            }
            ?>
        </div>
    </div>
</body>
</html>
