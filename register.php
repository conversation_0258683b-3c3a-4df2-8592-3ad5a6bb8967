<?php
/**
 * Registration Page for IGSS Management System
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirectToDashboard();
}

$error = '';
$success = '';
$step = 1;

// Get security questions
$conn = getDBConnection();
$stmt = $conn->prepare("SELECT * FROM security_questions WHERE is_active = 1 ORDER BY question");
$stmt->execute();
$security_questions = $stmt->fetchAll();

// Get classes for student registration
$classes = getAllClasses();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_type = sanitizeInput($_POST['user_type'] ?? '');
    $first_name = sanitizeInput($_POST['first_name'] ?? '');
    $last_name = sanitizeInput($_POST['last_name'] ?? '');
    $middle_name = sanitizeInput($_POST['middle_name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $address = sanitizeInput($_POST['address'] ?? '');
    $date_of_birth = sanitizeInput($_POST['date_of_birth'] ?? '');
    $gender = sanitizeInput($_POST['gender'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    // Security questions
    $question1 = sanitizeInput($_POST['question1'] ?? '');
    $answer1 = sanitizeInput($_POST['answer1'] ?? '');
    $question2 = sanitizeInput($_POST['question2'] ?? '');
    $answer2 = sanitizeInput($_POST['answer2'] ?? '');

    // Validation - phone number not required for students, date of birth only required for students
    $phone_required = ($user_type !== 'student');
    $dob_required = ($user_type === 'student');

    if (empty($user_type) || empty($first_name) || empty($last_name) || empty($email) ||
        empty($gender) || empty($password) || empty($confirm_password) ||
        empty($question1) || empty($answer1) || empty($question2) || empty($answer2)) {
        $error = 'Please fill in all required fields.';
    } elseif ($phone_required && empty($phone)) {
        $error = 'Phone number is required for this account type.';
    } elseif ($dob_required && empty($date_of_birth)) {
        $error = 'Date of birth is required for students.';
    } elseif (!empty($phone) && !preg_match('/^[0-9]{11}$/', $phone)) {
        $error = 'Phone number must be exactly 11 digits.';
    } elseif ($password !== $confirm_password) {
        $error = 'Passwords do not match.';
    } elseif (strlen($password) < PASSWORD_MIN_LENGTH) {
        $error = 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long.';
    } elseif ($question1 === $question2) {
        $error = 'Please select different security questions.';
    } else {
        try {
            $conn->beginTransaction();

            // Check if email already exists
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                throw new Exception('Email address already exists.');
            }

            // Generate user code
            $user_code = generateUserCode($user_type);

            // Hash password
            $password_hash = password_hash($password, PASSWORD_DEFAULT);

            // Insert user (handle empty phone for students and empty DOB for teachers/parents)
            $phone_value = ($user_type === 'student' && empty($phone)) ? null : $phone;
            $dob_value = ($user_type !== 'student' && empty($date_of_birth)) ? null : $date_of_birth;

            $stmt = $conn->prepare("
                INSERT INTO users (user_code, first_name, last_name, middle_name, email, phone, address,
                                 date_of_birth, gender, user_type, password_hash, is_approved, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, 1)
            ");

            $stmt->execute([
                $user_code, $first_name, $last_name, $middle_name, $email, $phone_value, $address,
                $dob_value, $gender, $user_type, $password_hash
            ]);

            $user_id = $conn->lastInsertId();

            // Insert security answers
            $stmt = $conn->prepare("
                INSERT INTO user_security_answers (user_id, question_id, answer_hash)
                VALUES (?, ?, ?)
            ");

            $stmt->execute([$user_id, $question1, password_hash(strtolower($answer1), PASSWORD_DEFAULT)]);
            $stmt->execute([$user_id, $question2, password_hash(strtolower($answer2), PASSWORD_DEFAULT)]);

            // Handle specific user type data
            if ($user_type === 'teacher') {
                $employee_id = 'EMP' . str_pad($user_id, 6, '0', STR_PAD_LEFT);
                $qualification = sanitizeInput($_POST['qualification'] ?? '');
                $specialization = sanitizeInput($_POST['specialization'] ?? '');

                $stmt = $conn->prepare("
                    INSERT INTO teachers (user_id, employee_id, qualification, specialization, hire_date)
                    VALUES (?, ?, ?, ?, CURDATE())
                ");
                $stmt->execute([$user_id, $employee_id, $qualification, $specialization]);

            } elseif ($user_type === 'student') {
                $class_id = sanitizeInput($_POST['class_id'] ?? '');
                $guardian_name = sanitizeInput($_POST['guardian_name'] ?? '');
                $guardian_phone = sanitizeInput($_POST['guardian_phone'] ?? '');
                $guardian_email = sanitizeInput($_POST['guardian_email'] ?? '');
                $emergency_contact = sanitizeInput($_POST['emergency_contact'] ?? '');
                $is_boarding = isset($_POST['is_boarding']) ? 1 : 0;

                if (empty($class_id) || empty($guardian_name) || empty($guardian_phone)) {
                    throw new Exception('Please fill in all student-specific fields.');
                }

                $admission_number = 'ADM' . date('Y') . str_pad($user_id, 4, '0', STR_PAD_LEFT);

                $stmt = $conn->prepare("
                    INSERT INTO students (user_id, admission_number, class_id, admission_date,
                                        guardian_name, guardian_phone, guardian_email, emergency_contact, is_boarding)
                    VALUES (?, ?, ?, CURDATE(), ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $user_id, $admission_number, $class_id, $guardian_name,
                    $guardian_phone, $guardian_email, $emergency_contact, $is_boarding
                ]);

            } elseif ($user_type === 'parent') {
                $occupation = sanitizeInput($_POST['occupation'] ?? '');
                $workplace = sanitizeInput($_POST['workplace'] ?? '');
                $relationship = sanitizeInput($_POST['relationship'] ?? '');

                $stmt = $conn->prepare("
                    INSERT INTO parents (user_id, occupation, workplace, relationship_to_student)
                    VALUES (?, ?, ?, ?)
                ");
                $stmt->execute([$user_id, $occupation, $workplace, $relationship]);
            }

            $conn->commit();
            $success = 'Registration successful! Your account has been created with User Code: ' . $user_code . '. Please wait for administrator approval before you can log in. You will be notified once your account is approved.';

        } catch (Exception $e) {
            $conn->rollback();
            $error = $e->getMessage();
        }
    }
}

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 50%, #ff4500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }

        .register-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            max-width: 900px;
            margin: 0 auto;
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .register-header {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 50%, #ff4500 100%);
            color: white;
            padding: 35px;
            text-align: center;
            position: relative;
        }

        .register-header::before {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            height: 40px;
            background: white;
            border-radius: 50px 50px 0 0;
        }

        .register-header h2 {
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .register-body {
            padding: 50px 40px 40px;
            background: white;
            position: relative;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }

        .form-control, .form-select {
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 12px 15px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-control:focus, .form-select:focus {
            border-color: #ff8c00;
            box-shadow: 0 0 0 0.2rem rgba(255, 140, 0, 0.25);
            background: white;
        }

        .btn-register {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 50%, #ff4500 100%);
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            color: white;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(255, 140, 0, 0.3);
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 140, 0, 0.4);
            background: linear-gradient(135deg, #ff9500 0%, #ff7635 50%, #ff5500 100%);
        }

        .user-type-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .user-type-card {
            border: 2px solid #e1e5e9;
            border-radius: 15px;
            padding: 25px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(5px);
        }

        .user-type-card:hover {
            border-color: #ff8c00;
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(255, 140, 0, 0.2);
        }

        .user-type-card.selected {
            border-color: #ff8c00;
            background: rgba(255, 140, 0, 0.1);
            box-shadow: 0 5px 15px rgba(255, 140, 0, 0.3);
        }

        .user-type-card i {
            font-size: 35px;
            color: #ff8c00;
            margin-bottom: 12px;
        }

        .specific-fields {
            display: none;
            margin-top: 25px;
            padding: 25px;
            background: rgba(255, 140, 0, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 140, 0, 0.2);
        }

        .specific-fields h5 {
            color: #ff6b35;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .specific-fields.active {
            display: block;
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 18px;
            margin-bottom: 25px;
            backdrop-filter: blur(5px);
        }

        .alert-success {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
            color: #155724;
        }

        .alert-danger {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            color: #721c24;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }

        .btn-success:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        a {
            color: #ff8c00;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        a:hover {
            color: #ff6b35;
            text-decoration: underline;
        }

        /* Password Toggle Styles */
        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
            transition: color 0.3s ease;
            z-index: 10;
        }

        .password-toggle:hover {
            color: #ff8c00;
        }

        @media (max-width: 768px) {
            .register-container {
                margin: 10px;
                border-radius: 20px;
            }

            .register-body {
                padding: 40px 25px 30px;
            }

            .user-type-cards {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .user-type-card {
                padding: 20px 15px;
            }

            .register-header {
                padding: 25px;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <h2><i class="fas fa-user-plus me-2"></i>Create Account</h2>
            <p>Join <?php echo htmlspecialchars($school_info['name']); ?> Management System</p>
        </div>

        <div class="register-body">
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                    <div class="mt-3">
                        <a href="login.php" class="btn btn-success">
                            <i class="fas fa-sign-in-alt me-2"></i>Go to Login
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <form method="POST" action="" id="registrationForm">
                    <!-- User Type Selection -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-users me-2"></i>Select Account Type
                        </label>
                        <div class="user-type-cards">
                            <div class="user-type-card" data-type="teacher">
                                <i class="fas fa-chalkboard-teacher"></i>
                                <h6>Teacher</h6>
                                <input type="radio" name="user_type" value="teacher" style="display: none;">
                            </div>
                            <div class="user-type-card" data-type="student">
                                <i class="fas fa-user-graduate"></i>
                                <h6>Student</h6>
                                <input type="radio" name="user_type" value="student" style="display: none;">
                            </div>
                            <div class="user-type-card" data-type="parent">
                                <i class="fas fa-user-friends"></i>
                                <h6>Parent/Guardian</h6>
                                <input type="radio" name="user_type" value="parent" style="display: none;">
                            </div>
                        </div>
                    </div>

                    <!-- Personal Information -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="middle_name" class="form-label">Middle Name</label>
                                <input type="text" class="form-control" id="middle_name" name="middle_name">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                            </div>
                        </div>
                    </div>

                    <!-- Email (always required) -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                        <div class="col-md-6" id="phone-field">
                            <div class="form-group">
                                <label for="phone" class="form-label">Phone Number *</label>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       maxlength="11" pattern="[0-9]{11}"
                                       placeholder="e.g., 08012345678" required>
                                <div class="form-text">Enter 11-digit Nigerian phone number</div>
                            </div>
                        </div>
                    </div>

                    <!-- Date of Birth and Gender (conditional) -->
                    <div class="row">
                        <div class="col-md-6" id="dob-field">
                            <div class="form-group">
                                <label for="date_of_birth" class="form-label" id="dob-label">Date of Birth</label>
                                <input type="date" class="form-control" id="date_of_birth" name="date_of_birth">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="gender" class="form-label">Gender *</label>
                                <select class="form-select" id="gender" name="gender" required>
                                    <option value="">Select Gender</option>
                                    <option value="Male">Male</option>
                                    <option value="Female">Female</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Address (conditional) -->
                    <div class="form-group" id="address-field">
                        <label for="address" class="form-label">Address</label>
                        <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                    </div>

                    <!-- Password Section -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="password" class="form-label">Password *</label>
                                <div class="password-container position-relative">
                                    <input type="password" class="form-control" id="password" name="password"
                                           minlength="<?php echo PASSWORD_MIN_LENGTH; ?>" required>
                                    <span class="password-toggle" onclick="togglePassword('password')">
                                        <i class="fas fa-eye" id="password-toggle-icon"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="confirm_password" class="form-label">Confirm Password *</label>
                                <div class="password-container position-relative">
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    <span class="password-toggle" onclick="togglePassword('confirm_password')">
                                        <i class="fas fa-eye" id="confirm_password-toggle-icon"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Questions -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="question1" class="form-label">Security Question 1 *</label>
                                <select class="form-select" id="question1" name="question1" required>
                                    <option value="">Select a question</option>
                                    <?php foreach ($security_questions as $question): ?>
                                        <option value="<?php echo $question['id']; ?>">
                                            <?php echo htmlspecialchars($question['question']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="answer1" class="form-label">Answer 1 *</label>
                                <input type="text" class="form-control" id="answer1" name="answer1" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="question2" class="form-label">Security Question 2 *</label>
                                <select class="form-select" id="question2" name="question2" required>
                                    <option value="">Select a question</option>
                                    <?php foreach ($security_questions as $question): ?>
                                        <option value="<?php echo $question['id']; ?>">
                                            <?php echo htmlspecialchars($question['question']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="answer2" class="form-label">Answer 2 *</label>
                                <input type="text" class="form-control" id="answer2" name="answer2" required>
                            </div>
                        </div>
                    </div>

                    <!-- Teacher Specific Fields -->
                    <div id="teacher-fields" class="specific-fields">
                        <h5><i class="fas fa-chalkboard-teacher me-2"></i>Teacher Information</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="qualification" class="form-label">Qualification</label>
                                    <input type="text" class="form-control" id="qualification" name="qualification"
                                           placeholder="e.g., B.Ed, M.Sc, etc.">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="specialization" class="form-label">Specialization</label>
                                    <input type="text" class="form-control" id="specialization" name="specialization"
                                           placeholder="e.g., Mathematics, English, etc.">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Student Specific Fields -->
                    <div id="student-fields" class="specific-fields">
                        <h5><i class="fas fa-user-graduate me-2"></i>Student Information</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="class_id" class="form-label">Class *</label>
                                    <select class="form-select" id="class_id" name="class_id">
                                        <option value="">Select Class</option>
                                        <?php foreach ($classes as $class): ?>
                                            <option value="<?php echo $class['id']; ?>">
                                                <?php echo htmlspecialchars($class['class_name']); ?>
                                                <?php if ($class['class_type'] !== 'General'): ?>
                                                    (<?php echo $class['class_type']; ?>)
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="is_boarding" name="is_boarding">
                                        <label class="form-check-label" for="is_boarding">
                                            Boarding Student
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="guardian_name" class="form-label">Guardian Name *</label>
                                    <input type="text" class="form-control" id="guardian_name" name="guardian_name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="guardian_phone" class="form-label">Guardian Phone *</label>
                                    <input type="tel" class="form-control" id="guardian_phone" name="guardian_phone"
                                           maxlength="11" pattern="[0-9]{11}" placeholder="e.g., 08012345678">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="guardian_email" class="form-label">Guardian Email</label>
                                    <input type="email" class="form-control" id="guardian_email" name="guardian_email">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="emergency_contact" class="form-label">Emergency Contact</label>
                                    <input type="tel" class="form-control" id="emergency_contact" name="emergency_contact"
                                           maxlength="11" pattern="[0-9]{11}" placeholder="e.g., 08012345678">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Parent Specific Fields -->
                    <div id="parent-fields" class="specific-fields">
                        <h5><i class="fas fa-user-friends me-2"></i>Parent/Guardian Information</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="occupation" class="form-label">Occupation</label>
                                    <input type="text" class="form-control" id="occupation" name="occupation">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="workplace" class="form-label">Workplace</label>
                                    <input type="text" class="form-control" id="workplace" name="workplace">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="relationship" class="form-label">Relationship to Student</label>
                                    <select class="form-select" id="relationship" name="relationship">
                                        <option value="">Select Relationship</option>
                                        <option value="Father">Father</option>
                                        <option value="Mother">Mother</option>
                                        <option value="Guardian">Guardian</option>
                                        <option value="Uncle">Uncle</option>
                                        <option value="Aunt">Aunt</option>
                                        <option value="Grandparent">Grandparent</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary btn-register">
                        <i class="fas fa-user-plus me-2"></i>Create Account
                    </button>
                </form>
            <?php endif; ?>

            <div class="text-center mt-4">
                <p>Already have an account?
                    <a href="login.php" class="text-decoration-none">
                        <i class="fas fa-sign-in-alt me-1"></i>Sign In
                    </a>
                </p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // User type selection
        document.querySelectorAll('.user-type-card').forEach(card => {
            card.addEventListener('click', function() {
                // Remove selected class from all cards
                document.querySelectorAll('.user-type-card').forEach(c => c.classList.remove('selected'));

                // Add selected class to clicked card
                this.classList.add('selected');

                // Set radio button value
                const userType = this.dataset.type;
                this.querySelector('input[type="radio"]').checked = true;

                // Hide all specific fields
                document.querySelectorAll('.specific-fields').forEach(field => {
                    field.classList.remove('active');
                });

                // Show relevant specific fields
                const specificFields = document.getElementById(userType + '-fields');
                if (specificFields) {
                    specificFields.classList.add('active');
                }

                // Handle conditional fields based on user type
                handleConditionalFields(userType);
            });
        });

        // Handle conditional field display
        function handleConditionalFields(userType) {
            const phoneField = document.getElementById('phone-field');
            const dobField = document.getElementById('dob-field');
            const addressField = document.getElementById('address-field');
            const phoneInput = document.getElementById('phone');
            const dobInput = document.getElementById('date_of_birth');
            const dobLabel = document.getElementById('dob-label');
            const addressInput = document.getElementById('address');

            // Reset all fields to visible and required
            phoneField.style.display = 'block';
            dobField.style.display = 'block';
            addressField.style.display = 'block';
            phoneInput.required = true;
            dobInput.required = false;

            if (userType === 'student') {
                // Students don't need phone number but need date of birth
                phoneField.style.display = 'none';
                phoneInput.required = false;
                phoneInput.value = '';
                dobField.style.display = 'block';
                dobInput.required = true;
                dobLabel.innerHTML = 'Date of Birth *';
            } else if (userType === 'teacher') {
                // Teachers don't need date of birth
                dobField.style.display = 'none';
                dobInput.required = false;
                dobInput.value = '';
                addressInput.required = false;
                dobLabel.innerHTML = 'Date of Birth';
            } else if (userType === 'parent') {
                // Parents don't need date of birth
                dobField.style.display = 'none';
                dobInput.required = false;
                dobInput.value = '';
                addressInput.required = false;
                dobLabel.innerHTML = 'Date of Birth';
            }
        }

        // Phone number validation (only digits, max 11)
        function validatePhoneInput(input) {
            input.addEventListener('input', function() {
                // Remove any non-digit characters
                this.value = this.value.replace(/\D/g, '');

                // Limit to 11 digits
                if (this.value.length > 11) {
                    this.value = this.value.slice(0, 11);
                }
            });

            input.addEventListener('keypress', function(e) {
                // Only allow digits
                if (!/\d/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Enter'].includes(e.key)) {
                    e.preventDefault();
                }
            });
        }

        // Apply phone validation to all phone inputs
        document.addEventListener('DOMContentLoaded', function() {
            const phoneInputs = document.querySelectorAll('input[type="tel"]');
            phoneInputs.forEach(validatePhoneInput);
        });

        // Password toggle function
        function togglePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const toggleIcon = document.getElementById(fieldId + '-toggle-icon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;

            if (password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });

        // Security questions validation
        document.getElementById('question1').addEventListener('change', function() {
            const question2 = document.getElementById('question2');
            const selectedValue = this.value;

            // Enable all options in question2
            Array.from(question2.options).forEach(option => {
                option.disabled = false;
            });

            // Disable the selected option in question2
            if (selectedValue) {
                const optionToDisable = question2.querySelector(`option[value="${selectedValue}"]`);
                if (optionToDisable) {
                    optionToDisable.disabled = true;
                }

                // If question2 has the same value, reset it
                if (question2.value === selectedValue) {
                    question2.value = '';
                }
            }
        });

        document.getElementById('question2').addEventListener('change', function() {
            const question1 = document.getElementById('question1');
            const selectedValue = this.value;

            // Enable all options in question1
            Array.from(question1.options).forEach(option => {
                option.disabled = false;
            });

            // Disable the selected option in question1
            if (selectedValue) {
                const optionToDisable = question1.querySelector(`option[value="${selectedValue}"]`);
                if (optionToDisable) {
                    optionToDisable.disabled = true;
                }

                // If question1 has the same value, reset it
                if (question1.value === selectedValue) {
                    question1.value = '';
                }
            }
        });
    </script>
</body>
</html>
