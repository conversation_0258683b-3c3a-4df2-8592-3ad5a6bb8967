-- Parent Approval System Database Schema
-- Intellectual Giant Secondary School Electronic Management System

-- Create parent approval logs table
CREATE TABLE IF NOT EXISTS parent_approval_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action ENUM('approved', 'rejected', 'reactivated') NOT NULL,
    admin_id INT NOT NULL,
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_admin_id (admin_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- Add approval notification preferences table (optional)
CREATE TABLE IF NOT EXISTS parent_approval_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    notification_type ENUM('email', 'sms', 'both') DEFAULT 'email',
    is_sent BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_sent (is_sent)
);

-- Update existing users table to ensure is_approved field exists with proper default
-- (This is safe to run even if the field already exists)
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS is_approved BOOLEAN DEFAULT FALSE AFTER is_active;

-- Update existing parent accounts to be approved by default (for backward compatibility)
-- This ensures existing parents don't get locked out
UPDATE users 
SET is_approved = TRUE 
WHERE user_type = 'parent' 
AND created_at < NOW() 
AND is_approved IS NULL OR is_approved = FALSE;

-- Create index for faster approval status queries
CREATE INDEX IF NOT EXISTS idx_users_approval_status ON users(user_type, is_approved, is_active);

-- Insert sample approval log entries (optional - for testing)
-- INSERT INTO parent_approval_logs (user_id, action, admin_id, admin_notes) 
-- VALUES 
-- (1, 'approved', 1, 'Initial approval for testing'),
-- (2, 'rejected', 1, 'Incomplete registration information');

-- Create view for easy parent approval management
CREATE OR REPLACE VIEW parent_approval_summary AS
SELECT 
    u.id,
    u.user_code,
    u.first_name,
    u.last_name,
    u.email,
    u.phone,
    u.is_approved,
    u.is_active,
    u.created_at as registration_date,
    p.occupation,
    p.workplace,
    (SELECT COUNT(*) FROM parent_student_relationships psr WHERE psr.parent_id = p.id) as linked_wards_count,
    (SELECT action FROM parent_approval_logs pal WHERE pal.user_id = u.id ORDER BY pal.created_at DESC LIMIT 1) as last_action,
    (SELECT admin_notes FROM parent_approval_logs pal WHERE pal.user_id = u.id ORDER BY pal.created_at DESC LIMIT 1) as last_admin_notes,
    (SELECT created_at FROM parent_approval_logs pal WHERE pal.user_id = u.id ORDER BY pal.created_at DESC LIMIT 1) as last_action_date
FROM users u
JOIN parents p ON u.id = p.user_id
WHERE u.user_type = 'parent'
ORDER BY u.created_at DESC;

-- Create stored procedure for approving parents
DELIMITER //
CREATE OR REPLACE PROCEDURE ApproveParent(
    IN p_user_id INT,
    IN p_admin_id INT,
    IN p_admin_notes TEXT
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Update user approval status
    UPDATE users 
    SET is_approved = TRUE, updated_at = NOW() 
    WHERE id = p_user_id AND user_type = 'parent';
    
    -- Log the approval action
    INSERT INTO parent_approval_logs (user_id, action, admin_id, admin_notes)
    VALUES (p_user_id, 'approved', p_admin_id, p_admin_notes);
    
    COMMIT;
END //
DELIMITER ;

-- Create stored procedure for rejecting parents
DELIMITER //
CREATE OR REPLACE PROCEDURE RejectParent(
    IN p_user_id INT,
    IN p_admin_id INT,
    IN p_admin_notes TEXT
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Update user approval and active status
    UPDATE users 
    SET is_approved = FALSE, is_active = FALSE, updated_at = NOW() 
    WHERE id = p_user_id AND user_type = 'parent';
    
    -- Log the rejection action
    INSERT INTO parent_approval_logs (user_id, action, admin_id, admin_notes)
    VALUES (p_user_id, 'rejected', p_admin_id, p_admin_notes);
    
    COMMIT;
END //
DELIMITER ;

-- Create stored procedure for reactivating parents
DELIMITER //
CREATE OR REPLACE PROCEDURE ReactivateParent(
    IN p_user_id INT,
    IN p_admin_id INT,
    IN p_admin_notes TEXT
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Update user active status
    UPDATE users 
    SET is_active = TRUE, updated_at = NOW() 
    WHERE id = p_user_id AND user_type = 'parent';
    
    -- Log the reactivation action
    INSERT INTO parent_approval_logs (user_id, action, admin_id, admin_notes)
    VALUES (p_user_id, 'reactivated', p_admin_id, p_admin_notes);
    
    COMMIT;
END //
DELIMITER ;

-- Create function to get approval statistics
DELIMITER //
CREATE OR REPLACE FUNCTION GetParentApprovalStats()
RETURNS JSON
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result JSON;
    
    SELECT JSON_OBJECT(
        'total', COUNT(*),
        'pending', SUM(CASE WHEN is_approved = FALSE AND is_active = TRUE THEN 1 ELSE 0 END),
        'approved', SUM(CASE WHEN is_approved = TRUE AND is_active = TRUE THEN 1 ELSE 0 END),
        'rejected', SUM(CASE WHEN is_approved = FALSE AND is_active = FALSE THEN 1 ELSE 0 END)
    ) INTO result
    FROM users 
    WHERE user_type = 'parent';
    
    RETURN result;
END //
DELIMITER ;

-- Create trigger to automatically create approval notification record
DELIMITER //
CREATE OR REPLACE TRIGGER after_parent_approval_update
AFTER UPDATE ON users
FOR EACH ROW
BEGIN
    -- Only trigger for parent accounts when approval status changes
    IF NEW.user_type = 'parent' AND OLD.is_approved != NEW.is_approved THEN
        INSERT INTO parent_approval_notifications (user_id, notification_type)
        VALUES (NEW.id, 'email');
    END IF;
END //
DELIMITER ;

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE ON parent_approval_logs TO 'igss_admin'@'localhost';
-- GRANT SELECT, INSERT, UPDATE ON parent_approval_notifications TO 'igss_admin'@'localhost';
-- GRANT EXECUTE ON PROCEDURE ApproveParent TO 'igss_admin'@'localhost';
-- GRANT EXECUTE ON PROCEDURE RejectParent TO 'igss_admin'@'localhost';
-- GRANT EXECUTE ON PROCEDURE ReactivateParent TO 'igss_admin'@'localhost';
-- GRANT EXECUTE ON FUNCTION GetParentApprovalStats TO 'igss_admin'@'localhost';

-- Sample queries for testing the approval system

-- Get all pending parent approvals
-- SELECT * FROM parent_approval_summary WHERE is_approved = FALSE AND is_active = TRUE;

-- Get approval statistics
-- SELECT GetParentApprovalStats() as stats;

-- Get approval history for a specific parent
-- SELECT * FROM parent_approval_logs WHERE user_id = 1 ORDER BY created_at DESC;

-- Get parents approved in the last 30 days
-- SELECT * FROM parent_approval_summary 
-- WHERE last_action = 'approved' 
-- AND last_action_date >= DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Get parents with most linked wards
-- SELECT * FROM parent_approval_summary 
-- WHERE is_approved = TRUE 
-- ORDER BY linked_wards_count DESC 
-- LIMIT 10;
