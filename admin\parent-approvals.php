<?php
/**
 * Parent Approval Management for Administrators
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/parent-approval-notifications.php';

// Check if user is logged in and is an admin
if (!isLoggedIn() || $_SESSION['user_type'] !== 'admin') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$message = '';
$error = '';

// Handle approval/rejection actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        $user_id = (int)$_POST['user_id'];
        $admin_notes = sanitizeInput($_POST['admin_notes'] ?? '');
        
        try {
            if ($action === 'approve') {
                // Approve the parent account
                $stmt = $conn->prepare("
                    UPDATE users 
                    SET is_approved = 1, updated_at = NOW() 
                    WHERE id = ? AND user_type = 'parent'
                ");
                $stmt->execute([$user_id]);
                
                // Log the approval action
                $stmt = $conn->prepare("
                    INSERT INTO parent_approval_logs (user_id, action, admin_id, admin_notes, created_at)
                    VALUES (?, 'approved', ?, ?, NOW())
                ");
                $stmt->execute([$user_id, $_SESSION['user_id'], $admin_notes]);

                // Send approval notification email
                sendParentApprovalNotification($user_id, 'approved', $admin_notes);

                $message = 'Parent account approved successfully and notification sent.';
                
            } elseif ($action === 'reject') {
                // Reject the parent account (mark as inactive)
                $stmt = $conn->prepare("
                    UPDATE users 
                    SET is_approved = 0, is_active = 0, updated_at = NOW() 
                    WHERE id = ? AND user_type = 'parent'
                ");
                $stmt->execute([$user_id]);
                
                // Log the rejection action
                $stmt = $conn->prepare("
                    INSERT INTO parent_approval_logs (user_id, action, admin_id, admin_notes, created_at)
                    VALUES (?, 'rejected', ?, ?, NOW())
                ");
                $stmt->execute([$user_id, $_SESSION['user_id'], $admin_notes]);

                // Send rejection notification email
                sendParentApprovalNotification($user_id, 'rejected', $admin_notes);

                $message = 'Parent account rejected and notification sent.';
                
            } elseif ($action === 'reactivate') {
                // Reactivate a rejected account
                $stmt = $conn->prepare("
                    UPDATE users 
                    SET is_active = 1, updated_at = NOW() 
                    WHERE id = ? AND user_type = 'parent'
                ");
                $stmt->execute([$user_id]);
                
                // Log the reactivation action
                $stmt = $conn->prepare("
                    INSERT INTO parent_approval_logs (user_id, action, admin_id, admin_notes, created_at)
                    VALUES (?, 'reactivated', ?, ?, NOW())
                ");
                $stmt->execute([$user_id, $_SESSION['user_id'], $admin_notes]);

                // Send reactivation notification email
                sendParentApprovalNotification($user_id, 'reactivated', $admin_notes);

                $message = 'Parent account reactivated and notification sent.';
            }
            
        } catch (Exception $e) {
            $error = 'Failed to process action: ' . $e->getMessage();
        }
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? 'pending';
$search = $_GET['search'] ?? '';

// Build query based on filters
$where_conditions = ["u.user_type = 'parent'"];
$params = [];

if ($status_filter === 'pending') {
    $where_conditions[] = "u.is_approved = 0 AND u.is_active = 1";
} elseif ($status_filter === 'approved') {
    $where_conditions[] = "u.is_approved = 1 AND u.is_active = 1";
} elseif ($status_filter === 'rejected') {
    $where_conditions[] = "u.is_approved = 0 AND u.is_active = 0";
}

if (!empty($search)) {
    $where_conditions[] = "(u.first_name LIKE ? OR u.last_name LIKE ? OR u.email LIKE ? OR u.user_code LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

$where_clause = implode(' AND ', $where_conditions);

// Get parent registrations
$stmt = $conn->prepare("
    SELECT 
        u.id,
        u.user_code,
        u.first_name,
        u.last_name,
        u.middle_name,
        u.email,
        u.phone,
        u.address,
        u.is_approved,
        u.is_active,
        u.created_at,
        p.occupation,
        p.workplace,
        p.relationship_to_student,
        (SELECT COUNT(*) FROM parent_student_relationships psr WHERE psr.parent_id = p.id) as linked_wards_count
    FROM users u
    JOIN parents p ON u.id = p.user_id
    WHERE $where_clause
    ORDER BY u.created_at DESC
");
$stmt->execute($params);
$parents = $stmt->fetchAll();

// Get approval statistics
$stmt = $conn->prepare("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN is_approved = 0 AND is_active = 1 THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN is_approved = 1 AND is_active = 1 THEN 1 ELSE 0 END) as approved,
        SUM(CASE WHEN is_approved = 0 AND is_active = 0 THEN 1 ELSE 0 END) as rejected
    FROM users 
    WHERE user_type = 'parent'
");
$stmt->execute();
$stats = $stmt->fetch();

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parent Approvals - <?php echo $school_info['name']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            width: 250px;
            z-index: 1000;
        }
        
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .content-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-approved {
            background: #d4edda;
            color: #155724;
        }
        
        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .btn-action {
            padding: 5px 10px;
            font-size: 0.8rem;
            border-radius: 5px;
            margin: 2px;
        }
        
        .sidebar-brand {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }
        
        .sidebar-menu li {
            margin: 5px 0;
        }
        
        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0 10px;
            border-radius: 10px;
        }
        
        .filter-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h5 class="mb-0">
                <i class="fas fa-graduation-cap me-2"></i>
                IGSS Admin
            </h5>
        </div>

        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
            <li><a href="manage-users.php"><i class="fas fa-users me-2"></i>Manage Users</a></li>
            <li><a href="parent-approvals.php" class="active"><i class="fas fa-user-check me-2"></i>Parent Approvals</a></li>
            <li><a href="academic-settings.php"><i class="fas fa-calendar-alt me-2"></i>Academic Settings</a></li>
            <li><a href="assign-subjects.php"><i class="fas fa-book me-2"></i>Assign Subjects</a></li>
            <li><a href="assign-form-teachers.php"><i class="fas fa-chalkboard-teacher me-2"></i>Form Teachers</a></li>
            <li><a href="manage-fees.php"><i class="fas fa-money-bill me-2"></i>Manage Fees</a></li>
            <li><a href="fee-confirmations.php"><i class="fas fa-money-check-alt me-2"></i>Fee Confirmations</a></li>
            <li><hr></li>
            <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><i class="fas fa-user-check me-2"></i>Parent Account Approvals</h4>
                    <small class="text-muted">Review and manage parent registration requests</small>
                </div>
                <div>
                    <button class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                </div>
            </div>
        </div>

        <!-- Alerts -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number text-warning"><?php echo $stats['pending']; ?></div>
                    <div class="stat-label">Pending Approval</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number text-success"><?php echo $stats['approved']; ?></div>
                    <div class="stat-label">Approved</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number text-danger"><?php echo $stats['rejected']; ?></div>
                    <div class="stat-label">Rejected</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number text-primary"><?php echo $stats['total']; ?></div>
                    <div class="stat-label">Total Registrations</div>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-card">
            <form method="GET" action="">
                <div class="row align-items-end">
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status Filter</label>
                        <select class="form-select" id="status" name="status" onchange="this.form.submit()">
                            <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending Approval</option>
                            <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>Approved</option>
                            <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                            <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>All</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search by name, email, or user code...">
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                        <a href="parent-approvals.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Parent Registrations Table -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    <?php
                    $status_labels = [
                        'pending' => 'Pending Approval',
                        'approved' => 'Approved',
                        'rejected' => 'Rejected',
                        'all' => 'All'
                    ];
                    echo $status_labels[$status_filter] ?? 'All';
                    ?> Parent Registrations (<?php echo count($parents); ?>)
                </h5>
            </div>

            <?php if (!empty($parents)): ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="parentsTable">
                        <thead>
                            <tr>
                                <th>User Code</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Occupation</th>
                                <th>Registration Date</th>
                                <th>Status</th>
                                <th>Linked Wards</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($parents as $parent): ?>
                                <tr>
                                    <td>
                                        <code><?php echo htmlspecialchars($parent['user_code']); ?></code>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($parent['first_name'] . ' ' . $parent['last_name']); ?></strong>
                                        <?php if ($parent['middle_name']): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($parent['middle_name']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="mailto:<?php echo htmlspecialchars($parent['email']); ?>">
                                            <?php echo htmlspecialchars($parent['email']); ?>
                                        </a>
                                    </td>
                                    <td>
                                        <?php if ($parent['phone']): ?>
                                            <a href="tel:<?php echo htmlspecialchars($parent['phone']); ?>">
                                                <?php echo htmlspecialchars($parent['phone']); ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">Not provided</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo htmlspecialchars($parent['occupation'] ?? 'Not specified'); ?>
                                        <?php if ($parent['workplace']): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($parent['workplace']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo date('M d, Y', strtotime($parent['created_at'])); ?>
                                        <br><small class="text-muted"><?php echo date('g:i A', strtotime($parent['created_at'])); ?></small>
                                    </td>
                                    <td>
                                        <?php if ($parent['is_approved'] && $parent['is_active']): ?>
                                            <span class="status-badge status-approved">
                                                <i class="fas fa-check-circle me-1"></i>Approved
                                            </span>
                                        <?php elseif (!$parent['is_approved'] && $parent['is_active']): ?>
                                            <span class="status-badge status-pending">
                                                <i class="fas fa-clock me-1"></i>Pending
                                            </span>
                                        <?php else: ?>
                                            <span class="status-badge status-rejected">
                                                <i class="fas fa-times-circle me-1"></i>Rejected
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo $parent['linked_wards_count']; ?> ward<?php echo $parent['linked_wards_count'] != 1 ? 's' : ''; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group-vertical" role="group">
                                            <button class="btn btn-outline-primary btn-action"
                                                    onclick="viewParentDetails(<?php echo $parent['id']; ?>)">
                                                <i class="fas fa-eye me-1"></i>View
                                            </button>

                                            <?php if (!$parent['is_approved'] && $parent['is_active']): ?>
                                                <button class="btn btn-success btn-action"
                                                        onclick="approveParent(<?php echo $parent['id']; ?>, '<?php echo htmlspecialchars($parent['first_name'] . ' ' . $parent['last_name']); ?>')">
                                                    <i class="fas fa-check me-1"></i>Approve
                                                </button>
                                                <button class="btn btn-danger btn-action"
                                                        onclick="rejectParent(<?php echo $parent['id']; ?>, '<?php echo htmlspecialchars($parent['first_name'] . ' ' . $parent['last_name']); ?>')">
                                                    <i class="fas fa-times me-1"></i>Reject
                                                </button>
                                            <?php elseif (!$parent['is_active']): ?>
                                                <button class="btn btn-warning btn-action"
                                                        onclick="reactivateParent(<?php echo $parent['id']; ?>, '<?php echo htmlspecialchars($parent['first_name'] . ' ' . $parent['last_name']); ?>')">
                                                    <i class="fas fa-undo me-1"></i>Reactivate
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Parent Registrations Found</h5>
                    <p class="text-muted">
                        <?php if ($status_filter === 'pending'): ?>
                            No pending parent registrations at this time.
                        <?php elseif ($status_filter === 'approved'): ?>
                            No approved parent accounts found.
                        <?php elseif ($status_filter === 'rejected'): ?>
                            No rejected parent accounts found.
                        <?php else: ?>
                            No parent registrations match your search criteria.
                        <?php endif; ?>
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Approval Action Modal -->
    <div class="modal fade" id="approvalActionModal" tabindex="-1" aria-labelledby="approvalActionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="approvalActionModalLabel">
                        <i class="fas fa-user-check me-2"></i>Confirm Action
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" id="modalAction">
                        <input type="hidden" name="user_id" id="modalUserId">

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <span id="modalMessage"></span>
                        </div>

                        <div class="mb-3">
                            <label for="admin_notes" class="form-label">
                                <i class="fas fa-sticky-note me-1"></i>Admin Notes (Optional)
                            </label>
                            <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3"
                                      placeholder="Add any notes about this decision..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="submit" class="btn" id="modalSubmitBtn">
                            <i class="fas fa-check me-2"></i>Confirm
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#parentsTable').DataTable({
                "pageLength": 25,
                "order": [[ 5, "desc" ]],
                "columnDefs": [
                    { "orderable": false, "targets": 8 }
                ]
            });
        });

        function viewParentDetails(userId) {
            // For now, show a simple alert with user ID
            // In a full implementation, this would load detailed parent information
            alert(`View details for parent ID: ${userId}\n\nThis would show:\n- Complete registration information\n- Linked wards\n- Approval history\n- Contact details`);
        }

        function approveParent(userId, parentName) {
            showApprovalModal('approve', userId, parentName,
                `Are you sure you want to approve the parent account for <strong>${parentName}</strong>? This will grant them access to the parent portal.`,
                'btn-success');
        }

        function rejectParent(userId, parentName) {
            showApprovalModal('reject', userId, parentName,
                `Are you sure you want to reject the parent account for <strong>${parentName}</strong>? This will deactivate their account.`,
                'btn-danger');
        }

        function reactivateParent(userId, parentName) {
            showApprovalModal('reactivate', userId, parentName,
                `Are you sure you want to reactivate the parent account for <strong>${parentName}</strong>? This will allow them to access the system again.`,
                'btn-warning');
        }

        function showApprovalModal(action, userId, parentName, message, buttonClass) {
            document.getElementById('modalAction').value = action;
            document.getElementById('modalUserId').value = userId;
            document.getElementById('modalMessage').innerHTML = message;
            document.getElementById('admin_notes').value = '';

            const submitBtn = document.getElementById('modalSubmitBtn');
            submitBtn.className = `btn ${buttonClass}`;

            const actionLabels = {
                'approve': 'Approve',
                'reject': 'Reject',
                'reactivate': 'Reactivate'
            };

            submitBtn.innerHTML = `<i class="fas fa-check me-2"></i>${actionLabels[action]}`;

            const modal = new bootstrap.Modal(document.getElementById('approvalActionModal'));
            modal.show();
        }

        // Add hover effects to stat cards
        document.addEventListener('DOMContentLoaded', function() {
            const statCards = document.querySelectorAll('.stat-card');

            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
