<?php
/**
 * Parent Dashboard - Enhanced with Ward Management
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a parent
if (!isLoggedIn() || $_SESSION['user_type'] !== 'parent') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get parent information including approval status
$stmt = $conn->prepare("
    SELECT p.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, u.user_code, u.is_approved
    FROM parents p
    JOIN users u ON p.user_id = u.id
    WHERE p.user_id = ?
");
$stmt->execute([$user_id]);
$parent = $stmt->fetch();

if (!$parent) {
    header('Location: ../login.php');
    exit();
}

// Check if parent account is approved
if (!$parent['is_approved']) {
    header('Location: pending-approval.php');
    exit();
}

// Get all linked wards (children)
$stmt = $conn->prepare("
    SELECT
        s.id as student_id,
        s.admission_number,
        u.first_name,
        u.last_name,
        u.middle_name,
        u.profile_image,
        u.gender,
        c.class_name,
        l.level_name,
        psr.relationship_type,
        psr.is_primary,
        s.current_session_id,
        s.current_term_id
    FROM parent_student_relationships psr
    JOIN students s ON psr.student_id = s.id
    JOIN users u ON s.user_id = u.id
    JOIN classes c ON s.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    WHERE psr.parent_id = ?
    ORDER BY psr.is_primary DESC, u.first_name ASC
");
$stmt->execute([$parent['id']]);
$wards = $stmt->fetchAll();

// Function to get child relationship from parent relationship
function getChildRelationship($parentRelationship, $childGender) {
    $relationships = [
        'Father' => $childGender === 'Male' ? 'Son' : 'Daughter',
        'Mother' => $childGender === 'Male' ? 'Son' : 'Daughter',
        'Guardian' => 'Ward',
        'Uncle' => $childGender === 'Male' ? 'Nephew' : 'Niece',
        'Aunt' => $childGender === 'Male' ? 'Nephew' : 'Niece',
        'Grandparent' => 'Grandchild',
        'Sibling' => 'Sibling',
        'Other' => 'Ward'
    ];

    return $relationships[$parentRelationship] ?? 'Ward';
}

// Get attendance and grade data for each ward
foreach ($wards as &$ward) {
    // Get real attendance data
    $stmt = $conn->prepare("
        SELECT
            COUNT(*) as total_days,
            SUM(CASE WHEN status = 'Present' THEN 1 ELSE 0 END) as present_days
        FROM attendance
        WHERE student_id = ?
        AND attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    ");
    $stmt->execute([$ward['student_id']]);
    $attendance_data = $stmt->fetch();

    $ward['attendance_percentage'] = 0;
    if ($attendance_data['total_days'] > 0) {
        $ward['attendance_percentage'] = round(($attendance_data['present_days'] / $attendance_data['total_days']) * 100, 1);
    }

    // Get real grade data
    $stmt = $conn->prepare("
        SELECT AVG(total_score) as average_score
        FROM student_grades
        WHERE student_id = ?
        AND created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    ");
    $stmt->execute([$ward['student_id']]);
    $grade_data = $stmt->fetch();

    $ward['average_grade'] = 'No Data';
    if ($grade_data['average_score'] !== null) {
        $avg_score = round($grade_data['average_score'], 1);
        if ($avg_score >= 70) $ward['average_grade'] = 'A';
        elseif ($avg_score >= 60) $ward['average_grade'] = 'B';
        elseif ($avg_score >= 50) $ward['average_grade'] = 'C';
        elseif ($avg_score >= 45) $ward['average_grade'] = 'D';
        elseif ($avg_score >= 40) $ward['average_grade'] = 'E';
        else $ward['average_grade'] = 'F';
    }

    // Get outstanding fees
    $stmt = $conn->prepare("
        SELECT COALESCE(SUM(amount_paid), 0) as total_paid
        FROM fee_payments
        WHERE student_id = ?
        AND status = 'Confirmed'
    ");
    $stmt->execute([$ward['student_id']]);
    $fee_data = $stmt->fetch();

    // Get expected fees based on student level
    $stmt = $conn->prepare("
        SELECT l.level_code
        FROM students s
        JOIN classes c ON s.class_id = c.id
        JOIN levels l ON c.level_id = l.id
        WHERE s.id = ?
    ");
    $stmt->execute([$ward['student_id']]);
    $level_data = $stmt->fetch();

    // Calculate expected fees (simplified - using total from fee_types)
    $stmt = $conn->prepare("
        SELECT SUM(CASE WHEN ? = 'JS' THEN amount_js ELSE amount_ss END) as expected_fees
        FROM fee_types
        WHERE is_active = 1 AND is_mandatory = 1
    ");
    $stmt->execute([$level_data['level_code'] ?? 'JS']);
    $expected_data = $stmt->fetch();

    $ward['outstanding_fees'] = max(0, ($expected_data['expected_fees'] ?? 0) - ($fee_data['total_paid'] ?? 0));

    // Set child relationship
    $ward['child_relationship'] = getChildRelationship($ward['relationship_type'], $ward['gender']);
}

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();

$message = '';
$error = '';

// Handle ward linking
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['link_ward'])) {
    $admission_number = sanitizeInput($_POST['admission_number']);
    $relationship_type = sanitizeInput($_POST['relationship_type']);
    $is_primary = isset($_POST['is_primary']) ? 1 : 0;

    if (empty($admission_number) || empty($relationship_type)) {
        $error = 'Please fill in all required fields.';
    } else {
        try {
            // Check if student exists
            $stmt = $conn->prepare("
                SELECT s.id, u.first_name, u.last_name
                FROM students s
                JOIN users u ON s.user_id = u.id
                WHERE s.admission_number = ?
            ");
            $stmt->execute([$admission_number]);
            $student = $stmt->fetch();

            if (!$student) {
                $error = 'No student found with admission number: ' . $admission_number;
            } else {
                // Check if relationship already exists
                $stmt = $conn->prepare("
                    SELECT id FROM parent_student_relationships
                    WHERE parent_id = ? AND student_id = ?
                ");
                $stmt->execute([$parent['id'], $student['id']]);

                if ($stmt->fetch()) {
                    $error = 'This student is already linked to your account.';
                } else {
                    // If this is set as primary, remove primary status from other relationships
                    if ($is_primary) {
                        $stmt = $conn->prepare("
                            UPDATE parent_student_relationships
                            SET is_primary = 0
                            WHERE parent_id = ?
                        ");
                        $stmt->execute([$parent['id']]);
                    }

                    // Create the relationship
                    $stmt = $conn->prepare("
                        INSERT INTO parent_student_relationships
                        (parent_id, student_id, relationship_type, is_primary)
                        VALUES (?, ?, ?, ?)
                    ");
                    $stmt->execute([$parent['id'], $student['id'], $relationship_type, $is_primary]);

                    $message = 'Successfully linked ' . $student['first_name'] . ' ' . $student['last_name'] . ' to your account.';

                    // Refresh wards list
                    $stmt = $conn->prepare("
                        SELECT
                            s.id as student_id,
                            s.admission_number,
                            u.first_name,
                            u.last_name,
                            u.middle_name,
                            u.profile_image,
                            u.gender,
                            c.class_name,
                            l.level_name,
                            psr.relationship_type,
                            psr.is_primary,
                            s.current_session_id,
                            s.current_term_id
                        FROM parent_student_relationships psr
                        JOIN students s ON psr.student_id = s.id
                        JOIN users u ON s.user_id = u.id
                        JOIN classes c ON s.class_id = c.id
                        JOIN levels l ON c.level_id = l.id
                        WHERE psr.parent_id = ?
                        ORDER BY psr.is_primary DESC, u.first_name ASC
                    ");
                    $stmt->execute([$parent['id']]);
                    $wards = $stmt->fetchAll();

                    // Get attendance and grade data for each ward
                    foreach ($wards as &$ward) {
                        // Get real attendance data
                        $stmt = $conn->prepare("
                            SELECT
                                COUNT(*) as total_days,
                                SUM(CASE WHEN status = 'Present' THEN 1 ELSE 0 END) as present_days
                            FROM attendance
                            WHERE student_id = ?
                            AND attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                        ");
                        $stmt->execute([$ward['student_id']]);
                        $attendance_data = $stmt->fetch();

                        $ward['attendance_percentage'] = 0;
                        if ($attendance_data['total_days'] > 0) {
                            $ward['attendance_percentage'] = round(($attendance_data['present_days'] / $attendance_data['total_days']) * 100, 1);
                        }

                        // Get real grade data
                        $stmt = $conn->prepare("
                            SELECT AVG(total_score) as average_score
                            FROM student_grades
                            WHERE student_id = ?
                            AND created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                        ");
                        $stmt->execute([$ward['student_id']]);
                        $grade_data = $stmt->fetch();

                        $ward['average_grade'] = 'No Data';
                        if ($grade_data['average_score'] !== null) {
                            $avg_score = round($grade_data['average_score'], 1);
                            if ($avg_score >= 70) $ward['average_grade'] = 'A';
                            elseif ($avg_score >= 60) $ward['average_grade'] = 'B';
                            elseif ($avg_score >= 50) $ward['average_grade'] = 'C';
                            elseif ($avg_score >= 45) $ward['average_grade'] = 'D';
                            elseif ($avg_score >= 40) $ward['average_grade'] = 'E';
                            else $ward['average_grade'] = 'F';
                        }

                        // Get outstanding fees
                        $stmt = $conn->prepare("
                            SELECT COALESCE(SUM(amount_paid), 0) as total_paid
                            FROM fee_payments
                            WHERE student_id = ?
                            AND status = 'Confirmed'
                        ");
                        $stmt->execute([$ward['student_id']]);
                        $fee_data = $stmt->fetch();

                        // Get expected fees based on student level
                        $stmt = $conn->prepare("
                            SELECT l.level_code
                            FROM students s
                            JOIN classes c ON s.class_id = c.id
                            JOIN levels l ON c.level_id = l.id
                            WHERE s.id = ?
                        ");
                        $stmt->execute([$ward['student_id']]);
                        $level_data = $stmt->fetch();

                        // Calculate expected fees (simplified - using total from fee_types)
                        $stmt = $conn->prepare("
                            SELECT SUM(CASE WHEN ? = 'JS' THEN amount_js ELSE amount_ss END) as expected_fees
                            FROM fee_types
                            WHERE is_active = 1 AND is_mandatory = 1
                        ");
                        $stmt->execute([$level_data['level_code'] ?? 'JS']);
                        $expected_data = $stmt->fetch();

                        $ward['outstanding_fees'] = max(0, ($expected_data['expected_fees'] ?? 0) - ($fee_data['total_paid'] ?? 0));

                        // Set child relationship
                        $ward['child_relationship'] = getChildRelationship($ward['relationship_type'], $ward['gender']);
                    }
                }
            }
        } catch (Exception $e) {
            $error = 'Failed to link student: ' . $e->getMessage();
        }
    }
}

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parent Dashboard - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 50%, #ff4500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }

        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 1050; /* Higher than Bootstrap's default modal z-index (1040) */
        }

        /* Ensure dropdown menu appears above all other content */
        .navbar-custom .dropdown-menu {
            z-index: 1060; /* Even higher than navbar */
            position: absolute;
        }

        /* Fix dropdown positioning */
        .navbar-custom .nav-item.dropdown {
            position: relative;
        }

        /* Style the dropdown menu */
        .navbar-custom .dropdown-menu {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            margin-top: 8px;
            min-width: 220px;
        }

        .navbar-custom .dropdown-item {
            padding: 12px 20px;
            border-radius: 10px;
            margin: 4px 8px;
            transition: all 0.3s ease;
        }

        .navbar-custom .dropdown-item:hover {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            color: white;
            transform: translateX(5px);
        }

        .navbar-custom .dropdown-item i {
            width: 20px;
            text-align: center;
        }

        .dashboard-container {
            padding: 20px;
            position: relative;
            z-index: 1; /* Keep lower than navbar */
        }

        .parent-header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .parent-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff8c00, #ff6b35);
        }

        .dashboard-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff8c00, #ff6b35);
        }

        .dashboard-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .ward-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .ward-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #28a745, #20c997);
        }

        .ward-card:hover {
            transform: translateY(-5px) scale(1.01);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .ward-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            margin-right: 20px;
            box-shadow: 0 4px 15px rgba(255, 140, 0, 0.3);
        }

        .btn-orange {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            border: none;
            color: white;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 140, 0, 0.3);
        }

        .btn-orange:hover {
            background: linear-gradient(135deg, #ff9500 0%, #ff7635 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 140, 0, 0.4);
        }

        .btn-action {
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 2px;
        }

        .btn-action:hover {
            transform: translateY(-2px);
        }

        .primary-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }

        .relationship-badge {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
        }

        .admission-number {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #ff8c00;
            font-size: 1rem;
            background: rgba(255, 140, 0, 0.1);
            padding: 4px 8px;
            border-radius: 6px;
        }

        .no-wards-container {
            text-align: center;
            padding: 60px 20px;
        }

        .no-wards-container i {
            color: #ff8c00;
            margin-bottom: 20px;
        }

        /* Enhanced Ward Linking Modal Styles */
        .ward-link-step {
            transition: all 0.3s ease;
        }

        .student-preview-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #ff6b35;
            border-radius: 15px;
            padding: 2rem;
            margin: 1rem 0;
            position: relative;
            overflow: hidden;
        }

        .student-preview-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff6b35, #f7931e);
        }

        .student-avatar-large {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0 auto 1rem;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }

        .student-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .info-item {
            background: white;
            padding: 1rem;
            border-radius: 10px;
            border-left: 4px solid #ff6b35;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .info-label {
            font-size: 0.85rem;
            color: #6c757d;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .info-value {
            font-size: 1rem;
            color: #2c3e50;
            font-weight: 500;
        }

        .search-status {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            min-height: 80px;
        }

        .input-group .btn {
            border-color: #ced4da;
        }

        .input-group .btn:hover {
            background-color: #ff6b35;
            border-color: #ff6b35;
            color: white;
        }

        #searchResults {
            animation: fadeInUp 0.5s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .confirmation-highlight {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .linking-progress {
            text-align: center;
            padding: 2rem;
        }

        .linking-progress .spinner-border {
            width: 3rem;
            height: 3rem;
        }

        .quick-stats {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ff8c00;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-users me-2 text-orange"></i>
                IGSS Parent Portal
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle fw-semibold" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($parent['first_name'] . ' ' . $parent['last_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
                        <li><a class="dropdown-item" href="ward-management.php"><i class="fas fa-users me-2"></i>Ward Management</a></li>
                        <li><a class="dropdown-item" href="payments.php"><i class="fas fa-credit-card me-2"></i>Fee Payments</a></li>
                        <li><a class="dropdown-item" href="messages.php"><i class="fas fa-envelope me-2"></i>Messages</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="dashboard-container">
        <div class="container">
            <!-- Parent Header -->
            <div class="parent-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-2">
                            <i class="fas fa-home me-2"></i>
                            Welcome, <?php echo htmlspecialchars($parent['first_name'] . ' ' . $parent['last_name']); ?>
                        </h2>
                        <p class="text-muted mb-0">
                            <i class="fas fa-briefcase me-2"></i>
                            <?php echo htmlspecialchars($parent['occupation'] ?? 'Parent/Guardian'); ?>
                            <span class="ms-3">
                                <i class="fas fa-id-card me-1"></i>
                                Parent ID: <?php echo htmlspecialchars($parent['user_code']); ?>
                            </span>
                        </p>
                        <p class="text-muted mb-0 mt-1">
                            <i class="fas fa-calendar me-2"></i>
                            Session: <?php echo $current_academic['session']; ?> |
                            Term: <?php echo TERMS[$current_academic['term']]; ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-orange" data-bs-toggle="modal" data-bs-target="#linkWardModal">
                            <i class="fas fa-plus me-2"></i>Link New Ward
                        </button>
                    </div>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- My Wards Section -->
                <div class="col-lg-8">
                    <div class="dashboard-card">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="mb-0">
                                <i class="fas fa-users me-2"></i>
                                My Wards (<?php echo count($wards); ?>)
                            </h5>
                            <?php if (!empty($wards)): ?>
                                <a href="ward-management.php" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-cog me-1"></i>Manage Wards
                                </a>
                            <?php endif; ?>
                        </div>

                        <?php if (!empty($wards)): ?>
                            <?php foreach ($wards as $ward): ?>
                                <div class="ward-card">
                                    <div class="d-flex align-items-center">
                                        <div class="ward-avatar">
                                            <?php echo strtoupper(substr($ward['first_name'], 0, 1) . substr($ward['last_name'], 0, 1)); ?>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <?php echo htmlspecialchars($ward['first_name'] . ' ' . $ward['last_name']); ?>
                                                        <?php if ($ward['is_primary']): ?>
                                                            <span class="primary-badge ms-2">Primary Ward</span>
                                                        <?php endif; ?>
                                                    </h6>
                                                    <p class="text-muted mb-1">
                                                        <i class="fas fa-graduation-cap me-1"></i>
                                                        <?php echo htmlspecialchars($ward['class_name'] . ' - ' . $ward['level_name']); ?>
                                                    </p>
                                                    <div class="d-flex align-items-center gap-3">
                                                        <span class="admission-number">
                                                            <?php echo htmlspecialchars($ward['admission_number']); ?>
                                                        </span>
                                                        <span class="relationship-badge">
                                                            <?php echo htmlspecialchars($ward['child_relationship']); ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="text-end">
                                                    <div class="btn-group-vertical" role="group">
                                                        <a href="ward-details.php?id=<?php echo $ward['student_id']; ?>"
                                                           class="btn btn-outline-primary btn-sm btn-action">
                                                            <i class="fas fa-eye me-1"></i>View Details
                                                        </a>
                                                        <a href="ward-grades.php?id=<?php echo $ward['student_id']; ?>"
                                                           class="btn btn-outline-success btn-sm btn-action">
                                                            <i class="fas fa-chart-line me-1"></i>Academic Records
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="quick-stats">
                                                <div class="stat-item">
                                                    <div class="stat-number">
                                                        <?php echo $ward['attendance_percentage'] > 0 ? $ward['attendance_percentage'] . '%' : 'No Data'; ?>
                                                    </div>
                                                    <div class="stat-label">Attendance</div>
                                                </div>
                                                <div class="stat-item">
                                                    <div class="stat-number"><?php echo htmlspecialchars($ward['average_grade']); ?></div>
                                                    <div class="stat-label">Average Grade</div>
                                                </div>
                                                <div class="stat-item">
                                                    <div class="stat-number">₦<?php echo number_format($ward['outstanding_fees'], 0); ?></div>
                                                    <div class="stat-label">Outstanding Fees</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="no-wards-container">
                                <i class="fas fa-user-plus fa-4x"></i>
                                <h5 class="mt-3 mb-2">No Wards Linked Yet</h5>
                                <p class="text-muted mb-4">
                                    Start by linking your child's account using their admission number.<br>
                                    You can link multiple children to manage all their academic information in one place.
                                </p>
                                <button class="btn btn-orange btn-lg" data-bs-toggle="modal" data-bs-target="#linkWardModal">
                                    <i class="fas fa-plus me-2"></i>Link Your First Ward
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions & Information -->
                <div class="col-lg-4">
                    <div class="dashboard-card">
                        <h5 class="mb-4">
                            <i class="fas fa-bolt me-2"></i>
                            Quick Actions
                        </h5>

                        <div class="d-grid gap-3">
                            <button class="btn btn-orange" data-bs-toggle="modal" data-bs-target="#linkWardModal">
                                <i class="fas fa-plus me-2"></i>Link New Ward
                            </button>
                            <a href="payments.php" class="btn btn-outline-success">
                                <i class="fas fa-credit-card me-2"></i>Fee Payments
                            </a>
                            <a href="attendance-overview.php" class="btn btn-outline-info">
                                <i class="fas fa-calendar-check me-2"></i>Attendance Overview
                            </a>
                            <a href="academic-reports.php" class="btn btn-outline-warning">
                                <i class="fas fa-chart-bar me-2"></i>Academic Reports
                            </a>
                            <a href="messages.php" class="btn btn-outline-secondary">
                                <i class="fas fa-envelope me-2"></i>Messages & Notices
                            </a>
                        </div>
                    </div>

                    <?php if (!empty($wards)): ?>
                    <div class="dashboard-card">
                        <h5 class="mb-3">
                            <i class="fas fa-lightbulb me-2"></i>
                            Parenting Tips
                        </h5>
                        <ul class="list-unstyled">
                            <li class="mb-3">
                                <i class="fas fa-check text-success me-2"></i>
                                <strong>Monitor Daily:</strong> Check attendance and assignments regularly
                            </li>
                            <li class="mb-3">
                                <i class="fas fa-check text-success me-2"></i>
                                <strong>Stay Connected:</strong> Communicate with teachers and school
                            </li>
                            <li class="mb-3">
                                <i class="fas fa-check text-success me-2"></i>
                                <strong>Pay On Time:</strong> Ensure fees are paid before deadlines
                            </li>
                            <li class="mb-3">
                                <i class="fas fa-check text-success me-2"></i>
                                <strong>Review Progress:</strong> Analyze grades and academic trends
                            </li>
                        </ul>
                    </div>

                    <div class="dashboard-card">
                        <h5 class="mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            Portal Statistics
                        </h5>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="stat-number"><?php echo count($wards); ?></div>
                                <div class="stat-label">Linked Wards</div>
                            </div>
                            <div class="col-6">
                                <div class="stat-number">
                                    <?php echo $current_academic['session']; ?>
                                </div>
                                <div class="stat-label">Academic Session</div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Link Ward Modal -->
    <div class="modal fade" id="linkWardModal" tabindex="-1" aria-labelledby="linkWardModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="linkWardModalLabel">
                        <i class="fas fa-link me-2"></i>Link New Ward
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Step 1: Search Student -->
                    <div id="searchStep" class="ward-link-step">
                        <div class="alert alert-info">
                            <i class="fas fa-search me-2"></i>
                            <strong>Find Your Ward:</strong> Enter your child's admission number to search for their information.
                            The student details will appear instantly once found.
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="search_admission_number" class="form-label">
                                        <i class="fas fa-id-card me-1"></i>Student Admission Number *
                                    </label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="search_admission_number"
                                               placeholder="e.g., IGSS-2025-0001 or ADM20250001" autocomplete="off">
                                        <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">
                                        Enter the student code (IGSS-YYYY-XXXX) or admission number as shown on school documents
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="search-status">
                                    <div id="searchLoading" class="text-center" style="display: none;">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Searching...</span>
                                        </div>
                                        <div class="mt-2">Searching for student...</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Search Results -->
                        <div id="searchResults" style="display: none;">
                            <hr>
                            <div id="studentInfo"></div>
                        </div>

                        <!-- Error Display -->
                        <div id="searchError" class="alert alert-danger" style="display: none;">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <span id="errorMessage"></span>
                        </div>
                    </div>

                    <!-- Step 2: Confirm Linking -->
                    <div id="confirmStep" class="ward-link-step" style="display: none;">
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Student Found!</strong> Please confirm your relationship and complete the linking process.
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="relationship_type" class="form-label">
                                        <i class="fas fa-users me-1"></i>Your Relationship to Student *
                                    </label>
                                    <select class="form-select" id="relationship_type" required>
                                        <option value="">Select Relationship</option>
                                        <option value="Father">Father</option>
                                        <option value="Mother">Mother</option>
                                        <option value="Guardian">Guardian</option>
                                        <option value="Uncle">Uncle</option>
                                        <option value="Aunt">Aunt</option>
                                        <option value="Grandparent">Grandparent</option>
                                        <option value="Sibling">Sibling</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="is_primary">
                                        <label class="form-check-label" for="is_primary">
                                            <strong>Set as Primary Ward</strong>
                                            <small class="text-muted d-block">
                                                Primary ward will be displayed first and used for main communications
                                            </small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="button" id="backBtn" class="btn btn-outline-primary" style="display: none;">
                        <i class="fas fa-arrow-left me-2"></i>Back to Search
                    </button>
                    <button type="button" id="linkBtn" class="btn btn-orange" style="display: none;">
                        <i class="fas fa-link me-2"></i>Link Ward
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced Ward Linking System
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Enhanced Ward Linking System - Initializing...');

            // Global variables
            let currentStudent = null;
            let searchTimeout = null;

            // DOM elements
            const modal = document.getElementById('linkWardModal');
            const searchStep = document.getElementById('searchStep');
            const confirmStep = document.getElementById('confirmStep');
            const searchInput = document.getElementById('search_admission_number');
            const searchBtn = document.getElementById('searchBtn');
            const searchLoading = document.getElementById('searchLoading');
            const searchResults = document.getElementById('searchResults');
            const searchError = document.getElementById('searchError');
            const studentInfo = document.getElementById('studentInfo');
            const backBtn = document.getElementById('backBtn');
            const linkBtn = document.getElementById('linkBtn');
            const relationshipSelect = document.getElementById('relationship_type');
            const isPrimaryCheck = document.getElementById('is_primary');

            // Initialize modal event listeners
            if (modal) {
                modal.addEventListener('show.bs.modal', function() {
                    resetModal();
                });

                modal.addEventListener('hidden.bs.modal', function() {
                    resetModal();
                });
            }

            // Search functionality
            if (searchInput) {
                // Real-time search as user types
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    const admissionNumber = this.value.trim();

                    if (admissionNumber.length >= 3) {
                        searchTimeout = setTimeout(() => {
                            searchStudent(admissionNumber);
                        }, 500); // Wait 500ms after user stops typing
                    } else {
                        hideSearchResults();
                    }
                });

                // Search on Enter key
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        const admissionNumber = this.value.trim();
                        if (admissionNumber) {
                            searchStudent(admissionNumber);
                        }
                    }
                });
            }

            // Search button click
            if (searchBtn) {
                searchBtn.addEventListener('click', function() {
                    const admissionNumber = searchInput.value.trim();
                    if (admissionNumber) {
                        searchStudent(admissionNumber);
                    } else {
                        showError('Please enter an admission number');
                    }
                });
            }

            // Back button
            if (backBtn) {
                backBtn.addEventListener('click', function() {
                    showSearchStep();
                });
            }

            // Link button
            if (linkBtn) {
                linkBtn.addEventListener('click', function() {
                    linkWard();
                });
            }

            // Ward card interactions
            const wardCards = document.querySelectorAll('.ward-card');
            wardCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Action button animations
            const actionButtons = document.querySelectorAll('.btn-action');
            actionButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // Functions
            function resetModal() {
                currentStudent = null;
                searchInput.value = '';
                relationshipSelect.value = '';
                isPrimaryCheck.checked = false;
                hideSearchResults();
                hideError();
                showSearchStep();
            }

            function showSearchStep() {
                searchStep.style.display = 'block';
                confirmStep.style.display = 'none';
                backBtn.style.display = 'none';
                linkBtn.style.display = 'none';
            }

            function showConfirmStep() {
                searchStep.style.display = 'none';
                confirmStep.style.display = 'block';
                backBtn.style.display = 'inline-block';
                linkBtn.style.display = 'inline-block';
            }

            function showLoading() {
                searchLoading.style.display = 'block';
                hideSearchResults();
                hideError();
            }

            function hideLoading() {
                searchLoading.style.display = 'none';
            }

            function showSearchResults() {
                searchResults.style.display = 'block';
                hideError();
            }

            function hideSearchResults() {
                searchResults.style.display = 'none';
            }

            function showError(message) {
                document.getElementById('errorMessage').textContent = message;
                searchError.style.display = 'block';
                hideSearchResults();
                hideLoading();
            }

            function hideError() {
                searchError.style.display = 'none';
            }

            function searchStudent(admissionNumber) {
                showLoading();

                const formData = new FormData();
                formData.append('admission_number', admissionNumber);

                fetch('ajax/search-student.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();

                    if (data.success) {
                        currentStudent = data.student;
                        displayStudentInfo(data.student);
                        showSearchResults();
                    } else {
                        showError(data.error || 'Student not found');
                    }
                })
                .catch(error => {
                    hideLoading();
                    console.error('Search error:', error);
                    showError('An error occurred while searching. Please try again.');
                });
            }

            function displayStudentInfo(student) {
                const profileImage = student.profile_image ?
                    `<img src="../${student.profile_image}" alt="Profile" style="width: 80px; height: 80px; border-radius: 50%; object-fit: cover; border: 3px solid #ff6b35;">` :
                    `<div class="student-avatar-large">${student.initials}</div>`;

                const html = `
                    <div class="student-preview-card">
                        <div class="text-center mb-3">
                            ${profileImage}
                            <h4 class="mb-1">${student.full_name}</h4>
                            <p class="text-muted mb-0">Admission Number: <strong>${student.admission_number}</strong></p>
                        </div>

                        <div class="student-info-grid">
                            <div class="info-item">
                                <div class="info-label">Class & Level</div>
                                <div class="info-value">${student.class_name} - ${student.level_name}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Gender</div>
                                <div class="info-value">${student.gender}</div>
                            </div>
                            ${student.age ? `
                            <div class="info-item">
                                <div class="info-label">Age</div>
                                <div class="info-value">${student.age}</div>
                            </div>
                            ` : ''}
                            ${student.date_of_birth ? `
                            <div class="info-item">
                                <div class="info-label">Date of Birth</div>
                                <div class="info-value">${student.date_of_birth}</div>
                            </div>
                            ` : ''}
                            ${student.admission_date ? `
                            <div class="info-item">
                                <div class="info-label">Admission Date</div>
                                <div class="info-value">${student.admission_date}</div>
                            </div>
                            ` : ''}
                            ${student.is_boarding ? `
                            <div class="info-item">
                                <div class="info-label">Boarding Status</div>
                                <div class="info-value">Boarding Student</div>
                            </div>
                            ` : ''}
                        </div>

                        <div class="text-center mt-3">
                            <button type="button" class="btn btn-success" onclick="proceedToLink()">
                                <i class="fas fa-arrow-right me-2"></i>Proceed to Link This Student
                            </button>
                        </div>
                    </div>
                `;

                studentInfo.innerHTML = html;
            }

            // Make proceedToLink available globally
            window.proceedToLink = function() {
                if (currentStudent) {
                    showConfirmStep();
                }
            };

            function linkWard() {
                const relationship = relationshipSelect.value;
                const isPrimary = isPrimaryCheck.checked;

                if (!relationship) {
                    alert('Please select your relationship to the student');
                    return;
                }

                if (!currentStudent) {
                    alert('No student selected');
                    return;
                }

                // Show linking progress
                const originalText = linkBtn.innerHTML;
                linkBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Linking Ward...';
                linkBtn.disabled = true;

                const formData = new FormData();
                formData.append('student_id', currentStudent.student_id);
                formData.append('relationship_type', relationship);
                formData.append('is_primary', isPrimary);

                fetch('ajax/link-ward.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        alert('Ward linked successfully! The page will refresh to show your updated ward list.');

                        // Close modal and refresh page
                        const modalInstance = bootstrap.Modal.getInstance(modal);
                        modalInstance.hide();

                        // Refresh page after a short delay
                        setTimeout(() => {
                            window.location.reload();
                        }, 500);
                    } else {
                        alert('Error: ' + (data.error || 'Failed to link ward'));
                        linkBtn.innerHTML = originalText;
                        linkBtn.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Linking error:', error);
                    alert('An error occurred while linking the ward. Please try again.');
                    linkBtn.innerHTML = originalText;
                    linkBtn.disabled = false;
                });
            }
        });
    </script>
</body>
</html>
