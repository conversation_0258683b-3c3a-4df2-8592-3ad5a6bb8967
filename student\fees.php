<?php
/**
 * Student Fee Status View
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a student
if (!isLoggedIn() || $_SESSION['user_type'] !== 'student') {
    header('Location: ../login.php');
    exit();
}

// Check if student account is approved
if (!$_SESSION['is_approved']) {
    header('Location: pending-approval.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get student information
$stmt = $conn->prepare("
    SELECT s.*, u.first_name, u.last_name, u.middle_name, u.user_code,
           c.class_name, l.level_name, l.level_code
    FROM students s
    JOIN users u ON s.user_id = u.id
    JOIN classes c ON s.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    WHERE s.user_id = ?
");
$stmt->execute([$user_id]);
$student = $stmt->fetch();

if (!$student) {
    header('Location: ../login.php');
    exit();
}

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();

// Get fee structure for student's level
$stmt = $conn->prepare("
    SELECT * FROM fee_types 
    WHERE is_active = 1 
    ORDER BY fee_name
");
$stmt->execute();
$fee_types = $stmt->fetchAll();

// Get payment history
$stmt = $conn->prepare("
    SELECT fp.*, ft.fee_name, ft.fee_description,
           DATE_FORMAT(fp.payment_date, '%M %d, %Y') as formatted_date,
           DATE_FORMAT(fp.created_at, '%M %d, %Y at %h:%i %p') as submitted_date
    FROM fee_payments fp
    LEFT JOIN fee_types ft ON fp.fee_type_id = ft.id
    WHERE fp.student_id = ? AND fp.session_id = ?
    ORDER BY fp.created_at DESC
");
$stmt->execute([$student['id'], $current_academic['current_session_id'] ?? 1]);
$payment_history = $stmt->fetchAll();

// Calculate fee summary
$total_fees = 0;
$total_paid = 0;
$total_pending = 0;

foreach ($fee_types as $fee_type) {
    $amount = ($student['level_code'] === 'JS') ? $fee_type['amount_js'] : $fee_type['amount_ss'];
    $total_fees += $amount;
}

foreach ($payment_history as $payment) {
    if ($payment['status'] === 'Confirmed') {
        $total_paid += $payment['amount_paid'];
    } elseif ($payment['status'] === 'Pending') {
        $total_pending += $payment['amount_paid'];
    }
}

$balance = $total_fees - $total_paid;
$payment_percentage = $total_fees > 0 ? round(($total_paid / $total_fees) * 100, 1) : 0;

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Fees - <?php echo htmlspecialchars($school_info['name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .navbar-custom .navbar-brand {
            color: #ff6b35 !important;
            font-weight: 700;
        }
        
        .navbar-custom .nav-link {
            color: #333 !important;
            font-weight: 500;
        }
        
        .main-container {
            padding: 2rem 0;
        }
        
        .page-header {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 107, 53, 0.1);
        }
        
        .fee-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 107, 53, 0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 107, 53, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }
        
        .stat-card.total::before { background: #6c757d; }
        .stat-card.paid::before { background: #28a745; }
        .stat-card.pending::before { background: #ffc107; }
        .stat-card.balance::before { background: #dc3545; }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin: 0 auto 1rem;
        }
        
        .stat-card.total .stat-icon { background: #6c757d; }
        .stat-card.paid .stat-icon { background: #28a745; }
        .stat-card.pending .stat-icon { background: #ffc107; }
        .stat-card.balance .stat-icon { background: #dc3545; }
        
        .stat-amount {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .stat-card.total .stat-amount { color: #6c757d; }
        .stat-card.paid .stat-amount { color: #28a745; }
        .stat-card.pending .stat-amount { color: #ffc107; }
        .stat-card.balance .stat-amount { color: #dc3545; }
        
        .payment-status {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem;
        }
        
        .status-confirmed { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        
        .progress-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            margin: 0 auto 1rem;
        }
        
        .progress-excellent { background: linear-gradient(135deg, #28a745, #20c997); }
        .progress-good { background: linear-gradient(135deg, #17a2b8, #138496); }
        .progress-average { background: linear-gradient(135deg, #ffc107, #e0a800); }
        .progress-poor { background: linear-gradient(135deg, #dc3545, #c82333); }
        
        .fee-breakdown {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .fee-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .fee-item:last-child {
            border-bottom: none;
        }
        
        .fee-amount {
            font-weight: 600;
            color: #495057;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>
                Student Portal
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($student['first_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="index.php"><i class="fas fa-home me-2"></i>Dashboard</a></li>
                        <li><a class="dropdown-item" href="attendance.php"><i class="fas fa-calendar-check me-2"></i>Attendance</a></li>
                        <li><a class="dropdown-item" href="grades.php"><i class="fas fa-chart-line me-2"></i>Grades</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-1">
                            <i class="fas fa-money-bill-wave me-2 text-primary"></i>
                            My Fee Status
                        </h2>
                        <p class="text-muted mb-0">
                            <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?> - 
                            <?php echo htmlspecialchars($student['class_name']); ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="progress-circle <?php 
                            if ($payment_percentage >= 90) echo 'progress-excellent';
                            elseif ($payment_percentage >= 70) echo 'progress-good';
                            elseif ($payment_percentage >= 50) echo 'progress-average';
                            else echo 'progress-poor';
                        ?>">
                            <?php echo $payment_percentage; ?>%
                        </div>
                        <small class="text-muted">Payment Progress</small>
                    </div>
                </div>
            </div>

            <!-- Fee Statistics -->
            <div class="stats-grid">
                <div class="stat-card total">
                    <div class="stat-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="stat-amount">₦<?php echo number_format($total_fees, 2); ?></div>
                    <div class="text-muted">Total Fees</div>
                </div>
                <div class="stat-card paid">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-amount">₦<?php echo number_format($total_paid, 2); ?></div>
                    <div class="text-muted">Amount Paid</div>
                </div>
                <div class="stat-card pending">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-amount">₦<?php echo number_format($total_pending, 2); ?></div>
                    <div class="text-muted">Pending Verification</div>
                </div>
                <div class="stat-card balance">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-amount">₦<?php echo number_format($balance, 2); ?></div>
                    <div class="text-muted">Outstanding Balance</div>
                </div>
            </div>

            <!-- Fee Breakdown -->
            <div class="fee-card">
                <h4 class="mb-3">
                    <i class="fas fa-list me-2"></i>
                    Fee Breakdown for <?php echo htmlspecialchars($student['level_name']); ?>
                </h4>
                
                <div class="fee-breakdown">
                    <?php foreach ($fee_types as $fee_type): ?>
                        <?php $amount = ($student['level_code'] === 'JS') ? $fee_type['amount_js'] : $fee_type['amount_ss']; ?>
                        <div class="fee-item">
                            <div>
                                <strong><?php echo htmlspecialchars($fee_type['fee_name']); ?></strong>
                                <br><small class="text-muted"><?php echo htmlspecialchars($fee_type['fee_description']); ?></small>
                            </div>
                            <div class="fee-amount">₦<?php echo number_format($amount, 2); ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Payment History -->
            <div class="fee-card">
                <h4 class="mb-3">
                    <i class="fas fa-history me-2"></i>
                    Payment History
                </h4>
                
                <?php if (!empty($payment_history)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date Submitted</th>
                                    <th>Amount</th>
                                    <th>Payment Method</th>
                                    <th>Status</th>
                                    <th>Remarks</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($payment_history as $payment): ?>
                                    <tr>
                                        <td><?php echo $payment['submitted_date']; ?></td>
                                        <td><strong>₦<?php echo number_format($payment['amount_paid'], 2); ?></strong></td>
                                        <td><?php echo htmlspecialchars($payment['payment_method']); ?></td>
                                        <td>
                                            <span class="payment-status status-<?php echo strtolower($payment['status']); ?>">
                                                <?php echo $payment['status']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($payment['admin_remarks'] ?? '-'); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Payment Records</h5>
                        <p class="text-muted">No payment records found for the current session.</p>
                    </div>
                <?php endif; ?>
            </div>

            <?php if ($balance > 0): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Outstanding Balance:</strong> You have an outstanding balance of ₦<?php echo number_format($balance, 2); ?>. 
                    Please contact your parent/guardian to make the payment through the parent portal.
                </div>
            <?php else: ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Fees Paid:</strong> All fees for the current session have been paid. Thank you!
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
