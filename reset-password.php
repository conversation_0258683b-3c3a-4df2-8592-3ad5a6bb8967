<?php
/**
 * Password Reset Page using Security Questions
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirectToDashboard();
}

$error = '';
$success = '';
$step = 1;
$user_data = null;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['step']) && $_POST['step'] == '1') {
        // Step 1: Find user by user code
        $user_code = sanitizeInput($_POST['user_code'] ?? '');

        if (empty($user_code)) {
            $error = 'Please enter your user code.';
        } else {
            try {
                $conn = getDBConnection();
                $stmt = $conn->prepare("
                    SELECT u.*, sq1.question as question1_text, sq2.question as question2_text
                    FROM users u
                    LEFT JOIN user_security_answers usa1 ON u.id = usa1.user_id
                    LEFT JOIN security_questions sq1 ON usa1.question_id = sq1.id
                    LEFT JOIN user_security_answers usa2 ON u.id = usa2.user_id AND usa2.id != usa1.id
                    LEFT JOIN security_questions sq2 ON usa2.question_id = sq2.id
                    WHERE u.user_code = ? AND u.is_active = 1
                    LIMIT 1
                ");
                $stmt->execute([$user_code]);
                $user_data = $stmt->fetch();

                if ($user_data) {
                    $step = 2;
                    $_SESSION['reset_user_id'] = $user_data['id'];
                } else {
                    $error = 'User code not found or account is inactive.';
                }
            } catch (Exception $e) {
                $error = 'An error occurred. Please try again.';
            }
        }
    } elseif (isset($_POST['step']) && $_POST['step'] == '2') {
        // Step 2: Verify security answers
        $answer1 = sanitizeInput($_POST['answer1'] ?? '');
        $answer2 = sanitizeInput($_POST['answer2'] ?? '');
        $user_id = $_SESSION['reset_user_id'] ?? 0;

        if (empty($answer1) || empty($answer2)) {
            $error = 'Please answer both security questions.';
        } else {
            try {
                $conn = getDBConnection();

                // Get user's security answers
                $stmt = $conn->prepare("
                    SELECT usa.answer_hash, sq.question
                    FROM user_security_answers usa
                    JOIN security_questions sq ON usa.question_id = sq.id
                    WHERE usa.user_id = ?
                    ORDER BY usa.id
                ");
                $stmt->execute([$user_id]);
                $security_answers = $stmt->fetchAll();

                if (count($security_answers) >= 2) {
                    $answer1_valid = password_verify(strtolower($answer1), $security_answers[0]['answer_hash']);
                    $answer2_valid = password_verify(strtolower($answer2), $security_answers[1]['answer_hash']);

                    if ($answer1_valid && $answer2_valid) {
                        $step = 3;
                    } else {
                        $error = 'Security answers are incorrect. Please try again.';
                    }
                } else {
                    $error = 'Security questions not found for this account.';
                }
            } catch (Exception $e) {
                $error = 'An error occurred. Please try again.';
            }
        }
    } elseif (isset($_POST['step']) && $_POST['step'] == '3') {
        // Step 3: Reset password
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $user_id = $_SESSION['reset_user_id'] ?? 0;

        if (empty($new_password) || empty($confirm_password)) {
            $error = 'Please fill in all password fields.';
        } elseif ($new_password !== $confirm_password) {
            $error = 'Passwords do not match.';
        } elseif (strlen($new_password) < PASSWORD_MIN_LENGTH) {
            $error = 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long.';
        } else {
            try {
                $conn = getDBConnection();
                $password_hash = password_hash($new_password, PASSWORD_DEFAULT);

                $stmt = $conn->prepare("UPDATE users SET password_hash = ? WHERE id = ?");
                $stmt->execute([$password_hash, $user_id]);

                // Clear session
                unset($_SESSION['reset_user_id']);

                $success = 'Password reset successful! You can now log in with your new password.';
                $step = 4;
            } catch (Exception $e) {
                $error = 'An error occurred while resetting password. Please try again.';
            }
        }
    }
}

// Get user data for step 2 if needed
if ($step == 2 && isset($_SESSION['reset_user_id'])) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("
            SELECT u.first_name, u.last_name, sq1.question as question1_text, sq2.question as question2_text
            FROM users u
            LEFT JOIN user_security_answers usa1 ON u.id = usa1.user_id
            LEFT JOIN security_questions sq1 ON usa1.question_id = sq1.id
            LEFT JOIN user_security_answers usa2 ON u.id = usa2.user_id AND usa2.id != usa1.id
            LEFT JOIN security_questions sq2 ON usa2.question_id = sq2.id
            WHERE u.id = ?
            LIMIT 1
        ");
        $stmt->execute([$_SESSION['reset_user_id']]);
        $user_data = $stmt->fetch();
    } catch (Exception $e) {
        $error = 'Session expired. Please start over.';
        $step = 1;
    }
}

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .reset-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .reset-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .reset-body {
            padding: 40px;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #667eea;
            color: white;
        }

        .step.completed {
            background: #28a745;
            color: white;
        }

        .step.inactive {
            background: #e9ecef;
            color: #6c757d;
        }

        .form-control {
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 12px 15px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .alert {
            border-radius: 10px;
            border: none;
            padding: 15px;
            margin-bottom: 20px;
        }

        /* Password Toggle Styles */
        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
            transition: color 0.3s ease;
            z-index: 10;
        }

        .password-toggle:hover {
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-header">
            <h3><i class="fas fa-key me-2"></i>Reset Password</h3>
            <p class="mb-0">Recover your account access</p>
        </div>

        <div class="reset-body">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : 'inactive'; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : 'inactive'; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : 'inactive'; ?>">3</div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                    <div class="mt-3">
                        <a href="login.php" class="btn btn-success">
                            <i class="fas fa-sign-in-alt me-2"></i>Go to Login
                        </a>
                    </div>
                </div>
            <?php elseif ($step == 1): ?>
                <!-- Step 1: Enter User Code -->
                <form method="POST" action="">
                    <input type="hidden" name="step" value="1">
                    <h5 class="mb-3">Step 1: Enter Your User Code</h5>
                    <p class="text-muted mb-4">Enter the user code that was provided when you registered.</p>

                    <div class="form-group">
                        <label for="user_code" class="form-label">User Code</label>
                        <input type="text" class="form-control" id="user_code" name="user_code"
                               placeholder="e.g., IGSS-2024-0001 or TEA1234567890123" required>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-arrow-right me-2"></i>Continue
                    </button>
                </form>
            <?php elseif ($step == 2): ?>
                <!-- Step 2: Security Questions -->
                <form method="POST" action="">
                    <input type="hidden" name="step" value="2">
                    <h5 class="mb-3">Step 2: Answer Security Questions</h5>
                    <p class="text-muted mb-4">
                        Hello <?php echo htmlspecialchars($user_data['first_name'] . ' ' . $user_data['last_name']); ?>!
                        Please answer your security questions.
                    </p>

                    <div class="form-group">
                        <label class="form-label"><?php echo htmlspecialchars($user_data['question1_text']); ?></label>
                        <input type="text" class="form-control" name="answer1" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label"><?php echo htmlspecialchars($user_data['question2_text']); ?></label>
                        <input type="text" class="form-control" name="answer2" required>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-arrow-right me-2"></i>Verify Answers
                    </button>
                </form>
            <?php elseif ($step == 3): ?>
                <!-- Step 3: New Password -->
                <form method="POST" action="">
                    <input type="hidden" name="step" value="3">
                    <h5 class="mb-3">Step 3: Create New Password</h5>
                    <p class="text-muted mb-4">Enter your new password below.</p>

                    <div class="form-group">
                        <label for="new_password" class="form-label">New Password</label>
                        <div class="password-container position-relative">
                            <input type="password" class="form-control" id="new_password" name="new_password"
                                   minlength="<?php echo PASSWORD_MIN_LENGTH; ?>" required>
                            <span class="password-toggle" onclick="togglePassword('new_password')">
                                <i class="fas fa-eye" id="new_password-toggle-icon"></i>
                            </span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <div class="password-container position-relative">
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <span class="password-toggle" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye" id="confirm_password-toggle-icon"></i>
                            </span>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-check me-2"></i>Reset Password
                    </button>
                </form>
            <?php endif; ?>

            <div class="text-center mt-4">
                <a href="login.php" class="text-decoration-none">
                    <i class="fas fa-arrow-left me-1"></i>Back to Login
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Password toggle function
        function togglePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const toggleIcon = document.getElementById(fieldId + '-toggle-icon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Password confirmation validation
        document.getElementById('confirm_password')?.addEventListener('input', function() {
            const password = document.getElementById('new_password').value;
            const confirmPassword = this.value;

            if (password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
