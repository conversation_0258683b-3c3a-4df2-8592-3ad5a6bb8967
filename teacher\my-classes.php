<?php
/**
 * My Classes Page for Teachers
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a teacher
if (!isLoggedIn() || $_SESSION['user_type'] !== 'teacher') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get teacher information
$stmt = $conn->prepare("
    SELECT t.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, u.user_code
    FROM teachers t
    JOIN users u ON t.user_id = u.id
    WHERE t.user_id = ?
");
$stmt->execute([$user_id]);
$teacher = $stmt->fetch();

if (!$teacher) {
    header('Location: ../login.php');
    exit();
}

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();

// Get teacher's assigned classes with subjects
$stmt = $conn->prepare("
    SELECT DISTINCT
        c.id as class_id,
        c.class_name,
        c.class_type,
        l.level_name,
        COUNT(DISTINCT ts.subject_id) as subject_count,
        COUNT(DISTINCT s.id) as student_count,
        GROUP_CONCAT(DISTINCT sub.subject_name ORDER BY sub.subject_name SEPARATOR ', ') as subjects
    FROM teacher_subjects ts
    JOIN classes c ON ts.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    JOIN subjects sub ON ts.subject_id = sub.id
    LEFT JOIN students s ON c.id = s.class_id AND s.current_session_id = ?
    WHERE ts.teacher_id = ? AND ts.is_active = 1
    GROUP BY c.id, c.class_name, c.class_type, l.level_name
    ORDER BY l.id, c.class_name
");
$stmt->execute([1, $user_id]); // Using session_id = 1 for now
$classes = $stmt->fetchAll();

// Check if teacher is a form teacher
$stmt = $conn->prepare("
    SELECT c.id as class_id, c.class_name, c.class_type, l.level_name
    FROM form_teachers ft
    JOIN classes c ON ft.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    WHERE ft.teacher_id = ? AND ft.is_active = 1
    LIMIT 1
");
$stmt->execute([$user_id]);
$form_class = $stmt->fetch();

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Classes - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #ff7b00 0%, #ff9500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }

        .sidebar {
            background: linear-gradient(135deg, #ff7b00 0%, #ff9500 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        /* Custom white scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.8);
        }

        /* For Firefox */
        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.6) rgba(255, 255, 255, 0.2);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 80px;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.25) 100%);
            border-radius: 60px 0 0 60px;
        }

        .sidebar-header {
            padding: 25px 20px;
            text-align: center;
            position: relative;
            background: rgba(255, 255, 255, 0.01);
            border-radius: 0 0 30px 30px;
            margin-bottom: 15px;
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 700;
            font-size: 1.4rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header small {
            opacity: 0.9;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            margin-bottom: 80px;
        }

        .sidebar-menu li {
            margin: 1px 15px;
        }

        .sidebar-menu a {
            color: white;
            text-decoration: none;
            padding: 8px 20px;
            display: block;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 25px;
            position: relative;
            font-weight: 500;
            font-size: 0.85rem;
            background: rgba(255, 255, 255, 0.05);
            border: none;
            margin-bottom: 1px;
        }

        .sidebar-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover::before, .sidebar-menu a.active::before {
            opacity: 1;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            transform: translateX(5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .sidebar-menu a.active {
            background: rgba(255, 255, 255, 0.2);
        }

        .back-to-home {
            position: absolute;
            bottom: 60px;
            left: 15px;
            right: 15px;
            z-index: 1001;
        }

        .back-to-home .btn {
            background: rgba(255, 255, 255, 0.3);
            border: 3px solid rgba(255, 255, 255, 0.6);
            color: white;
            font-weight: 800;
            padding: 12px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            width: 100%;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            font-size: 0.95rem;
        }

        .back-to-home .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
            position: relative;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px 30px;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 25px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .class-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .class-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .class-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .class-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .class-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #2c3e50;
            margin: 0;
        }

        .class-level {
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .class-stats {
            display: flex;
            gap: 20px;
            margin: 15px 0;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .subjects-list {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }

        .subjects-title {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 10px;
            font-size: 0.9rem;
        }

        .subjects-text {
            color: #495057;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .class-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .btn-action {
            flex: 1;
            padding: 10px;
            border-radius: 10px;
            font-size: 0.85rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-action:hover {
            transform: translateY(-2px);
        }

        .form-teacher-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-chalkboard-teacher me-2"></i>IGSS Teacher</h4>
            <small>Management Portal</small>
        </div>

        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
            <li><a href="my-classes.php" class="active"><i class="fas fa-users me-2"></i>My Classes</a></li>
            <li><a href="my-subjects.php"><i class="fas fa-book me-2"></i>My Subjects</a></li>
            <li><a href="attendance.php"><i class="fas fa-calendar-check me-2"></i>Attendance</a></li>
            <li><a href="grades.php"><i class="fas fa-graduation-cap me-2"></i>Grades</a></li>
            <li><a href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
            <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
        </ul>

        <!-- Back to Home Button -->
        <div class="back-to-home">
            <a href="../index.php" class="btn">
                <i class="fas fa-home me-2"></i>Back to Home
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><i class="fas fa-users me-2"></i>My Classes</h4>
                    <small class="text-muted">Classes assigned to you for teaching</small>
                </div>
                <div>
                    <span class="badge bg-primary me-2">
                        Session: <?php echo $current_academic['session']; ?>
                    </span>
                    <span class="badge bg-info">
                        Term: <?php echo TERMS[$current_academic['term']]; ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Form Teacher Section -->
        <?php if ($form_class): ?>
            <div class="content-card mb-4">
                <h5 class="mb-3"><i class="fas fa-star me-2 text-warning"></i>Form Teacher Assignment</h5>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    You are the form teacher for <strong><?php echo htmlspecialchars($form_class['class_name']); ?></strong>
                    (<?php echo htmlspecialchars($form_class['level_name']); ?>)
                </div>
            </div>
        <?php endif; ?>

        <!-- Classes Grid -->
        <div class="row">
            <?php if (count($classes) > 0): ?>
                <?php foreach ($classes as $class): ?>
                    <div class="col-lg-6 col-md-12">
                        <div class="class-card">
                            <?php if ($form_class && $form_class['class_id'] == $class['class_id']): ?>
                                <div class="form-teacher-badge">
                                    <i class="fas fa-star me-1"></i>Form Teacher
                                </div>
                            <?php endif; ?>

                            <div class="class-header">
                                <div>
                                    <h5 class="class-title"><?php echo htmlspecialchars($class['class_name']); ?></h5>
                                    <div class="class-level"><?php echo htmlspecialchars($class['level_name']); ?></div>
                                </div>
                            </div>

                            <div class="class-stats">
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $class['subject_count']; ?></div>
                                    <div class="stat-label">Subjects</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $class['student_count']; ?></div>
                                    <div class="stat-label">Students</div>
                                </div>
                            </div>

                            <div class="subjects-list">
                                <div class="subjects-title">Teaching Subjects:</div>
                                <div class="subjects-text"><?php echo htmlspecialchars($class['subjects']); ?></div>
                            </div>

                            <div class="class-actions">
                                <a href="attendance.php?class_id=<?php echo $class['class_id']; ?>" class="btn btn-primary btn-action">
                                    <i class="fas fa-calendar-check me-1"></i>Attendance
                                </a>
                                <a href="grades.php?class_id=<?php echo $class['class_id']; ?>" class="btn btn-success btn-action">
                                    <i class="fas fa-graduation-cap me-1"></i>Grades
                                </a>
                                <a href="class-details.php?id=<?php echo $class['class_id']; ?>" class="btn btn-info btn-action">
                                    <i class="fas fa-eye me-1"></i>View Details
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12">
                    <div class="content-card text-center">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Classes Assigned</h5>
                        <p class="text-muted">You don't have any classes assigned yet. Please contact the administrator.</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced hover effects for class cards
        document.addEventListener('DOMContentLoaded', function() {
            const classCards = document.querySelectorAll('.class-card');

            classCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Add click animations to action buttons
            const actionButtons = document.querySelectorAll('.btn-action');
            actionButtons.forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px) scale(1.05)';
                });

                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });

                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // Add special effects for form teacher badges
            const formTeacherBadges = document.querySelectorAll('.form-teacher-badge');
            formTeacherBadges.forEach(badge => {
                // Add a subtle pulsing animation
                badge.style.animation = 'pulse 2s infinite';
            });

            // Add CSS for pulse animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes pulse {
                    0% {
                        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
                    }
                    50% {
                        box-shadow: 0 4px 20px rgba(255, 193, 7, 0.5);
                    }
                    100% {
                        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
                    }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
