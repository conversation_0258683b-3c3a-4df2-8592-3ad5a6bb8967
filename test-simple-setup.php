<?php
/**
 * Simple Setup Test
 * Test the simplified admin setup process
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h2>🧪 Simple Admin Setup Test</h2>";

try {
    $conn = getDBConnection();
    echo "<p>✅ Database connection successful</p>";
    
    // Check if admin user exists
    $stmt = $conn->prepare("SELECT * FROM users WHERE user_code = 'ADMIN001' AND user_type = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<h3>✅ Admin User Found</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th style='padding: 8px;'>Property</th><th style='padding: 8px;'>Value</th></tr>";
        echo "<tr><td style='padding: 8px;'>User Code</td><td style='padding: 8px;'>{$admin['user_code']}</td></tr>";
        echo "<tr><td style='padding: 8px;'>Name</td><td style='padding: 8px;'>{$admin['first_name']} {$admin['last_name']}</td></tr>";
        echo "<tr><td style='padding: 8px;'>Email</td><td style='padding: 8px;'>{$admin['email']}</td></tr>";
        echo "<tr><td style='padding: 8px;'>Is Approved</td><td style='padding: 8px;'>" . ($admin['is_approved'] ? 'Yes' : 'No') . "</td></tr>";
        echo "<tr><td style='padding: 8px;'>Is Active</td><td style='padding: 8px;'>" . ($admin['is_active'] ? 'Yes' : 'No') . "</td></tr>";
        echo "</table>";
        
        // Test default password
        echo "<h3>🔐 Password Test</h3>";
        $defaultPassword = 'setup123';
        $isValid = password_verify($defaultPassword, $admin['password_hash']);
        if ($isValid) {
            echo "<p>✅ Default password 'setup123' works - ready for setup</p>";
        } else {
            echo "<p>❌ Default password doesn't work - may have been changed already</p>";
        }
        
        // Test authentication
        echo "<h3>🔑 Authentication Test</h3>";
        $authResult = authenticateUserWithRole('ADMIN001', $defaultPassword, 'admin');
        if ($authResult) {
            echo "<p>✅ Authentication successful with default password</p>";
        } else {
            echo "<p>❌ Authentication failed - password may have been changed</p>";
        }
        
    } else {
        echo "<h3>❌ Admin User Not Found</h3>";
        echo "<p>Run importdb.php first to create the admin user.</p>";
    }
    
    // Check session
    session_start();
    echo "<h3>📋 Session Status</h3>";
    if (isset($_SESSION['admin_setup_needed'])) {
        echo "<p>✅ Admin setup needed: Yes</p>";
    } else {
        echo "<p>❌ Admin setup needed: No</p>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Error</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<h3>🔗 Quick Actions</h3>";
echo "<p><a href='importdb.php' style='color: blue;'>Import Database</a></p>";
echo "<p><a href='admin-setup.php' style='color: blue;'>Admin Setup</a></p>";
echo "<p><a href='login.php' style='color: blue;'>Login Page</a></p>";

// Simulate the setup process
echo "<hr>";
echo "<h3>🎯 Simulate Setup Process</h3>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['simulate'])) {
    try {
        $testPassword = 'mypassword123';
        $testEmail = '<EMAIL>';
        $passwordHash = password_hash($testPassword, PASSWORD_DEFAULT);
        
        $stmt = $conn->prepare("
            UPDATE users 
            SET password_hash = ?, email = ?, first_name = 'Test', last_name = 'Admin', updated_at = NOW()
            WHERE user_code = 'ADMIN001' AND user_type = 'admin'
        ");
        
        $result = $stmt->execute([$passwordHash, $testEmail]);
        
        if ($result && $stmt->rowCount() > 0) {
            echo "<div style='background: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin: 10px 0;'>";
            echo "✅ Simulation successful! Admin password set to: <strong>mypassword123</strong><br>";
            echo "Email set to: <strong><EMAIL></strong><br>";
            echo "You can now login with: <strong>ADMIN001</strong> / <strong>mypassword123</strong>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0;'>";
            echo "❌ Simulation failed - no rows updated";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0;'>";
        echo "❌ Simulation error: " . htmlspecialchars($e->getMessage());
        echo "</div>";
    }
}

echo "<form method='POST'>";
echo "<input type='hidden' name='simulate' value='1'>";
echo "<button type='submit' style='background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;'>Simulate Password Setup</button>";
echo "</form>";

echo "<p><small>This will set admin password to 'mypassword123' for testing</small></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
