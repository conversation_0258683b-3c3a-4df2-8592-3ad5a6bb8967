<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Messaging System - IGSS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .setup-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 50px auto;
            max-width: 800px;
            padding: 40px;
        }
        .status-success { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <h1 class="text-center mb-4">
                <i class="fas fa-cogs"></i> IGSS Messaging System Setup
            </h1>
            
            <?php
            require_once 'config/config.php';
            
            $setup_log = "";
            $setup_success = false;
            
            if (isset($_POST['setup_messaging'])) {
                try {
                    $conn = getDBConnection();
                    
                    $setup_log .= "🚀 Setting up IGSS Messaging System...\n\n";
                    
                    // Create tables step by step with proper error handling
                    $setup_log .= "Creating messaging tables...\n";

                    // 1. Create messages table (without foreign keys first)
                    $sql = "CREATE TABLE IF NOT EXISTS messages (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        sender_id INT NOT NULL,
                        sender_type ENUM('parent', 'teacher', 'admin') NOT NULL,
                        subject VARCHAR(255) NOT NULL,
                        message_content TEXT NOT NULL,
                        message_type ENUM('general', 'academic', 'disciplinary', 'fee_inquiry', 'attendance', 'emergency') DEFAULT 'general',
                        priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
                        ward_id INT NULL,
                        parent_message_id INT NULL,
                        is_read BOOLEAN DEFAULT FALSE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_sender (sender_id, sender_type),
                        INDEX idx_ward (ward_id),
                        INDEX idx_created_at (created_at),
                        INDEX idx_message_type (message_type),
                        INDEX idx_priority (priority)
                    )";
                    $conn->exec($sql);
                    $setup_log .= "✅ Created messages table\n";

                    // 2. Create message_recipients table
                    $sql = "CREATE TABLE IF NOT EXISTS message_recipients (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        message_id INT NOT NULL,
                        recipient_id INT NOT NULL,
                        recipient_type ENUM('teacher', 'admin', 'parent') NOT NULL,
                        is_read BOOLEAN DEFAULT FALSE,
                        read_at TIMESTAMP NULL,
                        is_replied BOOLEAN DEFAULT FALSE,
                        replied_at TIMESTAMP NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE KEY unique_message_recipient (message_id, recipient_id),
                        INDEX idx_recipient (recipient_id, recipient_type),
                        INDEX idx_message (message_id),
                        INDEX idx_is_read (is_read),
                        INDEX idx_created_at (created_at)
                    )";
                    $conn->exec($sql);
                    $setup_log .= "✅ Created message_recipients table\n";

                    // 3. Create message_attachments table
                    $sql = "CREATE TABLE IF NOT EXISTS message_attachments (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        message_id INT NOT NULL,
                        file_name VARCHAR(255) NOT NULL,
                        file_path VARCHAR(500) NOT NULL,
                        file_size INT NOT NULL,
                        file_type VARCHAR(100) NOT NULL,
                        uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_message (message_id)
                    )";
                    $conn->exec($sql);
                    $setup_log .= "✅ Created message_attachments table\n";

                    // 4. Create message_notifications table
                    $sql = "CREATE TABLE IF NOT EXISTS message_notifications (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        message_id INT NOT NULL,
                        recipient_id INT NOT NULL,
                        notification_type ENUM('email', 'sms', 'in_app') NOT NULL,
                        is_sent BOOLEAN DEFAULT FALSE,
                        sent_at TIMESTAMP NULL,
                        error_message TEXT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_message (message_id),
                        INDEX idx_recipient (recipient_id),
                        INDEX idx_is_sent (is_sent)
                    )";
                    $conn->exec($sql);
                    $setup_log .= "✅ Created message_notifications table\n";

                    // 5. Create message_templates table (without foreign key)
                    $sql = "CREATE TABLE IF NOT EXISTS message_templates (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        template_name VARCHAR(100) NOT NULL,
                        template_type ENUM('emergency', 'academic', 'fee_inquiry', 'attendance', 'general') NOT NULL,
                        subject_template VARCHAR(255) NOT NULL,
                        message_template TEXT NOT NULL,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_by INT NOT NULL DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_template_type (template_type),
                        INDEX idx_is_active (is_active)
                    )";
                    $conn->exec($sql);
                    $setup_log .= "✅ Created message_templates table\n";

                    // 6. Insert default message templates
                    $sql = "INSERT IGNORE INTO message_templates (template_name, template_type, subject_template, message_template, created_by) VALUES
                    ('Emergency Contact', 'emergency', 'URGENT: Emergency Contact Required', 'Dear Teacher/Admin,\\n\\nThis is an urgent matter requiring immediate attention regarding my ward [WARD_NAME].\\n\\nPlease contact me as soon as possible.\\n\\nThank you.', 1),
                    ('Academic Concern', 'academic', 'Academic Concern - [WARD_NAME]', 'Dear [RECIPIENT],\\n\\nI would like to discuss my ward\\'s academic performance in [SUBJECT]. I have some concerns that I believe need attention.\\n\\nPlease let me know when we can discuss this matter.\\n\\nThank you.', 1),
                    ('Fee Payment Issue', 'fee_inquiry', 'Fee Payment Inquiry - [WARD_NAME]', 'Dear Admin,\\n\\nI have a question regarding the fee payment for my ward [WARD_NAME]. \\n\\nPlease provide clarification on the payment status and any outstanding amounts.\\n\\nThank you.', 1),
                    ('Attendance Inquiry', 'attendance', 'Attendance Inquiry - [WARD_NAME]', 'Dear Form Teacher,\\n\\nI would like to inquire about my ward\\'s attendance record. Please provide an update on their attendance status.\\n\\nThank you.', 1),
                    ('Parent-Teacher Meeting Request', 'general', 'Request for Parent-Teacher Meeting', 'Dear [RECIPIENT],\\n\\nI would like to request a meeting to discuss my ward\\'s progress and any areas that need attention.\\n\\nPlease let me know your available times.\\n\\nThank you.', 1)";
                    $conn->exec($sql);
                    $setup_log .= "✅ Inserted default message templates\n";
                    
                    $setup_log .= "\n📋 Checking created tables...\n";
                    
                    // Check if tables were created
                    $tables_to_check = [
                        'messages',
                        'message_recipients', 
                        'message_attachments',
                        'message_notifications',
                        'message_templates'
                    ];
                    
                    $tables_created = 0;
                    foreach ($tables_to_check as $table) {
                        $stmt = $conn->query("SHOW TABLES LIKE '$table'");
                        if ($stmt->rowCount() > 0) {
                            $setup_log .= "✅ Table '$table' created successfully\n";
                            $tables_created++;
                        } else {
                            $setup_log .= "❌ Table '$table' not found\n";
                        }
                    }
                    
                    // Check if message templates were inserted
                    $stmt = $conn->query("SELECT COUNT(*) FROM message_templates");
                    $template_count = $stmt->fetchColumn();
                    $setup_log .= "\n📝 Message templates: $template_count templates available\n";
                    
                    if ($tables_created == count($tables_to_check)) {
                        $setup_success = true;
                        $setup_log .= "\n🎉 Messaging system setup completed successfully!\n";
                        $setup_log .= "\n📌 Next steps:\n";
                        $setup_log .= "   1. Test parent messaging at: parent/messages.php\n";
                        $setup_log .= "   2. Check teacher inbox at: teacher/messages.php\n";
                        $setup_log .= "   3. Review admin messages at: admin/messages.php\n";
                        $setup_log .= "\n💡 Features enabled:\n";
                        $setup_log .= "   ✅ Message routing to form teachers\n";
                        $setup_log .= "   ✅ Admin message handling\n";
                        $setup_log .= "   ✅ Message threading and replies\n";
                        $setup_log .= "   ✅ Priority levels and categories\n";
                        $setup_log .= "   ✅ Notification system\n";
                        $setup_log .= "   ✅ Quick message templates\n";
                    } else {
                        $setup_log .= "\n❌ Some tables were not created properly. Please check the errors above.\n";
                    }
                    
                } catch (Exception $e) {
                    $setup_log .= "❌ Error setting up messaging system: " . $e->getMessage() . "\n";
                }
            }
            ?>
            
            <?php if (!isset($_POST['setup_messaging'])): ?>
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> About This Setup</h5>
                    <p>This setup will create the necessary database tables for the IGSS messaging system:</p>
                    <ul>
                        <li><strong>messages</strong> - Main messages table</li>
                        <li><strong>message_recipients</strong> - Message routing table</li>
                        <li><strong>message_attachments</strong> - File attachments support</li>
                        <li><strong>message_notifications</strong> - Notification system</li>
                        <li><strong>message_templates</strong> - Quick message templates</li>
                    </ul>
                </div>
                
                <form method="POST" class="text-center">
                    <button type="submit" name="setup_messaging" class="btn btn-primary btn-lg">
                        <i class="fas fa-play"></i> Setup Messaging System
                    </button>
                </form>
            <?php else: ?>
                <div class="alert alert-<?php echo $setup_success ? 'success' : 'danger'; ?>">
                    <h5>
                        <i class="fas fa-<?php echo $setup_success ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                        Setup <?php echo $setup_success ? 'Completed' : 'Failed'; ?>
                    </h5>
                </div>
                
                <div class="log-output"><?php echo htmlspecialchars($setup_log); ?></div>
                
                <div class="text-center mt-4">
                    <?php if ($setup_success): ?>
                        <a href="parent/messages.php" class="btn btn-success me-2">
                            <i class="fas fa-envelope"></i> Test Parent Messages
                        </a>
                    <?php endif; ?>
                    <a href="setup-messaging-web.php" class="btn btn-secondary">
                        <i class="fas fa-redo"></i> Run Setup Again
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
