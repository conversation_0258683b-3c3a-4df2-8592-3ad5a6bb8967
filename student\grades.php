<?php
/**
 * Student Grades/Results View
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a student
if (!isLoggedIn() || $_SESSION['user_type'] !== 'student') {
    header('Location: ../login.php');
    exit();
}

// Check if student account is approved
if (!$_SESSION['is_approved']) {
    header('Location: pending-approval.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get student information
$stmt = $conn->prepare("
    SELECT s.*, u.first_name, u.last_name, u.middle_name, u.user_code,
           c.class_name, l.level_name
    FROM students s
    JOIN users u ON s.user_id = u.id
    JOIN classes c ON s.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    WHERE s.user_id = ?
");
$stmt->execute([$user_id]);
$student = $stmt->fetch();

if (!$student) {
    header('Location: ../login.php');
    exit();
}

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();

// Get grades for current term
$stmt = $conn->prepare("
    SELECT sg.*, sub.subject_name, sub.subject_code,
           CASE 
               WHEN sg.total_score >= 70 THEN 'A'
               WHEN sg.total_score >= 60 THEN 'B'
               WHEN sg.total_score >= 50 THEN 'C'
               WHEN sg.total_score >= 45 THEN 'D'
               WHEN sg.total_score >= 40 THEN 'E'
               ELSE 'F'
           END as letter_grade,
           CASE 
               WHEN sg.total_score >= 70 THEN 'Excellent'
               WHEN sg.total_score >= 60 THEN 'Very Good'
               WHEN sg.total_score >= 50 THEN 'Good'
               WHEN sg.total_score >= 45 THEN 'Fair'
               WHEN sg.total_score >= 40 THEN 'Pass'
               ELSE 'Fail'
           END as grade_remark
    FROM student_grades sg
    JOIN subjects sub ON sg.subject_id = sub.id
    WHERE sg.student_id = ? AND sg.term_id = ?
    ORDER BY sub.subject_name
");
$stmt->execute([$student['id'], $current_academic['current_term_id'] ?? 1]);
$current_grades = $stmt->fetchAll();

// Calculate statistics
$total_subjects = count($current_grades);
$total_score = 0;
$grade_distribution = ['A' => 0, 'B' => 0, 'C' => 0, 'D' => 0, 'E' => 0, 'F' => 0];

foreach ($current_grades as $grade) {
    $total_score += $grade['total_score'];
    $grade_distribution[$grade['letter_grade']]++;
}

$average_score = $total_subjects > 0 ? round($total_score / $total_subjects, 1) : 0;

// Determine overall performance
$overall_grade = 'F';
$overall_remark = 'Needs Improvement';
if ($average_score >= 70) {
    $overall_grade = 'A';
    $overall_remark = 'Excellent Performance';
} elseif ($average_score >= 60) {
    $overall_grade = 'B';
    $overall_remark = 'Very Good Performance';
} elseif ($average_score >= 50) {
    $overall_grade = 'C';
    $overall_remark = 'Good Performance';
} elseif ($average_score >= 45) {
    $overall_grade = 'D';
    $overall_remark = 'Fair Performance';
} elseif ($average_score >= 40) {
    $overall_grade = 'E';
    $overall_remark = 'Pass';
}

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Grades - <?php echo htmlspecialchars($school_info['name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .navbar-custom .navbar-brand {
            color: #ff6b35 !important;
            font-weight: 700;
        }
        
        .navbar-custom .nav-link {
            color: #333 !important;
            font-weight: 500;
        }
        
        .main-container {
            padding: 2rem 0;
        }
        
        .page-header {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 107, 53, 0.1);
        }
        
        .grade-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 107, 53, 0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 107, 53, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }
        
        .grade-table {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 107, 53, 0.1);
        }
        
        .table th {
            border-top: none;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            color: #495057;
        }
        
        .grade-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 700;
            font-size: 0.9rem;
        }
        
        .grade-A { background: #d4edda; color: #155724; }
        .grade-B { background: #d1ecf1; color: #0c5460; }
        .grade-C { background: #fff3cd; color: #856404; }
        .grade-D { background: #ffeaa7; color: #6c5ce7; }
        .grade-E { background: #fab1a0; color: #e17055; }
        .grade-F { background: #f8d7da; color: #721c24; }
        
        .overall-grade {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            font-weight: 700;
            color: white;
            margin: 0 auto 1rem;
        }
        
        .grade-A-circle { background: linear-gradient(135deg, #28a745, #20c997); }
        .grade-B-circle { background: linear-gradient(135deg, #17a2b8, #138496); }
        .grade-C-circle { background: linear-gradient(135deg, #ffc107, #e0a800); }
        .grade-D-circle { background: linear-gradient(135deg, #6f42c1, #5a32a3); }
        .grade-E-circle { background: linear-gradient(135deg, #fd7e14, #e55a4e); }
        .grade-F-circle { background: linear-gradient(135deg, #dc3545, #c82333); }
        
        .score-bar {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 0.5rem;
        }
        
        .score-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .score-excellent { background: linear-gradient(90deg, #28a745, #20c997); }
        .score-good { background: linear-gradient(90deg, #17a2b8, #138496); }
        .score-average { background: linear-gradient(90deg, #ffc107, #e0a800); }
        .score-poor { background: linear-gradient(90deg, #dc3545, #c82333); }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>
                Student Portal
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($student['first_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="index.php"><i class="fas fa-home me-2"></i>Dashboard</a></li>
                        <li><a class="dropdown-item" href="attendance.php"><i class="fas fa-calendar-check me-2"></i>Attendance</a></li>
                        <li><a class="dropdown-item" href="fees.php"><i class="fas fa-money-bill me-2"></i>Fees</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-1">
                            <i class="fas fa-chart-line me-2 text-primary"></i>
                            My Academic Results
                        </h2>
                        <p class="text-muted mb-0">
                            <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?> - 
                            <?php echo htmlspecialchars($student['class_name']); ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="overall-grade grade-<?php echo $overall_grade; ?>-circle">
                            <?php echo $overall_grade; ?>
                        </div>
                        <div>
                            <strong><?php echo $average_score; ?>%</strong><br>
                            <small class="text-muted"><?php echo $overall_remark; ?></small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $total_subjects; ?></div>
                    <div class="text-muted">Total Subjects</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $average_score; ?>%</div>
                    <div class="text-muted">Average Score</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $grade_distribution['A'] + $grade_distribution['B']; ?></div>
                    <div class="text-muted">A & B Grades</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $grade_distribution['F']; ?></div>
                    <div class="text-muted">Failed Subjects</div>
                </div>
            </div>

            <!-- Detailed Grades Table -->
            <div class="grade-table">
                <h4 class="mb-3">
                    <i class="fas fa-list me-2"></i>
                    Subject-wise Performance
                </h4>
                
                <?php if (!empty($current_grades)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Subject</th>
                                    <th>First Test</th>
                                    <th>Second Test</th>
                                    <th>Exam</th>
                                    <th>Total</th>
                                    <th>Grade</th>
                                    <th>Remark</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($current_grades as $grade): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($grade['subject_name']); ?></strong>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($grade['subject_code']); ?></small>
                                        </td>
                                        <td><?php echo $grade['first_test']; ?></td>
                                        <td><?php echo $grade['second_test']; ?></td>
                                        <td><?php echo $grade['exam_score']; ?></td>
                                        <td>
                                            <strong><?php echo $grade['total_score']; ?></strong>
                                            <div class="score-bar">
                                                <div class="score-fill <?php 
                                                    if ($grade['total_score'] >= 70) echo 'score-excellent';
                                                    elseif ($grade['total_score'] >= 60) echo 'score-good';
                                                    elseif ($grade['total_score'] >= 50) echo 'score-average';
                                                    else echo 'score-poor';
                                                ?>" style="width: <?php echo min(100, $grade['total_score']); ?>%"></div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="grade-badge grade-<?php echo $grade['letter_grade']; ?>">
                                                <?php echo $grade['letter_grade']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo $grade['grade_remark']; ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Grades Available</h5>
                        <p class="text-muted">No grades have been recorded for the current term yet.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Grade Distribution -->
            <?php if (!empty($current_grades)): ?>
                <div class="grade-card">
                    <h4 class="mb-3">
                        <i class="fas fa-chart-pie me-2"></i>
                        Grade Distribution
                    </h4>
                    <div class="row">
                        <?php foreach ($grade_distribution as $grade => $count): ?>
                            <div class="col-md-2 text-center mb-3">
                                <div class="grade-badge grade-<?php echo $grade; ?> d-block mb-2">
                                    Grade <?php echo $grade; ?>
                                </div>
                                <h5><?php echo $count; ?> Subject<?php echo $count != 1 ? 's' : ''; ?></h5>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
