<?php
/**
 * Student Details for Parents
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a parent
if (!isLoggedIn() || $_SESSION['user_type'] !== 'parent') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];
$student_id = $_GET['id'] ?? 0;

// Get parent information
$stmt = $conn->prepare("
    SELECT p.*, u.first_name as parent_first_name, u.last_name as parent_last_name
    FROM parents p
    JOIN users u ON p.user_id = u.id
    WHERE p.user_id = ?
");
$stmt->execute([$user_id]);
$parent = $stmt->fetch();

if (!$parent) {
    header('Location: ../login.php');
    exit();
}

// Verify parent has access to this student
$stmt = $conn->prepare("
    SELECT COUNT(*) FROM parent_student_relationships 
    WHERE parent_id = ? AND student_id = ?
");
$stmt->execute([$parent['id'], $student_id]);
$has_access = $stmt->fetchColumn() > 0;

if (!$has_access) {
    header('Location: index.php');
    exit();
}

// Get student information
$stmt = $conn->prepare("
    SELECT s.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, 
           u.date_of_birth, u.gender, u.user_code, u.profile_image,
           c.class_name, l.level_name, psr.relationship_type
    FROM students s
    JOIN users u ON s.user_id = u.id
    JOIN classes c ON s.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    JOIN parent_student_relationships psr ON s.id = psr.student_id
    WHERE s.id = ? AND psr.parent_id = ?
");
$stmt->execute([$student_id, $parent['id']]);
$student = $stmt->fetch();

if (!$student) {
    header('Location: index.php');
    exit();
}

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();

// Get attendance summary
$stmt = $conn->prepare("
    SELECT 
        COUNT(*) as total_days,
        SUM(CASE WHEN status = 'Present' THEN 1 ELSE 0 END) as present_days,
        SUM(CASE WHEN status = 'Absent' THEN 1 ELSE 0 END) as absent_days,
        SUM(CASE WHEN status = 'Late' THEN 1 ELSE 0 END) as late_days,
        SUM(CASE WHEN status = 'Excused' THEN 1 ELSE 0 END) as excused_days
    FROM attendance 
    WHERE student_id = ? AND term_id = ?
");
$stmt->execute([$student['id'], $current_academic['current_term_id'] ?? 1]);
$attendance_summary = $stmt->fetch();

// Calculate attendance percentage
$total_days = $attendance_summary['total_days'] ?? 0;
$present_days = $attendance_summary['present_days'] ?? 0;
$attendance_percentage = $total_days > 0 ? round(($present_days / $total_days) * 100, 1) : 0;

// Get recent grades
$stmt = $conn->prepare("
    SELECT sg.*, sub.subject_name, sub.subject_code,
           DATE_FORMAT(sg.created_at, '%M %d, %Y') as grade_date
    FROM student_grades sg
    JOIN subjects sub ON sg.subject_id = sub.id
    WHERE sg.student_id = ? AND sg.term_id = ?
    ORDER BY sg.created_at DESC
    LIMIT 10
");
$stmt->execute([$student['id'], $current_academic['current_term_id'] ?? 1]);
$recent_grades = $stmt->fetchAll();

// Calculate average grade
$total_score = 0;
$grade_count = 0;
foreach ($recent_grades as $grade) {
    $total_score += $grade['score'];
    $grade_count++;
}
$average_score = $grade_count > 0 ? round($total_score / $grade_count, 1) : 0;

// Get fee payment status
$stmt = $conn->prepare("
    SELECT fp.*, ft.fee_type_name, ft.amount as fee_amount
    FROM fee_payments fp
    JOIN fee_types ft ON fp.fee_type_id = ft.id
    WHERE fp.student_id = ? AND fp.session_id = ?
    ORDER BY fp.created_at DESC
    LIMIT 5
");
$stmt->execute([$student['id'], $current_academic['current_session_id'] ?? 1]);
$recent_payments = $stmt->fetchAll();

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?> - Details</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 50%, #ff4500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container-custom {
            padding: 20px;
        }
        
        .student-header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .info-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .stat-card {
            text-align: center;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 15px;
            color: white;
        }
        
        .attendance-stat { background: linear-gradient(135deg, #28a745, #20c997); }
        .grades-stat { background: linear-gradient(135deg, #17a2b8, #138496); }
        .payments-stat { background: linear-gradient(135deg, #ffc107, #e0a800); }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .profile-img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #fff;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .student-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #ff8c00;
            font-size: 1.2rem;
            background: rgba(255, 140, 0, 0.1);
            padding: 8px 12px;
            border-radius: 8px;
            display: inline-block;
        }
        
        .grade-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-weight: bold;
            color: white;
        }
        
        .grade-A { background: #28a745; }
        .grade-B { background: #17a2b8; }
        .grade-C { background: #ffc107; color: #333; }
        .grade-D { background: #fd7e14; }
        .grade-F { background: #dc3545; }
        
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .btn-orange {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-orange:hover {
            background: linear-gradient(135deg, #ff9500 0%, #ff7635 100%);
            color: white;
            transform: translateY(-1px);
        }
        
        .payment-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: bold;
        }
        
        .status-confirmed { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-rejected { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($parent['parent_first_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="index.php"><i class="fas fa-home me-2"></i>Dashboard</a></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-custom">
        <div class="container">
            <!-- Student Header -->
            <div class="student-header">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        <?php if ($student['profile_image']): ?>
                            <img src="../<?php echo htmlspecialchars($student['profile_image']); ?>" 
                                 alt="Profile" class="profile-img">
                        <?php else: ?>
                            <div class="profile-img d-flex align-items-center justify-content-center bg-primary text-white">
                                <i class="fas fa-user fa-2x"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                        <h2 class="mb-2">
                            <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                        </h2>
                        <p class="text-muted mb-1">
                            <i class="fas fa-graduation-cap me-2"></i>
                            <?php echo htmlspecialchars($student['class_name'] . ' - ' . $student['level_name']); ?>
                        </p>
                        <p class="text-muted mb-1">
                            <i class="fas fa-id-card me-2"></i>
                            Admission: <?php echo htmlspecialchars($student['admission_number']); ?>
                        </p>
                        <p class="text-muted mb-0">
                            <i class="fas fa-heart me-2"></i>
                            Your <?php echo htmlspecialchars($student['relationship_type']); ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-center">
                        <h6 class="mb-2">Student Code</h6>
                        <div class="student-code"><?php echo htmlspecialchars($student['user_code']); ?></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Quick Stats -->
                <div class="col-md-4">
                    <div class="stat-card attendance-stat">
                        <div class="stat-number"><?php echo $attendance_percentage; ?>%</div>
                        <h6>Attendance Rate</h6>
                        <small><?php echo $present_days; ?> of <?php echo $total_days; ?> days present</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card grades-stat">
                        <div class="stat-number"><?php echo $average_score; ?>%</div>
                        <h6>Average Score</h6>
                        <small>Based on <?php echo $grade_count; ?> recent assessments</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card payments-stat">
                        <div class="stat-number"><?php echo count($recent_payments); ?></div>
                        <h6>Recent Payments</h6>
                        <small>This academic session</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Personal Information -->
                <div class="col-md-6">
                    <div class="info-card">
                        <h5 class="mb-4">
                            <i class="fas fa-user me-2"></i>
                            Personal Information
                        </h5>
                        
                        <div class="row mb-3">
                            <div class="col-sm-4"><strong>Full Name:</strong></div>
                            <div class="col-sm-8">
                                <?php echo htmlspecialchars($student['first_name'] . ' ' . ($student['middle_name'] ? $student['middle_name'] . ' ' : '') . $student['last_name']); ?>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-4"><strong>Date of Birth:</strong></div>
                            <div class="col-sm-8">
                                <?php echo $student['date_of_birth'] ? date('F j, Y', strtotime($student['date_of_birth'])) : 'Not provided'; ?>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-4"><strong>Gender:</strong></div>
                            <div class="col-sm-8"><?php echo htmlspecialchars($student['gender']); ?></div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-4"><strong>Email:</strong></div>
                            <div class="col-sm-8"><?php echo htmlspecialchars($student['email'] ?? 'Not provided'); ?></div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-4"><strong>Phone:</strong></div>
                            <div class="col-sm-8"><?php echo htmlspecialchars($student['phone'] ?? 'Not provided'); ?></div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-4"><strong>Boarding Status:</strong></div>
                            <div class="col-sm-8">
                                <span class="badge <?php echo $student['is_boarding'] ? 'bg-info' : 'bg-secondary'; ?>">
                                    <?php echo $student['is_boarding'] ? 'Boarding Student' : 'Day Student'; ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Academic Information -->
                <div class="col-md-6">
                    <div class="info-card">
                        <h5 class="mb-4">
                            <i class="fas fa-graduation-cap me-2"></i>
                            Academic Information
                        </h5>
                        
                        <div class="row mb-3">
                            <div class="col-sm-4"><strong>Class:</strong></div>
                            <div class="col-sm-8"><?php echo htmlspecialchars($student['class_name']); ?></div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-4"><strong>Level:</strong></div>
                            <div class="col-sm-8"><?php echo htmlspecialchars($student['level_name']); ?></div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-4"><strong>Admission Number:</strong></div>
                            <div class="col-sm-8"><?php echo htmlspecialchars($student['admission_number']); ?></div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-4"><strong>Admission Date:</strong></div>
                            <div class="col-sm-8">
                                <?php echo date('F j, Y', strtotime($student['admission_date'])); ?>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-4"><strong>Student Code:</strong></div>
                            <div class="col-sm-8">
                                <span class="student-code"><?php echo htmlspecialchars($student['user_code']); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="info-card">
                <h5 class="mb-4">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
                
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="student-grades.php?id=<?php echo $student['id']; ?>" class="btn btn-outline-success w-100">
                            <i class="fas fa-chart-line me-2"></i>View Grades
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="student-attendance.php?id=<?php echo $student['id']; ?>" class="btn btn-outline-info w-100">
                            <i class="fas fa-calendar-check me-2"></i>View Attendance
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="pay-fees.php?student_id=<?php echo $student['id']; ?>" class="btn btn-orange w-100">
                            <i class="fas fa-credit-card me-2"></i>Pay Fees
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="student-reports.php?id=<?php echo $student['id']; ?>" class="btn btn-outline-primary w-100">
                            <i class="fas fa-file-alt me-2"></i>Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
