<?php
/**
 * Comprehensive Testing Script for IGSS Management System
 * Tests all functionality across 4 user roles (admin, teacher, parent, student)
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// Start session for testing
session_start();

$testResults = [];
$overallStatus = true;

// Test Database Connection
function testDatabaseConnection() {
    try {
        $conn = getDBConnection();
        if ($conn) {
            return ['status' => 'PASS', 'message' => 'Database connection successful'];
        } else {
            return ['status' => 'FAIL', 'message' => 'Database connection failed'];
        }
    } catch (Exception $e) {
        return ['status' => 'FAIL', 'message' => 'Database error: ' . $e->getMessage()];
    }
}

// Test Authentication System
function testAuthenticationSystem() {
    try {
        $conn = getDBConnection();

        // Test admin login
        $stmt = $conn->prepare("SELECT * FROM users WHERE user_code = 'ADMIN001' AND user_type = 'admin'");
        $stmt->execute();
        $admin = $stmt->fetch();

        if (!$admin) {
            return ['status' => 'FAIL', 'message' => 'Default admin user not found'];
        }

        // Test password verification
        if (password_verify('admin123', $admin['password_hash'])) {
            return ['status' => 'PASS', 'message' => 'Authentication system working correctly'];
        } else {
            return ['status' => 'FAIL', 'message' => 'Password verification failed'];
        }
    } catch (Exception $e) {
        return ['status' => 'FAIL', 'message' => 'Authentication test error: ' . $e->getMessage()];
    }
}

// Test Teacher Module
function testTeacherModule() {
    try {
        $conn = getDBConnection();

        // Check if teacher grades table has new scoring system columns
        $stmt = $conn->query("DESCRIBE student_grades");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        $required_columns = ['assessment_score', 'exam_score_new', 'teacher_remarks'];
        $missing_columns = array_diff($required_columns, $columns);

        if (!empty($missing_columns)) {
            return ['status' => 'FAIL', 'message' => 'Missing grading system columns: ' . implode(', ', $missing_columns)];
        }

        // Check if grades-new.php exists
        if (!file_exists('teacher/grades-new.php')) {
            return ['status' => 'FAIL', 'message' => 'Enhanced grading system file not found'];
        }

        return ['status' => 'PASS', 'message' => 'Teacher module with enhanced scoring system ready'];
    } catch (Exception $e) {
        return ['status' => 'FAIL', 'message' => 'Teacher module test error: ' . $e->getMessage()];
    }
}

// Test Parent Module
function testParentModule() {
    try {
        $conn = getDBConnection();

        // Check parent-student relationships table
        $stmt = $conn->query("SELECT COUNT(*) FROM parent_student_relationships");
        $relationship_count = $stmt->fetchColumn();

        // Check fee payments table
        $stmt = $conn->query("DESCRIBE fee_payments");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        $required_columns = ['receipt_image', 'status', 'depositor_name'];
        $missing_columns = array_diff($required_columns, $columns);

        if (!empty($missing_columns)) {
            return ['status' => 'FAIL', 'message' => 'Missing payment system columns: ' . implode(', ', $missing_columns)];
        }

        // Check if payment files exist
        if (!file_exists('parent/pay-fees.php')) {
            return ['status' => 'FAIL', 'message' => 'Payment system file not found'];
        }

        return ['status' => 'PASS', 'message' => 'Parent module with payment system ready'];
    } catch (Exception $e) {
        return ['status' => 'FAIL', 'message' => 'Parent module test error: ' . $e->getMessage()];
    }
}

// Test Student Module
function testStudentModule() {
    try {
        // Check if student files exist
        $required_files = ['student/index.php', 'student/grades.php', 'student/attendance.php', 'student/fees.php'];
        $missing_files = [];

        foreach ($required_files as $file) {
            if (!file_exists($file)) {
                $missing_files[] = $file;
            }
        }

        if (!empty($missing_files)) {
            return ['status' => 'FAIL', 'message' => 'Missing student files: ' . implode(', ', $missing_files)];
        }

        return ['status' => 'PASS', 'message' => 'Student module files present'];
    } catch (Exception $e) {
        return ['status' => 'FAIL', 'message' => 'Student module test error: ' . $e->getMessage()];
    }
}

// Test Admin Module
function testAdminModule() {
    try {
        // Check if admin files exist
        $required_files = ['admin/dashboard.php', 'admin/fee-confirmations.php', 'admin/manage-users.php'];
        $missing_files = [];

        foreach ($required_files as $file) {
            if (!file_exists($file)) {
                $missing_files[] = $file;
            }
        }

        if (!empty($missing_files)) {
            return ['status' => 'FAIL', 'message' => 'Missing admin files: ' . implode(', ', $missing_files)];
        }

        return ['status' => 'PASS', 'message' => 'Admin module files present'];
    } catch (Exception $e) {
        return ['status' => 'FAIL', 'message' => 'Admin module test error: ' . $e->getMessage()];
    }
}

// Test File Structure
function testFileStructure() {
    $critical_files = [
        'login.php', 'logout.php', 'register.php', 'index.php',
        'config/config.php', 'config/database.php',
        'includes/functions.php',
        'importdb.php', 'direct-reset.php'
    ];

    $missing_files = [];
    foreach ($critical_files as $file) {
        if (!file_exists($file)) {
            $missing_files[] = $file;
        }
    }

    if (empty($missing_files)) {
        return ['status' => 'PASS', 'message' => 'All critical files present'];
    } else {
        return ['status' => 'FAIL', 'message' => 'Missing files: ' . implode(', ', $missing_files)];
    }
}

// Test Notification System
function testNotificationSystem() {
    try {
        $conn = getDBConnection();

        // Check if messaging tables exist
        $stmt = $conn->query("SHOW TABLES LIKE 'messages'");
        $messages_table = $stmt->fetch();

        if (!$messages_table) {
            return ['status' => 'FAIL', 'message' => 'Messages table not found'];
        }

        return ['status' => 'PASS', 'message' => 'Notification system tables present'];
    } catch (Exception $e) {
        return ['status' => 'FAIL', 'message' => 'Notification test error: ' . $e->getMessage()];
    }
}

// Run all tests
$testResults['Database Connection'] = testDatabaseConnection();
$testResults['Authentication System'] = testAuthenticationSystem();
$testResults['Teacher Module'] = testTeacherModule();
$testResults['Parent Module'] = testParentModule();
$testResults['Student Module'] = testStudentModule();
$testResults['Admin Module'] = testAdminModule();
$testResults['File Structure'] = testFileStructure();
$testResults['Notification System'] = testNotificationSystem();

// Check overall status
foreach ($testResults as $test => $result) {
    if ($result['status'] === 'FAIL') {
        $overallStatus = false;
    }
}

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive System Test - IGSS Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }
        .test-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .test-pass {
            background-color: #d4edda;
            border-left-color: #28a745;
        }
        .test-fail {
            background-color: #f8d7da;
            border-left-color: #dc3545;
        }
        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 12px;
        }
        .badge-pass {
            background-color: #28a745;
            color: white;
        }
        .badge-fail {
            background-color: #dc3545;
            color: white;
        }
        .overall-status {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            font-size: 18px;
            font-weight: 600;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .action-buttons {
            text-align: center;
            margin-top: 30px;
        }
        .btn-test {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 500;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        .module-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .module-title {
            color: #333;
            font-weight: 600;
            margin-bottom: 15px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <div class="test-header">
                <h2><i class="fas fa-vial text-primary"></i> IGSS System Comprehensive Test</h2>
                <p class="text-muted">Complete functionality testing across all 4 user roles</p>
            </div>

            <div class="overall-status <?php echo $overallStatus ? 'status-success' : 'status-error'; ?>">
                <i class="fas <?php echo $overallStatus ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                Overall Status: <?php echo $overallStatus ? 'ALL SYSTEMS OPERATIONAL' : 'ISSUES DETECTED - REQUIRES ATTENTION'; ?>
            </div>

            <div class="test-results">
                <?php foreach ($testResults as $testName => $result): ?>
                <div class="test-item <?php echo $result['status'] === 'PASS' ? 'test-pass' : 'test-fail'; ?>">
                    <div>
                        <strong><?php echo htmlspecialchars($testName); ?></strong>
                        <br>
                        <small class="text-muted"><?php echo htmlspecialchars($result['message']); ?></small>
                    </div>
                    <span class="status-badge <?php echo $result['status'] === 'PASS' ? 'badge-pass' : 'badge-fail'; ?>">
                        <?php echo $result['status']; ?>
                    </span>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Module Testing Sections -->
            <div class="module-section">
                <h4 class="module-title"><i class="fas fa-chalkboard-teacher me-2"></i>Teacher Module Testing</h4>
                <div class="row">
                    <div class="col-md-6">
                        <h6>Enhanced Scoring System</h6>
                        <ul>
                            <li>Assessment (40 marks) + Exam (60 marks) = Total (100 marks)</li>
                            <li>Automatic grade calculation (A-F)</li>
                            <li>Teacher remarks functionality</li>
                            <li>Form teacher restrictions</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Test Actions</h6>
                        <a href="teacher/grades-new.php" class="btn btn-test btn-sm" target="_blank">
                            <i class="fas fa-graduation-cap me-1"></i>Test Enhanced Grading
                        </a>
                        <a href="teacher/dashboard.php" class="btn btn-test btn-sm" target="_blank">
                            <i class="fas fa-tachometer-alt me-1"></i>Teacher Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="module-section">
                <h4 class="module-title"><i class="fas fa-users me-2"></i>Parent Module Testing</h4>
                <div class="row">
                    <div class="col-md-6">
                        <h6>Payment System</h6>
                        <ul>
                            <li>Upload payment receipts</li>
                            <li>View payment status</li>
                            <li>Parent-student relationship mapping</li>
                            <li>Academic records viewing</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Test Actions</h6>
                        <a href="parent/pay-fees.php" class="btn btn-test btn-sm" target="_blank">
                            <i class="fas fa-credit-card me-1"></i>Test Payment System
                        </a>
                        <a href="parent/index.php" class="btn btn-test btn-sm" target="_blank">
                            <i class="fas fa-home me-1"></i>Parent Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="module-section">
                <h4 class="module-title"><i class="fas fa-user-graduate me-2"></i>Student Module Testing</h4>
                <div class="row">
                    <div class="col-md-6">
                        <h6>Student Features</h6>
                        <ul>
                            <li>View academic records</li>
                            <li>Check payment status</li>
                            <li>Attendance tracking</li>
                            <li>Notification system</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Test Actions</h6>
                        <a href="student/grades.php" class="btn btn-test btn-sm" target="_blank">
                            <i class="fas fa-chart-line me-1"></i>Test Grade Viewing
                        </a>
                        <a href="student/index.php" class="btn btn-test btn-sm" target="_blank">
                            <i class="fas fa-user me-1"></i>Student Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="module-section">
                <h4 class="module-title"><i class="fas fa-user-shield me-2"></i>Admin Module Testing</h4>
                <div class="row">
                    <div class="col-md-6">
                        <h6>Admin Features</h6>
                        <ul>
                            <li>Payment approval system</li>
                            <li>User management</li>
                            <li>Academic settings</li>
                            <li>Notification triggers</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Test Actions</h6>
                        <a href="admin/fee-confirmations.php" class="btn btn-test btn-sm" target="_blank">
                            <i class="fas fa-money-check-alt me-1"></i>Test Payment Approvals
                        </a>
                        <a href="admin/dashboard.php" class="btn btn-test btn-sm" target="_blank">
                            <i class="fas fa-cog me-1"></i>Admin Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="action-buttons">
                <a href="importdb.php" class="btn btn-test">
                    <i class="fas fa-database me-2"></i>Database Import Tool
                </a>
                <a href="direct-reset.php" class="btn btn-test">
                    <i class="fas fa-key me-2"></i>Reset Admin Password
                </a>
                <a href="login.php" class="btn btn-test">
                    <i class="fas fa-sign-in-alt me-2"></i>Test Login System
                </a>
                <button onclick="location.reload()" class="btn btn-test">
                    <i class="fas fa-sync-alt me-2"></i>Re-run Tests
                </button>
            </div>

            <div class="mt-4 text-center">
                <small class="text-muted">
                    Last tested: <?php echo date('Y-m-d H:i:s'); ?> |
                    Tests passed: <?php echo count(array_filter($testResults, function($r) { return $r['status'] === 'PASS'; })); ?>/<?php echo count($testResults); ?>
                </small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>