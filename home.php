<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IGSS - Intellectual Giant Secondary School</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 50%, #f7931e 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }





        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand-logo {
            height: 55px;
            width: 55px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            border: 3px solid #ff6b35;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .navbar-brand-logo:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(255, 107, 53, 0.3);
        }

        .hero-section {
            padding: 120px 0 80px 0;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .hero-title {
            font-size: 3.8rem;
            font-weight: 800;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            line-height: 1.2;
        }

        .hero-breadcrumb {
            margin-top: 30px;
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 500;
        }

        .hero-breadcrumb .breadcrumb-item {
            color: white;
        }

        .hero-breadcrumb i {
            font-size: 0.8rem;
            opacity: 0.7;
        }

        /* Decorative Elements */
        .hero-decorations {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .decoration-cloud-1 {
            position: absolute;
            top: 15%;
            right: 15%;
            font-size: 2.5rem;
            color: rgba(255, 255, 255, 0.3);
            animation: float 6s ease-in-out infinite;
        }

        .decoration-balloon-1 {
            position: absolute;
            top: 10%;
            right: 8%;
            font-size: 1.8rem;
            color: rgba(255, 255, 255, 0.4);
            animation: float 4s ease-in-out infinite 1s;
        }

        .decoration-plane-1 {
            position: absolute;
            top: 25%;
            left: 8%;
            font-size: 2rem;
            color: rgba(255, 255, 255, 0.3);
            animation: float 5s ease-in-out infinite 2s;
            transform: rotate(-15deg);
        }

        .decoration-student-1 {
            position: absolute;
            bottom: 35%;
            left: 12%;
            font-size: 2.2rem;
            color: rgba(255, 255, 255, 0.4);
            animation: float 4.5s ease-in-out infinite 0.5s;
        }

        .decoration-megaphone-1 {
            position: absolute;
            bottom: 30%;
            right: 20%;
            font-size: 2rem;
            color: rgba(255, 255, 255, 0.3);
            animation: float 5.5s ease-in-out infinite 1.5s;
        }

        /* Wavy Bottom Border */
        .hero-wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            overflow: hidden;
            line-height: 0;
        }

        .hero-wave svg {
            position: relative;
            display: block;
            width: calc(100% + 1.3px);
            height: 60px;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-15px);
            }
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 40px 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            height: 280px; /* Fixed height for all cards */
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff9a56, #ff6b35, #f7931e);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #ff9a56, #ff6b35);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            flex-shrink: 0; /* Prevent icon from shrinking */
        }

        .feature-card h4 {
            margin-bottom: 15px;
            font-weight: 600;
            color: #333;
            flex-shrink: 0; /* Prevent title from shrinking */
        }

        .feature-card p {
            margin: 0;
            color: #666;
            line-height: 1.6;
            flex-grow: 1; /* Allow text to fill remaining space */
            display: flex;
            align-items: center;
        }

        .btn-custom {
            background: linear-gradient(135deg, #ff9a56, #ff6b35);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
            color: white;
        }

        .footer {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding: 40px 0;
            margin-top: 80px;
        }

        /* Page Loading and Transition Effects */
        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #ff9a56, #ff6b35);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            opacity: 1;
            transition: opacity 0.8s ease;
        }

        .page-loader.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loader-logo {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: logoAppear 1.2s ease-out;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .loader-logo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .loader-text {
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
            margin-top: 15px;
            animation: fadeInUp 0.8s ease 0.5s both;
        }

        .page-switch {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #ff9a56, #ff6b35);
            z-index: 10000;
            transform: translateX(-100%);
            transition: transform 0.8s cubic-bezier(0.77, 0, 0.175, 1);
        }

        .page-switch.active {
            transform: translateX(0);
        }

        @keyframes logoAppear {
            0% {
                transform: scale(0) rotate(-180deg);
                opacity: 0;
            }
            60% {
                transform: scale(1.1) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: scale(1) rotate(0deg);
                opacity: 1;
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <!-- Page Loader -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-logo">
            <img src="images/logo.jpg" alt="IGSS Logo">
        </div>
        <div class="loader-text">Loading...</div>
    </div>

    <!-- Page Switch Effect -->
    <div class="page-switch" id="pageSwitch"></div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#">
                <img src="images/logo.jpg" alt="IGSS Logo" class="navbar-brand-logo">
                IGSS
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#features">Features</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-custom ms-2" href="login.php">Login</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-10 mx-auto">
                    <!-- Decorative Elements -->
                    <div class="hero-decorations">
                        <div class="decoration-cloud decoration-cloud-1">
                            <i class="fas fa-cloud"></i>
                        </div>
                        <div class="decoration-balloon decoration-balloon-1">
                            <i class="fas fa-circle"></i>
                        </div>
                        <div class="decoration-plane decoration-plane-1">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="decoration-student decoration-student-1">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <div class="decoration-megaphone decoration-megaphone-1">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                    </div>

                    <h1 class="hero-title">Welcome To Intellectual Giants Christian Academy</h1>

                    <!-- Breadcrumb -->
                    <nav class="hero-breadcrumb">
                        <span class="breadcrumb-item">Home</span>
                        <i class="fas fa-chevron-right mx-2"></i>
                        <span class="breadcrumb-item">Welcome To Intellectual Giants Christian Academy</span>
                    </nav>
                </div>
            </div>
        </div>

        <!-- Wavy Bottom Border -->
        <div class="hero-wave">
            <svg viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="#ffffff"></path>
                <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="#ffffff"></path>
                <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="#ffffff"></path>
            </svg>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5">
                    <h2 class="text-white fw-bold">Our Features</h2>
                    <p class="text-white-50">Comprehensive school management at your fingertips</p>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h4>User Management</h4>
                        <p>Efficiently manage students, teachers, parents, and administrative staff with role-based access control.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h4>Academic Calendar</h4>
                        <p>Organize academic sessions, terms, and important dates with our comprehensive calendar system.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h4>Grade Management</h4>
                        <p>Track student performance with detailed grading systems and comprehensive report generation.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-money-check-alt"></i>
                        </div>
                        <h4>Fee Management</h4>
                        <p>Streamline fee collection and payment tracking with automated confirmation systems.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <h4>Subject Assignment</h4>
                        <p>Efficiently assign subjects to teachers and manage class schedules across all levels.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4>Secure Access</h4>
                        <p>Role-based authentication ensures data security and appropriate access levels for all users.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <h5 class="fw-bold">Intellectual Giant Secondary School</h5>
                    <p class="text-muted">Electronic Management System</p>
                </div>
                <div class="col-lg-6 text-end">
                    <p class="text-muted">&copy; 2024 IGSS. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Page Loading and Transition Script -->
    <script>
        // Page Loading Effect
        window.addEventListener('load', function() {
            const pageLoader = document.getElementById('pageLoader');

            // Hide loader after page is fully loaded
            setTimeout(() => {
                pageLoader.classList.add('hidden');
            }, 1500);
        });

        // Page Transition Effect
        document.addEventListener('DOMContentLoaded', function() {
            const pageSwitch = document.getElementById('pageSwitch');

            // Get all links that should trigger transition
            const transitionLinks = document.querySelectorAll('a[href]:not([href^="#"]):not([href^="mailto"]):not([href^="tel"])');

            transitionLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    const href = this.getAttribute('href');

                    // Skip external links
                    if (href.startsWith('http') && !href.includes(window.location.hostname)) {
                        return;
                    }

                    e.preventDefault();

                    // Start page switch transition
                    pageSwitch.classList.add('active');

                    // Navigate after transition
                    setTimeout(() => {
                        window.location.href = href;
                    }, 800);
                });
            });
        });
    </script>
</body>
</html>
