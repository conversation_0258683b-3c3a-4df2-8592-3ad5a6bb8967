<?php
/**
 * Database Operations & Data Integrity Testing
 * IGSS Management System
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

session_start();

$testResults = [];
$message = '';
$error = '';

// Test Database CRUD Operations
function testCRUDOperations() {
    $results = [];

    try {
        $conn = getDBConnection();

        // Test 1: CREATE - Insert a test student
        $test_user_code = 'TEST_' . time();
        $stmt = $conn->prepare("
            INSERT INTO users (user_code, first_name, last_name, email, phone, user_type, password_hash, is_active)
            VALUES (?, 'Test', 'Student', '<EMAIL>', '1234567890', 'student', ?, 1)
        ");
        $password_hash = password_hash('test123', PASSWORD_DEFAULT);
        $stmt->execute([$test_user_code, $password_hash]);
        $test_user_id = $conn->lastInsertId();

        if ($test_user_id) {
            $results['CREATE'] = ['status' => 'PASS', 'message' => 'User created successfully'];
        } else {
            $results['CREATE'] = ['status' => 'FAIL', 'message' => 'Failed to create user'];
        }

        // Test 2: READ - Retrieve the test student
        $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$test_user_id]);
        $user = $stmt->fetch();

        if ($user && $user['user_code'] === $test_user_code) {
            $results['READ'] = ['status' => 'PASS', 'message' => 'User retrieved successfully'];
        } else {
            $results['READ'] = ['status' => 'FAIL', 'message' => 'Failed to retrieve user'];
        }

        // Test 3: UPDATE - Modify the test student
        $stmt = $conn->prepare("UPDATE users SET first_name = 'Updated' WHERE id = ?");
        $stmt->execute([$test_user_id]);

        $stmt = $conn->prepare("SELECT first_name FROM users WHERE id = ?");
        $stmt->execute([$test_user_id]);
        $updated_user = $stmt->fetch();

        if ($updated_user && $updated_user['first_name'] === 'Updated') {
            $results['UPDATE'] = ['status' => 'PASS', 'message' => 'User updated successfully'];
        } else {
            $results['UPDATE'] = ['status' => 'FAIL', 'message' => 'Failed to update user'];
        }

        // Test 4: DELETE - Remove the test student
        $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
        $stmt->execute([$test_user_id]);

        $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$test_user_id]);
        $deleted_user = $stmt->fetch();

        if (!$deleted_user) {
            $results['DELETE'] = ['status' => 'PASS', 'message' => 'User deleted successfully'];
        } else {
            $results['DELETE'] = ['status' => 'FAIL', 'message' => 'Failed to delete user'];
        }

    } catch (Exception $e) {
        $results['ERROR'] = ['status' => 'FAIL', 'message' => 'CRUD test error: ' . $e->getMessage()];
    }

    return $results;
}