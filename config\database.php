<?php
/**
 * Database Configuration for IGSS Management System
 * Intellectual Giant Secondary School Electronic Management System
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'igss_management');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Create database connection
class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    public $conn;

    // Get database connection
    public function getConnection() {
        $this->conn = null;

        try {
            // First try to connect to the specific database
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            // If database doesn't exist, try to create it
            try {
                $dsn = "mysql:host=" . $this->host . ";charset=" . $this->charset;
                $this->conn = new PDO($dsn, $this->username, $this->password);
                $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                // Create the database
                $this->conn->exec("CREATE DATABASE IF NOT EXISTS " . $this->db_name . " CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci");

                // Now connect to the created database
                $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
                $this->conn = new PDO($dsn, $this->username, $this->password);
                $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            } catch(PDOException $e) {
                echo "Connection error: " . $e->getMessage();
            }
        }

        return $this->conn;
    }

    // Test database connection
    public function testConnection() {
        try {
            $conn = $this->getConnection();
            if ($conn) {
                return true;
            }
            return false;
        } catch (Exception $e) {
            return false;
        }
    }

    // Execute SQL file (for schema installation)
    public function executeSQLFile($filepath) {
        try {
            $conn = $this->getConnection();
            if (!$conn) {
                throw new Exception("Database connection failed");
            }

            $sql = file_get_contents($filepath);
            if ($sql === false) {
                throw new Exception("Could not read SQL file: " . $filepath);
            }

            // Remove comments and split SQL into individual statements
            $sql = preg_replace('/--.*$/m', '', $sql); // Remove single line comments
            $sql = preg_replace('/\/\*.*?\*\//s', '', $sql); // Remove multi-line comments

            // Split SQL into individual statements
            $statements = array_filter(array_map('trim', explode(';', $sql)));

            foreach ($statements as $statement) {
                if (!empty($statement) && !preg_match('/^\s*$/', $statement)) {
                    try {
                        $conn->exec($statement);
                    } catch (PDOException $e) {
                        // Log the error but continue with other statements
                        error_log("SQL Statement error: " . $e->getMessage() . " - Statement: " . substr($statement, 0, 100));
                        // Don't throw exception for individual statement failures
                    }
                }
            }

            return true;
        } catch (Exception $e) {
            error_log("Database execution error: " . $e->getMessage());
            return false;
        }
    }
}

// Global database connection function
function getDBConnection() {
    $database = new Database();
    return $database->getConnection();
}

// Check if database exists and is properly configured
function isDatabaseConfigured() {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        if (!$conn) {
            return false;
        }

        // Check if users table exists (indicates database is set up)
        $stmt = $conn->prepare("SHOW TABLES LIKE 'users'");
        $stmt->execute();
        $result = $stmt->fetch();

        return $result !== false;
    } catch (Exception $e) {
        return false;
    }
}

// Initialize database if not configured
function initializeDatabase() {
    try {
        $database = new Database();
        $schemaPath = __DIR__ . '/../database/schema.sql';

        if (file_exists($schemaPath)) {
            return $database->executeSQLFile($schemaPath);
        }

        return false;
    } catch (Exception $e) {
        error_log("Database initialization error: " . $e->getMessage());
        return false;
    }
}
?>
