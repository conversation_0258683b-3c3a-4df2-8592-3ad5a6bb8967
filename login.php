<?php
/**
 * Login Page for IGSS Management System
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

// Check if admin setup is required
session_start();
if (isset($_SESSION['setup_required']) && $_SESSION['setup_required']) {
    header('Location: admin-setup.php');
    exit();
}

// Redirect if already logged in
if (isLoggedIn()) {
    redirectToDashboard();
}

$error = '';
$success = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $identifier = sanitizeInput($_POST['identifier'] ?? $_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $user_role = sanitizeInput($_POST['user_role'] ?? '');

    if (empty($identifier) || empty($password) || empty($user_role)) {
        $error = 'Please fill in all fields and select your role.';
    } else {
        if (authenticateUserWithRole($identifier, $password, $user_role)) {
            redirectToDashboard();
        } else {
            $error = 'Invalid credentials or incorrect role selection. Please try again.';
        }
    }
}

$school_info = getSchoolInfo();

// Check if logo needs to be created
if (!file_exists('logo.jpg')) {
    // We'll create the logo using a separate script
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
            overflow: hidden;
            padding: 0;
            margin: 0;
        }

        /* Floating Icons */
        .floating-icons {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .floating-icon {
            position: absolute;
            color: rgba(255, 255, 255, 0.25);
            animation: float 6s ease-in-out infinite;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
            filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.2));
        }

        .floating-icon:nth-child(1) {
            top: 10%;
            left: 10%;
            font-size: 60px;
            animation-delay: 0s;
        }

        .floating-icon:nth-child(2) {
            top: 20%;
            right: 15%;
            font-size: 40px;
            animation-delay: 1s;
        }

        .floating-icon:nth-child(3) {
            bottom: 30%;
            left: 5%;
            font-size: 50px;
            animation-delay: 2s;
        }

        .floating-icon:nth-child(4) {
            bottom: 10%;
            right: 10%;
            font-size: 45px;
            animation-delay: 3s;
        }

        .floating-icon:nth-child(5) {
            top: 50%;
            left: 3%;
            font-size: 35px;
            animation-delay: 4s;
        }

        .floating-icon:nth-child(6) {
            top: 70%;
            right: 5%;
            font-size: 55px;
            animation-delay: 5s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(10deg); }
        }

        .login-container {
            background: white;
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            max-width: 950px;
            width: 95%;
            height: 85vh;
            max-height: 600px;
            position: relative;
            z-index: 2;
            margin: 0;
        }

        .login-left {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
            color: white;
            padding: 40px 30px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            clip-path: polygon(0 0, 85% 0, 100% 100%, 0 100%);
            height: 100%;
        }

        .login-left::before {
            content: '';
            position: absolute;
            top: 0;
            right: -50px;
            width: 100px;
            height: 100%;
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
            clip-path: ellipse(50px 100% at 0% 50%);
            z-index: -1;
        }

        .login-left::after {
            content: '';
            position: absolute;
            top: 10%;
            right: -30px;
            width: 60px;
            height: 80%;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50px;
            transform: rotate(-15deg);
            z-index: 1;
        }

        .school-logo {
            width: 100px;
            height: 100px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 2;
            border: 4px solid rgba(255, 255, 255, 0.3);
        }

        .school-logo::before {
            content: '';
            position: absolute;
            top: -15px;
            left: -15px;
            right: -15px;
            bottom: -15px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            animation: pulse 3s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.05); opacity: 0.3; }
        }

        .school-logo img {
            width: 85%;
            height: 85%;
            object-fit: contain;
            border-radius: 50%;
        }

        .school-logo i {
            font-size: 60px;
            color: #ff6b35;
        }

        .login-left h2 {
            font-size: 22px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .login-left p {
            font-size: 13px;
            opacity: 0.9;
            line-height: 1.4;
        }

        .login-right {
            padding: 30px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            height: 100%;
        }

        .login-right::before {
            content: '';
            position: absolute;
            top: 20px;
            right: 20px;
            width: 120px;
            height: 120px;
            background: linear-gradient(45deg, #ff9a56, #ff6b35);
            border-radius: 50%;
            opacity: 0.15;
            z-index: 1;
        }

        .header-logo {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            border: 4px solid rgba(255, 154, 86, 0.4);
            z-index: 10;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .header-logo:hover {
            transform: scale(1.08);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 154, 86, 0.6);
        }

        .header-logo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
            padding: 8px;
        }

        .header-logo i {
            font-size: 50px;
            color: #ff6b35;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Back to Home Button */
        .back-to-home {
            position: absolute;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #ff9a56, #ff6b35);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 12px 25px;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
            z-index: 10;
        }

        .back-to-home:hover {
            transform: translateX(-50%) translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
            color: white;
            text-decoration: none;
        }

        .back-to-home i {
            font-size: 16px;
        }

        .login-right::after {
            content: '';
            position: absolute;
            bottom: 30px;
            left: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #ff6b35, #ff9a56);
            border-radius: 50%;
            opacity: 0.08;
            z-index: 1;
        }

        .form-wrapper {
            position: relative;
            z-index: 2;
        }

        .form-content {
            max-height: 400px;
            overflow-y: auto;
            overflow-x: hidden;
            padding-right: 15px;
            margin-right: -15px;
        }

        .form-content::-webkit-scrollbar {
            width: 8px;
        }

        .form-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .form-content::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #ff9a56, #ff6b35);
            border-radius: 10px;
        }

        .form-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #ff6b35, #ff5722);
        }

        .login-header {
            text-align: center;
            margin-bottom: 25px;
            position: relative;
            z-index: 2;
        }

        .login-header h3 {
            color: #333;
            font-weight: 700;
            margin-bottom: 8px;
            font-size: 26px;
            background: linear-gradient(135deg, #ff9a56, #ff6b35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .login-header p {
            color: #666;
            font-size: 14px;
            font-weight: 500;
        }

        .form-group {
            margin-bottom: 20px;
            position: relative;
            z-index: 2;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-control {
            border: 2px solid #e8ecef;
            border-radius: 12px;
            padding: 14px 20px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }

        .form-control:focus {
            border-color: #ff9a56;
            box-shadow: 0 0 0 0.2rem rgba(255, 154, 86, 0.25);
        }

        .input-group-text {
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            border: 2px solid #e8ecef;
            border-right: none;
            border-radius: 12px 0 0 12px;
            padding: 14px 16px;
            color: #ff6b35;
            font-weight: 600;
        }

        .input-group .form-control {
            border-left: none;
            border-radius: 0 12px 12px 0;
        }

        .btn-login {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
            border: none;
            border-radius: 12px;
            padding: 14px 30px;
            font-weight: 700;
            width: 100%;
            transition: all 0.3s ease;
            font-size: 15px;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(255, 154, 86, 0.4);
        }

        /* User Role Selection */
        .role-selection {
            margin-bottom: 20px;
        }

        .role-option {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            margin: 8px 0;
            border: 2px solid #e8ecef;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            position: relative;
            overflow: hidden;
        }

        .role-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 154, 86, 0.1), transparent);
            transition: left 0.5s;
        }

        .role-option:hover::before {
            left: 100%;
        }

        .role-option:hover {
            border-color: #ff9a56;
            background: linear-gradient(135deg, #fff5f0, #ffffff);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 154, 86, 0.15);
        }

        .role-option.selected {
            border-color: #ff6b35;
            background: linear-gradient(135deg, #fff5f0, #ffffff);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.2);
            transform: translateY(-2px);
        }

        .role-option input[type="radio"] {
            margin-right: 12px;
            accent-color: #ff6b35;
            transform: scale(1.1);
        }

        .role-icon {
            margin-right: 12px;
            font-size: 18px;
            color: #ff6b35;
            width: 25px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .role-option:hover .role-icon {
            transform: scale(1.1);
            color: #ff5722;
        }

        .role-option.selected .role-icon {
            transform: scale(1.1);
            color: #ff5722;
        }

        .role-text {
            flex: 1;
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }

        /* Password Toggle */
        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
            transition: color 0.3s ease;
            z-index: 10;
        }

        .password-toggle:hover {
            color: #ff6b35;
        }

        .alert {
            border-radius: 10px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 25px;
        }

        .links-section {
            text-align: center;
            margin-top: 15px;
        }

        .links-section a {
            color: #ff9a56;
            text-decoration: none;
            font-weight: 500;
            margin: 0 12px;
            font-size: 13px;
            transition: color 0.3s ease;
        }

        .links-section a:hover {
            color: #ff6b35;
        }

        @media (max-width: 768px) {
            body {
                padding: 5px 0;
                height: 100vh;
            }

            .login-container {
                margin: 5px;
                border-radius: 20px;
                height: 95vh;
                max-height: none;
            }

            .login-left {
                clip-path: none;
                padding: 30px 20px;
                height: auto;
                min-height: 200px;
            }

            .login-left::before,
            .login-left::after {
                display: none;
            }

            .login-right {
                padding: 10px 20px 10px 10px;
                height: auto;
                max-height: 60vh;
                overflow-y: auto;
            }

            .form-wrapper {
                padding: 15px 15px 30px 15px;
            }

            .school-logo {
                width: 80px;
                height: 80px;
            }

            .login-left h2 {
                font-size: 20px;
            }

            .login-header h3 {
                font-size: 22px;
            }

            .role-option {
                padding: 12px 16px;
                margin: 8px 0;
            }

            .form-control {
                padding: 14px 18px;
            }

            .btn-login {
                padding: 14px 25px;
            }

            .login-right::-webkit-scrollbar {
                width: 6px;
            }

            .back-to-home {
                bottom: 40px;
                left: 50%;
                transform: translateX(-50%);
                padding: 10px 20px;
                font-size: 13px;
            }
        }

        /* Page Loading and Transition Effects */
        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #ff9a56, #ff6b35);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            opacity: 1;
            transition: opacity 0.8s ease;
        }

        .page-loader.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loader-logo {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: logoAppear 1.2s ease-out;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .loader-logo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .loader-text {
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
            margin-top: 15px;
            animation: fadeInUp 0.8s ease 0.5s both;
        }

        .page-switch {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #ff9a56, #ff6b35);
            z-index: 10000;
            transform: translateX(-100%);
            transition: transform 0.8s cubic-bezier(0.77, 0, 0.175, 1);
        }

        .page-switch.active {
            transform: translateX(0);
        }

        @keyframes logoAppear {
            0% {
                transform: scale(0) rotate(-180deg);
                opacity: 0;
            }
            60% {
                transform: scale(1.1) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: scale(1) rotate(0deg);
                opacity: 1;
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <!-- Page Loader -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-logo">
            <?php if (file_exists('images/logo.jpg')): ?>
                <img src="images/logo.jpg" alt="School Logo">
            <?php else: ?>
                <i class="fas fa-graduation-cap" style="font-size: 60px; color: #ff6b35;"></i>
            <?php endif; ?>
        </div>
        <div class="loader-text">Loading...</div>
    </div>

    <!-- Page Switch Effect -->
    <div class="page-switch" id="pageSwitch"></div>

    <!-- Floating Icons -->
    <div class="floating-icons">
        <div class="floating-icon"><i class="fas fa-graduation-cap"></i></div>
        <div class="floating-icon"><i class="fas fa-book"></i></div>
        <div class="floating-icon"><i class="fas fa-users"></i></div>
        <div class="floating-icon"><i class="fas fa-chalkboard-teacher"></i></div>
        <div class="floating-icon"><i class="fas fa-apple-alt"></i></div>
        <div class="floating-icon"><i class="fas fa-pencil-alt"></i></div>
    </div>

    <div class="login-container">
        <div class="row g-0 h-100">
            <!-- Left Side - School Info -->
            <div class="col-lg-6">
                <div class="login-left h-100">
                    <div class="school-logo">
                        <i class="fas fa-graduation-cap"></i>
                    </div>

                    <h2><?php echo htmlspecialchars($school_info['name']); ?></h2>
                    <p>Electronic Management System</p>
                    <p class="mt-4">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        <?php echo htmlspecialchars($school_info['address']); ?>
                    </p>
                    <p>
                        <i class="fas fa-phone me-2"></i>
                        <?php echo htmlspecialchars($school_info['phone']); ?>
                    </p>

                    <!-- Back to Home Button -->
                    <a href="home.php" class="back-to-home">
                        <i class="fas fa-home"></i>
                        Back to Home
                    </a>
                </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="col-lg-6">
                <div class="login-right h-100">
                    <!-- Header Logo -->
                    <div class="header-logo">
                        <?php if (file_exists('images/logo.jpg')): ?>
                            <img src="images/logo.jpg" alt="School Logo">
                        <?php else: ?>
                            <i class="fas fa-graduation-cap"></i>
                        <?php endif; ?>
                    </div>

                    <div class="form-wrapper">
                        <div class="login-header">
                            <h3>Welcome Back!</h3>
                            <p>Please sign in to your account</p>
                        </div>

                        <div class="form-content">
                            <?php if ($error): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php echo htmlspecialchars($error); ?>
                                </div>
                            <?php endif; ?>

                            <?php if ($success): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <?php echo htmlspecialchars($success); ?>
                                </div>
                            <?php endif; ?>

                            <form method="POST" action="">
                                <!-- User Role Selection -->
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-user-tag me-2"></i>Select Your Role
                                    </label>
                                    <div class="role-selection">
                                        <label class="role-option" onclick="selectRole(this)">
                                            <input type="radio" name="user_role" value="admin"
                                                   <?php echo (($_POST['user_role'] ?? '') === 'admin') ? 'checked' : ''; ?> required>
                                            <i class="fas fa-user-shield role-icon"></i>
                                            <span class="role-text">Administrator</span>
                                        </label>
                                        <label class="role-option" onclick="selectRole(this)">
                                            <input type="radio" name="user_role" value="teacher"
                                                   <?php echo (($_POST['user_role'] ?? '') === 'teacher') ? 'checked' : ''; ?> required>
                                            <i class="fas fa-chalkboard-teacher role-icon"></i>
                                            <span class="role-text">Teacher</span>
                                        </label>
                                        <label class="role-option" onclick="selectRole(this)">
                                            <input type="radio" name="user_role" value="student"
                                                   <?php echo (($_POST['user_role'] ?? '') === 'student') ? 'checked' : ''; ?> required>
                                            <i class="fas fa-user-graduate role-icon"></i>
                                            <span class="role-text">Student</span>
                                        </label>
                                        <label class="role-option" onclick="selectRole(this)">
                                            <input type="radio" name="user_role" value="parent"
                                                   <?php echo (($_POST['user_role'] ?? '') === 'parent') ? 'checked' : ''; ?> required>
                                            <i class="fas fa-users role-icon"></i>
                                            <span class="role-text">Parent/Guardian</span>
                                        </label>
                                        <label class="role-option" onclick="selectRole(this)">
                                            <input type="radio" name="user_role" value="student"
                                                   <?php echo (($_POST['user_role'] ?? '') === 'student') ? 'checked' : ''; ?> required>
                                            <i class="fas fa-user-graduate role-icon"></i>
                                            <span class="role-text">Student</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="identifier" class="form-label" id="identifier-label">
                                        <i class="fas fa-envelope me-2" id="identifier-icon"></i>Email Address
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-envelope" id="identifier-input-icon"></i>
                                        </span>
                                        <input type="text"
                                               class="form-control"
                                               id="identifier"
                                               name="identifier"
                                               placeholder="Enter your email"
                                               value="<?php echo htmlspecialchars($_POST['identifier'] ?? $_POST['email'] ?? ''); ?>"
                                               required>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>Password
                                    </label>
                                    <div class="input-group password-container">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        <input type="password"
                                               class="form-control"
                                               id="password"
                                               name="password"
                                               placeholder="Enter your password"
                                               required>
                                        <span class="password-toggle" onclick="togglePassword('password')">
                                            <i class="fas fa-eye" id="password-toggle-icon"></i>
                                        </span>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary btn-login">
                                    <i class="fas fa-sign-in-alt me-2"></i>Sign In
                                </button>
                            </form>

                            <div class="links-section">
                                <a href="reset-password.php">
                                    <i class="fas fa-key me-1"></i>Reset Password
                                </a>
                                <a href="register.php">
                                    <i class="fas fa-user-plus me-1"></i>Register
                                </a>
                            </div>

                            <div class="text-center mt-4">
                                <small class="text-muted">
                                    © <?php echo date('Y'); ?> <?php echo htmlspecialchars($school_info['name']); ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Role selection function
        function selectRole(element) {
            // Remove selected class from all options
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });

            // Add selected class to clicked option
            element.classList.add('selected');

            // Check the radio button
            const radio = element.querySelector('input[type="radio"]');
            radio.checked = true;

            // Update identifier field based on role
            updateIdentifierField(radio.value);
        }

        // Function to update identifier field based on selected role
        function updateIdentifierField(role) {
            const identifierLabel = document.getElementById('identifier-label');
            const identifierIcon = document.getElementById('identifier-icon');
            const identifierInputIcon = document.getElementById('identifier-input-icon');
            const identifierInput = document.getElementById('identifier');

            switch(role) {
                case 'admin':
                    identifierLabel.innerHTML = '<i class="fas fa-user-shield me-2" id="identifier-icon"></i>Admin Code';
                    identifierIcon.className = 'fas fa-user-shield me-2';
                    identifierInputIcon.className = 'fas fa-user-shield';
                    identifierInput.placeholder = 'Enter your admin code';
                    identifierInput.type = 'text';
                    break;
                case 'teacher':
                    identifierLabel.innerHTML = '<i class="fas fa-chalkboard-teacher me-2" id="identifier-icon"></i>Teacher Code';
                    identifierIcon.className = 'fas fa-chalkboard-teacher me-2';
                    identifierInputIcon.className = 'fas fa-chalkboard-teacher';
                    identifierInput.placeholder = 'Enter your teacher code';
                    identifierInput.type = 'text';
                    break;
                case 'parent':
                    identifierLabel.innerHTML = '<i class="fas fa-users me-2" id="identifier-icon"></i>Parent/Guardian Code';
                    identifierIcon.className = 'fas fa-users me-2';
                    identifierInputIcon.className = 'fas fa-users';
                    identifierInput.placeholder = 'Enter your parent/guardian code';
                    identifierInput.type = 'text';
                    break;
                case 'student':
                    identifierLabel.innerHTML = '<i class="fas fa-user-graduate me-2" id="identifier-icon"></i>Student Code';
                    identifierIcon.className = 'fas fa-user-graduate me-2';
                    identifierInputIcon.className = 'fas fa-user-graduate';
                    identifierInput.placeholder = 'Enter your student code';
                    identifierInput.type = 'text';
                    break;
                default:
                    identifierLabel.innerHTML = '<i class="fas fa-envelope me-2" id="identifier-icon"></i>Email Address';
                    identifierIcon.className = 'fas fa-envelope me-2';
                    identifierInputIcon.className = 'fas fa-envelope';
                    identifierInput.placeholder = 'Enter your email';
                    identifierInput.type = 'email';
            }
        }

        // Password toggle function
        function togglePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const toggleIcon = document.getElementById(fieldId + '-toggle-icon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Page Loading Effect
        window.addEventListener('load', function() {
            const pageLoader = document.getElementById('pageLoader');

            // Hide loader after page is fully loaded
            setTimeout(() => {
                pageLoader.classList.add('hidden');
            }, 1500);
        });

        // Page Transition Effect
        document.addEventListener('DOMContentLoaded', function() {
            const pageSwitch = document.getElementById('pageSwitch');

            // Get all links that should trigger transition
            const transitionLinks = document.querySelectorAll('a[href]:not([href^="#"]):not([href^="mailto"]):not([href^="tel"])');

            transitionLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    const href = this.getAttribute('href');

                    // Skip external links
                    if (href.startsWith('http') && !href.includes(window.location.hostname)) {
                        return;
                    }

                    e.preventDefault();

                    // Start page switch transition
                    pageSwitch.classList.add('active');

                    // Navigate after transition
                    setTimeout(() => {
                        window.location.href = href;
                    }, 800);
                });
            });
        });

        // Initialize role selection on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Add click event listeners to role options
            document.querySelectorAll('.role-option').forEach(option => {
                option.addEventListener('click', function() {
                    selectRole(this);
                });

                // Check if this option should be selected (for form persistence)
                const radio = option.querySelector('input[type="radio"]');
                if (radio && radio.checked) {
                    option.classList.add('selected');
                    updateIdentifierField(radio.value);
                }
            });

            // If no role is selected, default to admin for demonstration
            const selectedRole = document.querySelector('input[name="user_role"]:checked');
            if (!selectedRole) {
                // Set default to admin role
                const adminOption = document.querySelector('input[name="user_role"][value="admin"]');
                if (adminOption) {
                    adminOption.checked = true;
                    adminOption.closest('.role-option').classList.add('selected');
                    updateIdentifierField('admin');
                }
            }
        });
    </script>
</body>
</html>
