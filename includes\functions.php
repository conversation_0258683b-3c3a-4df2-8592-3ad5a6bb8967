<?php
/**
 * Common Functions for IGSS Management System
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once __DIR__ . '/../config/config.php';

/**
 * User Authentication Functions
 */

/**
 * Authenticate user login
 */
function authenticateUser($email, $password) {
    try {
        $conn = getDBConnection();

        // For parents, allow login even if not approved (they'll be redirected to pending page)
        // For other user types, require approval
        $stmt = $conn->prepare("
            SELECT u.*, t.id as teacher_id, s.id as student_id, p.id as parent_id
            FROM users u
            LEFT JOIN teachers t ON u.id = t.user_id
            LEFT JOIN students s ON u.id = s.user_id
            LEFT JOIN parents p ON u.id = p.user_id
            WHERE u.email = ? AND u.is_active = 1
            AND (u.user_type = 'parent' OR u.is_approved = 1)
        ");
        $stmt->execute([$email]);
        $user = $stmt->fetch();

        if ($user && password_verify($password, $user['password_hash'])) {
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_type'] = $user['user_type'];
            $_SESSION['user_code'] = $user['user_code'];
            $_SESSION['full_name'] = $user['first_name'] . ' ' . $user['last_name'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['is_approved'] = $user['is_approved'];

            // Set type-specific IDs
            if ($user['teacher_id']) $_SESSION['teacher_id'] = $user['teacher_id'];
            if ($user['student_id']) $_SESSION['student_id'] = $user['student_id'];
            if ($user['parent_id']) $_SESSION['parent_id'] = $user['parent_id'];

            return true;
        }

        return false;
    } catch (Exception $e) {
        error_log("Authentication error: " . $e->getMessage());
        return false;
    }
}

/**
 * Authenticate user login with role validation
 */
function authenticateUserWithRole($identifier, $password, $user_role) {
    try {
        $conn = getDBConnection();

        // Check if identifier is an email (contains @) or a user code
        $isEmail = filter_var($identifier, FILTER_VALIDATE_EMAIL);

        if ($isEmail) {
            // Use email for authentication
            // For parents and students, allow login even if not approved (they'll be redirected to pending page)
            // For other user types, require approval
            $stmt = $conn->prepare("
                SELECT u.*, t.id as teacher_id, s.id as student_id, p.id as parent_id
                FROM users u
                LEFT JOIN teachers t ON u.id = t.user_id
                LEFT JOIN students s ON u.id = s.user_id
                LEFT JOIN parents p ON u.id = p.user_id
                WHERE u.email = ? AND u.user_type = ? AND u.is_active = 1
                AND (u.user_type IN ('parent', 'student') OR u.is_approved = 1)
            ");
            $stmt->execute([$identifier, $user_role]);
        } else {
            // Use user_code for authentication
            // For parents and students, allow login even if not approved (they'll be redirected to pending page)
            // For other user types, require approval
            $stmt = $conn->prepare("
                SELECT u.*, t.id as teacher_id, s.id as student_id, p.id as parent_id
                FROM users u
                LEFT JOIN teachers t ON u.id = t.user_id
                LEFT JOIN students s ON u.id = s.user_id
                LEFT JOIN parents p ON u.id = p.user_id
                WHERE u.user_code = ? AND u.user_type = ? AND u.is_active = 1
                AND (u.user_type IN ('parent', 'student') OR u.is_approved = 1)
            ");
            $stmt->execute([$identifier, $user_role]);
        }

        $user = $stmt->fetch();

        if ($user && password_verify($password, $user['password_hash'])) {
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_type'] = $user['user_type'];
            $_SESSION['user_code'] = $user['user_code'];
            $_SESSION['full_name'] = $user['first_name'] . ' ' . $user['last_name'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['is_approved'] = $user['is_approved'];

            // Set type-specific IDs
            if ($user['teacher_id']) $_SESSION['teacher_id'] = $user['teacher_id'];
            if ($user['student_id']) $_SESSION['student_id'] = $user['student_id'];
            if ($user['parent_id']) $_SESSION['parent_id'] = $user['parent_id'];

            return true;
        }

        return false;
    } catch (Exception $e) {
        error_log("Authentication error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get user information by ID
 */
function getUserInfo($userId) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("
            SELECT u.*, t.employee_id, s.admission_number, s.class_id, c.class_name
            FROM users u
            LEFT JOIN teachers t ON u.id = t.user_id
            LEFT JOIN students s ON u.id = s.user_id
            LEFT JOIN classes c ON s.class_id = c.id
            WHERE u.id = ?
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Academic Functions
 */

/**
 * Get all classes
 */
function getAllClasses() {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("
            SELECT c.*, l.level_name, l.level_code
            FROM classes c
            JOIN levels l ON c.level_id = l.id
            ORDER BY l.id, c.class_name, c.class_type
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

/**
 * Get subjects for a specific class
 */
function getSubjectsForClass($classId) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("
            SELECT s.* FROM subjects s
            JOIN classes c ON (s.level_id = c.level_id)
            WHERE c.id = ? AND (s.class_type = c.class_type OR s.class_type = 'All')
            ORDER BY s.subject_name
        ");
        $stmt->execute([$classId]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

/**
 * Get students in a class
 */
function getStudentsInClass($classId, $sessionId = null) {
    try {
        $conn = getDBConnection();

        $sql = "
            SELECT s.*, u.first_name, u.last_name, u.email, u.phone
            FROM students s
            JOIN users u ON s.user_id = u.id
            WHERE s.class_id = ? AND u.is_active = 1
        ";

        $params = [$classId];

        if ($sessionId) {
            $sql .= " AND s.current_session_id = ?";
            $params[] = $sessionId;
        }

        $sql .= " ORDER BY u.first_name, u.last_name";

        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

/**
 * Get teacher's assigned subjects
 */
function getTeacherSubjects($teacherId, $sessionId = null) {
    try {
        $conn = getDBConnection();

        $sql = "
            SELECT sa.*, s.subject_name, s.subject_code, c.class_name, c.class_type
            FROM subject_assignments sa
            JOIN subjects s ON sa.subject_id = s.id
            JOIN classes c ON sa.class_id = c.id
            WHERE sa.teacher_id = ? AND sa.is_active = 1
        ";

        $params = [$teacherId];

        if ($sessionId) {
            $sql .= " AND sa.session_id = ?";
            $params[] = $sessionId;
        }

        $sql .= " ORDER BY c.class_name, s.subject_name";

        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

/**
 * Check if teacher is form teacher for a class
 */
function isFormTeacher($teacherId, $classId, $sessionId = null) {
    try {
        $conn = getDBConnection();

        $sql = "
            SELECT COUNT(*) FROM class_assignments
            WHERE teacher_id = ? AND class_id = ? AND is_active = 1
        ";

        $params = [$teacherId, $classId];

        if ($sessionId) {
            $sql .= " AND session_id = ?";
            $params[] = $sessionId;
        }

        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Check if teacher can enter grades for a subject and class
 * Only form teachers or subject teachers can enter grades
 */
function canEnterGrades($teacherId, $subjectId, $classId, $sessionId = null) {
    try {
        $conn = getDBConnection();

        // Check if teacher is assigned to this subject and class
        $sql = "
            SELECT COUNT(*) FROM subject_assignments
            WHERE teacher_id = ? AND subject_id = ? AND class_id = ? AND is_active = 1
        ";

        $params = [$teacherId, $subjectId, $classId];

        if ($sessionId) {
            $sql .= " AND session_id = ?";
            $params[] = $sessionId;
        }

        $stmt = $conn->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Get teacher's form classes
 */
function getTeacherFormClasses($teacherId, $sessionId = null) {
    try {
        $conn = getDBConnection();

        $sql = "
            SELECT ca.*, c.class_name, l.level_name, COUNT(s.id) as student_count
            FROM class_assignments ca
            JOIN classes c ON ca.class_id = c.id
            JOIN levels l ON c.level_id = l.id
            LEFT JOIN students s ON c.id = s.class_id
            WHERE ca.teacher_id = ? AND ca.is_active = 1
        ";

        $params = [$teacherId];

        if ($sessionId) {
            $sql .= " AND ca.session_id = ?";
            $params[] = $sessionId;
        }

        $sql .= " GROUP BY ca.id, c.class_name, l.level_name ORDER BY l.id, c.class_name";

        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

/**
 * Attendance Functions
 */

/**
 * Mark attendance for a student
 */
function markAttendance($studentId, $classId, $sessionId, $termId, $date, $status, $teacherId, $remarks = '') {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("
            INSERT INTO attendance (student_id, class_id, session_id, term_id, attendance_date, status, marked_by, remarks)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            status = VALUES(status), marked_by = VALUES(marked_by), remarks = VALUES(remarks)
        ");

        return $stmt->execute([$studentId, $classId, $sessionId, $termId, $date, $status, $teacherId, $remarks]);
    } catch (Exception $e) {
        error_log("Attendance marking error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get attendance for a student
 */
function getStudentAttendance($studentId, $sessionId, $termId) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("
            SELECT a.*, u.first_name, u.last_name
            FROM attendance a
            JOIN teachers t ON a.marked_by = t.id
            JOIN users u ON t.user_id = u.id
            WHERE a.student_id = ? AND a.session_id = ? AND a.term_id = ?
            ORDER BY a.attendance_date DESC
        ");
        $stmt->execute([$studentId, $sessionId, $termId]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

/**
 * Grade Functions
 */

/**
 * Save student grade (Enhanced for new scoring system)
 */
function saveStudentGrade($studentId, $subjectId, $classId, $sessionId, $termId, $firstTest, $secondTest, $examScore, $teacherId) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("
            INSERT INTO student_grades (student_id, subject_id, class_id, session_id, term_id, first_test, second_test, exam_score, teacher_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            first_test = VALUES(first_test),
            second_test = VALUES(second_test),
            exam_score = VALUES(exam_score),
            teacher_id = VALUES(teacher_id)
        ");

        return $stmt->execute([$studentId, $subjectId, $classId, $sessionId, $termId, $firstTest, $secondTest, $examScore, $teacherId]);
    } catch (Exception $e) {
        error_log("Grade saving error: " . $e->getMessage());
        return false;
    }
}

/**
 * Save student grade with new scoring system (Assessment + Exam)
 */
function saveStudentGradeNew($studentId, $subjectId, $classId, $sessionId, $termId, $assessmentScore, $examScore, $teacherRemarks, $teacherId) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("
            INSERT INTO student_grades
            (student_id, subject_id, class_id, session_id, term_id, assessment_score, exam_score_new, teacher_remarks, teacher_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            assessment_score = VALUES(assessment_score),
            exam_score_new = VALUES(exam_score_new),
            teacher_remarks = VALUES(teacher_remarks),
            teacher_id = VALUES(teacher_id),
            updated_at = CURRENT_TIMESTAMP
        ");

        return $stmt->execute([$studentId, $subjectId, $classId, $sessionId, $termId, $assessmentScore, $examScore, $teacherRemarks, $teacherId]);
    } catch (Exception $e) {
        error_log("Enhanced grade saving error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get student grades for a term
 */
function getStudentGrades($studentId, $sessionId, $termId) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("
            SELECT sg.*, s.subject_name, s.subject_code
            FROM student_grades sg
            JOIN subjects s ON sg.subject_id = s.id
            WHERE sg.student_id = ? AND sg.session_id = ? AND sg.term_id = ?
            ORDER BY s.subject_name
        ");
        $stmt->execute([$studentId, $sessionId, $termId]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

/**
 * Calculate student average for a term
 */
function calculateStudentAverage($studentId, $sessionId, $termId) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("
            SELECT AVG(total_score) as average, COUNT(*) as subject_count
            FROM student_grades
            WHERE student_id = ? AND session_id = ? AND term_id = ?
        ");
        $stmt->execute([$studentId, $sessionId, $termId]);
        $result = $stmt->fetch();

        return [
            'average' => round($result['average'], 2),
            'subject_count' => $result['subject_count']
        ];
    } catch (Exception $e) {
        return ['average' => 0, 'subject_count' => 0];
    }
}

/**
 * File Upload Functions
 */

/**
 * Upload file with validation
 */
function uploadFile($file, $uploadDir, $allowedTypes, $maxSize = MAX_FILE_SIZE) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'message' => 'No file uploaded'];
    }

    $fileName = $file['name'];
    $fileSize = $file['size'];
    $fileTmp = $file['tmp_name'];
    $fileError = $file['error'];

    // Check for upload errors
    if ($fileError !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'File upload error'];
    }

    // Check file size
    if ($fileSize > $maxSize) {
        return ['success' => false, 'message' => 'File size exceeds maximum allowed size'];
    }

    // Get file extension
    $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

    // Check file type
    if (!in_array($fileExt, $allowedTypes)) {
        return ['success' => false, 'message' => 'File type not allowed'];
    }

    // Generate unique filename
    $newFileName = uniqid() . '_' . time() . '.' . $fileExt;
    $uploadPath = $uploadDir . $newFileName;

    // Create directory if it doesn't exist
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Move uploaded file
    if (move_uploaded_file($fileTmp, $uploadPath)) {
        return ['success' => true, 'filename' => $newFileName, 'path' => $uploadPath];
    } else {
        return ['success' => false, 'message' => 'Failed to move uploaded file'];
    }
}

/**
 * Utility Functions
 */

/**
 * Generate pagination HTML
 */
function generatePagination($currentPage, $totalPages, $baseUrl) {
    $html = '<nav aria-label="Page navigation"><ul class="pagination justify-content-center">';

    // Previous button
    if ($currentPage > 1) {
        $html .= '<li class="page-item"><a class="page-link" href="' . $baseUrl . '?page=' . ($currentPage - 1) . '">Previous</a></li>';
    }

    // Page numbers
    for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++) {
        $active = ($i == $currentPage) ? 'active' : '';
        $html .= '<li class="page-item ' . $active . '"><a class="page-link" href="' . $baseUrl . '?page=' . $i . '">' . $i . '</a></li>';
    }

    // Next button
    if ($currentPage < $totalPages) {
        $html .= '<li class="page-item"><a class="page-link" href="' . $baseUrl . '?page=' . ($currentPage + 1) . '">Next</a></li>';
    }

    $html .= '</ul></nav>';
    return $html;
}

/**
 * Format date for display
 */
function formatDate($date, $format = 'M d, Y') {
    return date($format, strtotime($date));
}

/**
 * Get time ago format
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';

    return floor($time/31536000) . ' years ago';
}

/**
 * Get fee structure for parents portal
 */
function getParentFeeStructure($studentId, $currentTerm = null) {
    try {
        $conn = getDBConnection();

        // Get student's class information
        $stmt = $conn->prepare("
            SELECT s.class_id, c.class_name, l.level_name
            FROM students s
            JOIN classes c ON s.class_id = c.id
            JOIN levels l ON c.level_id = l.id
            WHERE s.id = ?
        ");
        $stmt->execute([$studentId]);
        $studentClass = $stmt->fetch();

        if (!$studentClass) {
            return ['regular_fees' => [], 'exam_fees' => [], 'total' => 0];
        }

        // Get regular fees (non-exam fees)
        $stmt = $conn->prepare("
            SELECT fee_name, amount_js as amount, fee_description
            FROM fee_types
            WHERE is_active = 1 AND is_exam_fee = 0
            ORDER BY fee_name
        ");
        $stmt->execute();
        $regularFees = $stmt->fetchAll();

        // Get exam fees for this student's class and current term
        $examFees = [];
        if ($currentTerm) {
            $targetClass = '';
            if (strpos($studentClass['class_name'], 'JSS3') !== false) {
                $targetClass = 'JSS3';
            } elseif (strpos($studentClass['class_name'], 'SS3') !== false) {
                $targetClass = 'SS3';
            }

            if ($targetClass) {
                $stmt = $conn->prepare("
                    SELECT fee_name, amount_js as amount, fee_description
                    FROM fee_types
                    WHERE is_active = 1 AND is_exam_fee = 1
                    AND target_class = ? AND payment_term = ?
                    ORDER BY fee_name
                ");
                $stmt->execute([$targetClass, $currentTerm]);
                $examFees = $stmt->fetchAll();
            }
        }

        // Calculate total
        $total = 0;
        foreach ($regularFees as $fee) {
            $total += $fee['amount'];
        }
        foreach ($examFees as $fee) {
            $total += $fee['amount'];
        }

        return [
            'regular_fees' => $regularFees,
            'exam_fees' => $examFees,
            'total' => $total,
            'student_class' => $studentClass
        ];

    } catch (Exception $e) {
        return ['regular_fees' => [], 'exam_fees' => [], 'total' => 0];
    }
}

/**
 * Check if student has pending exam fees
 */
function hasExamFees($studentId, $currentTerm) {
    try {
        $conn = getDBConnection();

        // Get student's class
        $stmt = $conn->prepare("
            SELECT c.class_name
            FROM students s
            JOIN classes c ON s.class_id = c.id
            WHERE s.id = ?
        ");
        $stmt->execute([$studentId]);
        $studentClass = $stmt->fetch();

        if (!$studentClass) return false;

        $targetClass = '';
        if (strpos($studentClass['class_name'], 'JSS3') !== false) {
            $targetClass = 'JSS3';
        } elseif (strpos($studentClass['class_name'], 'SS3') !== false) {
            $targetClass = 'SS3';
        }

        if (!$targetClass) return false;

        // Check if there are exam fees for this class and term
        $stmt = $conn->prepare("
            SELECT COUNT(*) as count
            FROM fee_types
            WHERE is_active = 1 AND is_exam_fee = 1
            AND target_class = ? AND payment_term = ?
        ");
        $stmt->execute([$targetClass, $currentTerm]);
        $result = $stmt->fetch();

        return $result['count'] > 0;

    } catch (Exception $e) {
        return false;
    }
}
?>
