<?php
/**
 * Database Setup Page for IGSS Management System
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once 'config/database.php';

$message = '';
$error = '';
$setup_complete = false;

// Check if database is already configured
if (isDatabaseConfigured()) {
    $setup_complete = true;
    $message = 'Database is already configured and ready to use.';
}

// Handle setup form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$setup_complete) {
    try {
        if (initializeDatabase()) {
            $setup_complete = true;
            $message = 'Database has been successfully initialized! You can now use the system.';
        } else {
            $error = 'Failed to initialize database. Please check your database configuration.';
        }
    } catch (Exception $e) {
        $error = 'Setup error: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Setup - IGSS Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .setup-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            overflow: hidden;
        }
        
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .setup-header h2 {
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .setup-body {
            padding: 40px;
        }
        
        .setup-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 36px;
        }
        
        .btn-setup {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-setup:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .setup-steps {
            list-style: none;
            padding: 0;
            margin: 30px 0;
        }
        
        .setup-steps li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        
        .setup-steps li:last-child {
            border-bottom: none;
        }
        
        .step-icon {
            width: 30px;
            height: 30px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 14px;
        }
        
        .step-icon.completed {
            background: #28a745;
        }
        
        .database-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .database-info h6 {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .database-info p {
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .database-info strong {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <div class="setup-icon">
                <i class="fas fa-database"></i>
            </div>
            <h2>Database Setup</h2>
            <p>Initialize IGSS Management System Database</p>
        </div>
        
        <div class="setup-body">
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!$setup_complete): ?>
                <div class="text-center mb-4">
                    <h4>Welcome to IGSS Management System</h4>
                    <p class="text-muted">Let's set up your database to get started</p>
                </div>
                
                <div class="database-info">
                    <h6><i class="fas fa-info-circle me-2"></i>Database Configuration</h6>
                    <p><strong>Host:</strong> <?php echo DB_HOST; ?></p>
                    <p><strong>Database:</strong> <?php echo DB_NAME; ?></p>
                    <p><strong>Username:</strong> <?php echo DB_USER; ?></p>
                    <p><strong>Charset:</strong> <?php echo DB_CHARSET; ?></p>
                </div>
                
                <ul class="setup-steps">
                    <li>
                        <div class="step-icon">1</div>
                        <div>Create database tables and relationships</div>
                    </li>
                    <li>
                        <div class="step-icon">2</div>
                        <div>Insert initial data (levels, classes, subjects)</div>
                    </li>
                    <li>
                        <div class="step-icon">3</div>
                        <div>Create default admin user</div>
                    </li>
                    <li>
                        <div class="step-icon">4</div>
                        <div>Configure system settings</div>
                    </li>
                    <li>
                        <div class="step-icon">5</div>
                        <div>Create database indexes for performance</div>
                    </li>
                </ul>
                
                <form method="POST" action="">
                    <button type="submit" class="btn btn-primary btn-setup">
                        <i class="fas fa-play me-2"></i>Initialize Database
                    </button>
                </form>
                
                <div class="text-center mt-4">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        This will create all necessary database structures for the system
                    </small>
                </div>
                
            <?php else: ?>
                <div class="text-center">
                    <div class="mb-4">
                        <i class="fas fa-check-circle text-success" style="font-size: 64px;"></i>
                    </div>
                    <h4 class="text-success">Setup Complete!</h4>
                    <p class="text-muted mb-4">Your database has been successfully configured.</p>
                    
                    <div class="database-info">
                        <h6><i class="fas fa-user-shield me-2"></i>Default Admin Account</h6>
                        <p><strong>User Code:</strong> ADMIN001</p>
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>Password:</strong> password (Please change immediately)</p>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <a href="direct-reset.php" class="btn btn-warning">
                            <i class="fas fa-key me-2"></i>Reset Admin Password
                        </a>
                        <a href="login.php" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Go to Login
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
