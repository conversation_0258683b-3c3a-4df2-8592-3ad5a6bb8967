<?php
/**
 * Admin Setup Page for IGSS Management System
 * First-time admin password setup after database import
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

session_start();

// Check if setup is needed
if (!isset($_SESSION['admin_setup_needed']) || !$_SESSION['admin_setup_needed']) {
    // Check if we should allow setup anyway
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE user_code = 'ADMIN001' AND user_type = 'admin'");
        $stmt->execute();
        $adminExists = $stmt->fetchColumn() > 0;

        if (!$adminExists) {
            // No admin user exists, allow setup
            $_SESSION['admin_setup_needed'] = true;
        }
    } catch (Exception $e) {
        // Continue with setup if there's an error
        $_SESSION['admin_setup_needed'] = true;
    }
}

$message = '';
$error = '';
$success = false;

// Handle setup form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $admin_email = sanitizeInput($_POST['admin_email'] ?? '');
    $admin_password = $_POST['admin_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $first_name = sanitizeInput($_POST['first_name'] ?? '');
    $last_name = sanitizeInput($_POST['last_name'] ?? '');

    // Validation
    if (empty($admin_email) || empty($admin_password) || empty($confirm_password) || empty($first_name) || empty($last_name)) {
        $error = 'All fields are required.';
    } elseif (!filter_var($admin_email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } elseif ($admin_password !== $confirm_password) {
        $error = 'Passwords do not match.';
    } elseif (strlen($admin_password) < PASSWORD_MIN_LENGTH) {
        $error = 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long.';
    } else {
        try {
            $conn = getDBConnection();

            // Hash the new password
            $password_hash = password_hash($admin_password, PASSWORD_DEFAULT);

            // Update the admin user with new details
            $stmt = $conn->prepare("
                UPDATE users
                SET password_hash = ?, email = ?, first_name = ?, last_name = ?, is_approved = 1, is_active = 1, updated_at = NOW()
                WHERE user_code = 'ADMIN001' AND user_type = 'admin'
            ");

            $result = $stmt->execute([$password_hash, $admin_email, $first_name, $last_name]);

            if ($result && $stmt->rowCount() > 0) {
                // Clear setup session variables
                unset($_SESSION['admin_setup_needed']);

                $success = true;
                $message = 'Admin password set successfully! You can now log in with User Code: ADMIN001 and your chosen password.';
            } else {
                $error = 'Failed to update admin password. Please ensure the database was imported correctly.';
            }
        } catch (Exception $e) {
            $error = 'Database error: ' . $e->getMessage();
        }
    }
}

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Setup - <?php echo $school_info['name']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .setup-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .setup-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .setup-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .setup-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-setup {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-setup:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .password-requirements {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            font-size: 0.9rem;
        }

        .requirement {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }

        .requirement i {
            margin-right: 8px;
            width: 16px;
        }

        .requirement.valid {
            color: #28a745;
        }

        .requirement.invalid {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <div class="setup-header">
                <div class="setup-icon">
                    <i class="fas fa-user-shield"></i>
                </div>
                <h3>Set Admin Password</h3>
                <p class="text-muted">Choose your admin email and password. You'll use User Code <strong>ADMIN001</strong> to login.</p>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <div class="mt-2">
                        <small>
                            <strong>Troubleshooting:</strong><br>
                            • Check if your database is running<br>
                            • Verify database connection settings<br>
                            • Try using <a href="direct-reset.php" class="text-white"><u>Direct Password Reset</u></a> as an alternative<br>
                            • Check <a href="test-admin-setup.php" class="text-white"><u>Admin Setup Test</u></a> for diagnostics
                        </small>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success text-center">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                    <div class="mt-3">
                        <a href="login.php" class="btn btn-success">
                            <i class="fas fa-sign-in-alt me-2"></i>Go to Login
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <form method="POST" action="" id="setupForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">First Name</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" 
                                   value="<?php echo htmlspecialchars($_POST['first_name'] ?? 'System'); ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">Last Name</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" 
                                   value="<?php echo htmlspecialchars($_POST['last_name'] ?? 'Administrator'); ?>" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="admin_email" class="form-label">Admin Email</label>
                        <input type="email" class="form-control" id="admin_email" name="admin_email" 
                               value="<?php echo htmlspecialchars($_POST['admin_email'] ?? '<EMAIL>'); ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="admin_password" class="form-label">Admin Password</label>
                        <input type="password" class="form-control" id="admin_password" name="admin_password" required>
                    </div>

                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>

                    <div class="password-requirements">
                        <strong>Password Requirements:</strong>
                        <div class="requirement" id="req-length">
                            <i class="fas fa-times"></i>
                            At least <?php echo PASSWORD_MIN_LENGTH; ?> characters long
                        </div>
                        <div class="requirement" id="req-match">
                            <i class="fas fa-times"></i>
                            Passwords match
                        </div>
                    </div>

                    <button type="submit" class="btn btn-setup mt-4">
                        <i class="fas fa-key me-2"></i>Save Admin Password
                    </button>
                </form>

                <div class="text-center mt-4">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        After saving, login with User Code: <strong>ADMIN001</strong> and your chosen password
                    </small>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Password validation
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('admin_password');
            const confirmInput = document.getElementById('confirm_password');
            const lengthReq = document.getElementById('req-length');
            const matchReq = document.getElementById('req-match');

            function validatePassword() {
                const password = passwordInput.value;
                const confirm = confirmInput.value;

                // Length requirement
                if (password.length >= <?php echo PASSWORD_MIN_LENGTH; ?>) {
                    lengthReq.classList.add('valid');
                    lengthReq.classList.remove('invalid');
                    lengthReq.querySelector('i').className = 'fas fa-check';
                } else {
                    lengthReq.classList.add('invalid');
                    lengthReq.classList.remove('valid');
                    lengthReq.querySelector('i').className = 'fas fa-times';
                }

                // Match requirement
                if (password && confirm && password === confirm) {
                    matchReq.classList.add('valid');
                    matchReq.classList.remove('invalid');
                    matchReq.querySelector('i').className = 'fas fa-check';
                } else {
                    matchReq.classList.add('invalid');
                    matchReq.classList.remove('valid');
                    matchReq.querySelector('i').className = 'fas fa-times';
                }
            }

            passwordInput.addEventListener('input', validatePassword);
            confirmInput.addEventListener('input', validatePassword);
        });
    </script>
</body>
</html>
