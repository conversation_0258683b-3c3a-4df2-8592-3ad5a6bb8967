<?php
/**
 * <PERSON> Details Page for Parents
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a parent
if (!isLoggedIn() || $_SESSION['user_type'] !== 'parent') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get parent information including approval status
$stmt = $conn->prepare("
    SELECT p.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, u.user_code, u.is_approved
    FROM parents p
    JOIN users u ON p.user_id = u.id
    WHERE p.user_id = ?
");
$stmt->execute([$user_id]);
$parent = $stmt->fetch();

if (!$parent) {
    header('Location: ../login.php');
    exit();
}

// Check if parent account is approved
if (!$parent['is_approved']) {
    header('Location: pending-approval.php');
    exit();
}

// Get student ID from URL
$student_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$student_id) {
    header('Location: index.php');
    exit();
}

// Verify parent has access to this student
$stmt = $conn->prepare("
    SELECT COUNT(*) FROM parent_student_relationships 
    WHERE parent_id = ? AND student_id = ?
");
$stmt->execute([$parent['id'], $student_id]);
$has_access = $stmt->fetchColumn();

if (!$has_access) {
    header('Location: index.php');
    exit();
}

// Get detailed student information
$stmt = $conn->prepare("
    SELECT
        s.*,
        u.first_name,
        u.last_name,
        u.middle_name,
        u.email,
        u.phone,
        u.date_of_birth,
        u.gender,
        u.address,
        u.profile_image,
        u.user_code,
        c.class_name,
        l.level_name,
        psr.relationship_type,
        psr.is_primary,
        psr.created_at as linked_date,
        asess.session_name,
        t.term_name
    FROM students s
    JOIN users u ON s.user_id = u.id
    JOIN classes c ON s.class_id = c.id
    JOIN levels l ON c.level_id = l.id
    JOIN parent_student_relationships psr ON s.id = psr.student_id
    LEFT JOIN academic_sessions asess ON s.current_session_id = asess.id
    LEFT JOIN terms t ON s.current_term_id = t.id
    WHERE s.id = ? AND psr.parent_id = ?
");
$stmt->execute([$student_id, $parent['id']]);
$student = $stmt->fetch();

// Function to get child relationship from parent relationship
function getChildRelationship($parentRelationship, $childGender) {
    $relationships = [
        'Father' => $childGender === 'Male' ? 'Son' : 'Daughter',
        'Mother' => $childGender === 'Male' ? 'Son' : 'Daughter',
        'Guardian' => 'Ward',
        'Uncle' => $childGender === 'Male' ? 'Nephew' : 'Niece',
        'Aunt' => $childGender === 'Male' ? 'Nephew' : 'Niece',
        'Grandparent' => 'Grandchild',
        'Sibling' => 'Sibling',
        'Other' => 'Ward'
    ];

    return $relationships[$parentRelationship] ?? 'Ward';
}

if (!$student) {
    header('Location: index.php');
    exit();
}

// Get recent attendance summary
$stmt = $conn->prepare("
    SELECT 
        COUNT(*) as total_days,
        SUM(CASE WHEN status = 'Present' THEN 1 ELSE 0 END) as present_days,
        SUM(CASE WHEN status = 'Absent' THEN 1 ELSE 0 END) as absent_days,
        SUM(CASE WHEN status = 'Late' THEN 1 ELSE 0 END) as late_days
    FROM attendance 
    WHERE student_id = ? 
    AND attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
");
$stmt->execute([$student_id]);
$attendance_summary = $stmt->fetch();

// Calculate attendance percentage
$attendance_percentage = 0;
if ($attendance_summary['total_days'] > 0) {
    $attendance_percentage = round(($attendance_summary['present_days'] / $attendance_summary['total_days']) * 100, 1);
}

// Get recent grades summary
$stmt = $conn->prepare("
    SELECT 
        sub.subject_name,
        sg.ca_score,
        sg.exam_score,
        sg.total_score,
        sg.grade,
        sg.remarks
    FROM student_grades sg
    JOIN subjects sub ON sg.subject_id = sub.id
    WHERE sg.student_id = ? 
    AND sg.session_id = ?
    AND sg.term_id = ?
    ORDER BY sub.subject_name
    LIMIT 5
");
$stmt->execute([$student_id, $student['current_session_id'], $student['current_term_id']]);
$recent_grades = $stmt->fetchAll();

// Get outstanding fees
$stmt = $conn->prepare("
    SELECT 
        SUM(ft.amount) as total_fees,
        COALESCE(SUM(fp.amount_paid), 0) as paid_amount
    FROM fee_types ft
    LEFT JOIN fee_payments fp ON ft.id = fp.fee_type_id 
        AND fp.student_id = ? 
        AND fp.session_id = ?
        AND fp.status = 'Confirmed'
    WHERE ft.session_id = ?
");
$stmt->execute([$student_id, $student['current_session_id'], $student['current_session_id']]);
$fee_summary = $stmt->fetch();

$outstanding_fees = ($fee_summary['total_fees'] ?? 0) - ($fee_summary['paid_amount'] ?? 0);

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();
$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ward Details - <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?> - <?php echo $school_info['name']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 50%, #ff4500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }
        
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 1050;
        }

        .navbar-custom .dropdown-menu {
            z-index: 1060;
            position: absolute;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            margin-top: 8px;
            min-width: 220px;
        }

        .navbar-custom .dropdown-item {
            padding: 12px 20px;
            border-radius: 10px;
            margin: 4px 8px;
            transition: all 0.3s ease;
        }

        .navbar-custom .dropdown-item:hover {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            color: white;
            transform: translateX(5px);
        }

        .navbar-custom .dropdown-item i {
            width: 20px;
            text-align: center;
        }
        
        .main-container {
            padding: 20px;
            position: relative;
            z-index: 1;
        }
        
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff8c00, #ff6b35);
        }
        
        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff8c00, #ff6b35);
        }

        .student-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            font-weight: bold;
            margin: 0 auto 20px;
            box-shadow: 0 8px 25px rgba(255, 140, 0, 0.3);
        }
        
        .btn-orange {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
            border: none;
            color: white;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 140, 0, 0.3);
        }
        
        .btn-orange:hover {
            background: linear-gradient(135deg, #ff9500 0%, #ff7635 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 140, 0, 0.4);
        }

        .btn-action {
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 2px;
        }

        .btn-action:hover {
            transform: translateY(-2px);
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #28a745, #20c997);
        }

        .stat-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #ff8c00;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #6c757d;
        }

        .info-value {
            color: #2c3e50;
            font-weight: 500;
        }

        .grade-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #28a745;
        }

        .admission-number {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #ff8c00;
            font-size: 1.2rem;
            background: rgba(255, 140, 0, 0.1);
            padding: 8px 16px;
            border-radius: 10px;
            display: inline-block;
        }

        .relationship-badge {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
        }

        .primary-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-users me-2 text-orange"></i>
                IGSS Parent Portal
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle fw-semibold" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($parent['first_name'] . ' ' . $parent['last_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="index.php"><i class="fas fa-home me-2"></i>Dashboard</a></li>
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
                        <li><a class="dropdown-item" href="ward-management.php"><i class="fas fa-users me-2"></i>Ward Management</a></li>
                        <li><a class="dropdown-item" href="payments.php"><i class="fas fa-credit-card me-2"></i>Fee Payments</a></li>
                        <li><a class="dropdown-item" href="messages.php"><i class="fas fa-envelope me-2"></i>Messages</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-2">
                            <i class="fas fa-user-graduate me-2"></i>
                            Ward Details - <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                        </h2>
                        <p class="text-muted mb-0">
                            Comprehensive information about your ward
                        </p>
                    </div>
                    <div>
                        <a href="index.php" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                        <a href="ward-management.php" class="btn btn-outline-primary">
                            <i class="fas fa-cog me-2"></i>Manage Wards
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Student Profile -->
                <div class="col-lg-4">
                    <div class="content-card text-center">
                        <div class="student-avatar">
                            <?php echo strtoupper(substr($student['first_name'], 0, 1) . substr($student['last_name'], 0, 1)); ?>
                        </div>

                        <h4 class="mb-2">
                            <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                        </h4>

                        <div class="mb-3">
                            <span class="admission-number">
                                <?php echo htmlspecialchars($student['admission_number']); ?>
                            </span>
                        </div>

                        <div class="mb-3">
                            <span class="relationship-badge me-2">
                                <?php echo htmlspecialchars(getChildRelationship($student['relationship_type'], $student['gender'])); ?>
                            </span>
                            <?php if ($student['is_primary']): ?>
                                <span class="primary-badge">Primary Ward</span>
                            <?php endif; ?>
                        </div>

                        <div class="d-grid gap-2">
                            <a href="ward-grades.php?id=<?php echo $student_id; ?>" class="btn btn-success btn-action">
                                <i class="fas fa-chart-line me-2"></i>Academic Records
                            </a>
                            <a href="ward-attendance.php?id=<?php echo $student_id; ?>" class="btn btn-info btn-action">
                                <i class="fas fa-calendar-check me-2"></i>Attendance History
                            </a>
                            <a href="pay-fees.php?student_id=<?php echo $student_id; ?>" class="btn btn-orange btn-action">
                                <i class="fas fa-credit-card me-2"></i>Pay Fees
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Student Information -->
                <div class="col-lg-8">
                    <div class="content-card">
                        <h5 class="mb-4">
                            <i class="fas fa-info-circle me-2"></i>Personal Information
                        </h5>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item">
                                    <span class="info-label">Full Name:</span>
                                    <span class="info-value">
                                        <?php echo htmlspecialchars($student['first_name'] . ' ' . ($student['middle_name'] ? $student['middle_name'] . ' ' : '') . $student['last_name']); ?>
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Gender:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($student['gender']); ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Date of Birth:</span>
                                    <span class="info-value">
                                        <?php echo $student['date_of_birth'] ? date('M d, Y', strtotime($student['date_of_birth'])) : 'Not set'; ?>
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Email:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($student['email'] ?? 'Not set'); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <span class="info-label">Class:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($student['class_name']); ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Level:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($student['level_name']); ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Session:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($student['session_name'] ?? 'Current Session'); ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Term:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($student['term_name'] ?? 'Current Term'); ?></span>
                                </div>
                            </div>
                        </div>

                        <?php if ($student['address']): ?>
                            <div class="mt-3">
                                <div class="info-item">
                                    <span class="info-label">Address:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($student['address']); ?></span>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $attendance_percentage; ?>%</div>
                        <div class="stat-label">Attendance Rate</div>
                        <small class="text-muted">Last 30 days</small>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count($recent_grades); ?></div>
                        <div class="stat-label">Subjects</div>
                        <small class="text-muted">Current term</small>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number">₦<?php echo number_format($outstanding_fees, 0); ?></div>
                        <div class="stat-label">Outstanding Fees</div>
                        <small class="text-muted">Current session</small>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo date('Y', strtotime($student['linked_date'])); ?></div>
                        <div class="stat-label">Linked Since</div>
                        <small class="text-muted"><?php echo date('M Y', strtotime($student['linked_date'])); ?></small>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Recent Grades -->
                <div class="col-lg-6">
                    <div class="content-card">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>Recent Grades
                            </h5>
                            <a href="ward-grades.php?id=<?php echo $student_id; ?>" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-eye me-1"></i>View All
                            </a>
                        </div>

                        <?php if (!empty($recent_grades)): ?>
                            <?php foreach ($recent_grades as $grade): ?>
                                <div class="grade-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($grade['subject_name']); ?></h6>
                                            <small class="text-muted">
                                                CA: <?php echo $grade['ca_score'] ?? 'N/A'; ?> |
                                                Exam: <?php echo $grade['exam_score'] ?? 'N/A'; ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold text-success">
                                                <?php echo $grade['total_score'] ?? 'N/A'; ?>
                                            </div>
                                            <small class="text-muted">
                                                Grade: <?php echo $grade['grade'] ?? 'N/A'; ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-chart-line fa-2x mb-3"></i>
                                <p>No grades available for current term</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Attendance Summary -->
                <div class="col-lg-6">
                    <div class="content-card">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="mb-0">
                                <i class="fas fa-calendar-check me-2"></i>Attendance Summary
                            </h5>
                            <a href="ward-attendance.php?id=<?php echo $student_id; ?>" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a>
                        </div>

                        <div class="row text-center">
                            <div class="col-3">
                                <div class="mb-2">
                                    <div class="h4 text-success"><?php echo $attendance_summary['present_days']; ?></div>
                                    <small class="text-muted">Present</small>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="mb-2">
                                    <div class="h4 text-danger"><?php echo $attendance_summary['absent_days']; ?></div>
                                    <small class="text-muted">Absent</small>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="mb-2">
                                    <div class="h4 text-warning"><?php echo $attendance_summary['late_days']; ?></div>
                                    <small class="text-muted">Late</small>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="mb-2">
                                    <div class="h4 text-primary"><?php echo $attendance_summary['total_days']; ?></div>
                                    <small class="text-muted">Total</small>
                                </div>
                            </div>
                        </div>

                        <div class="progress mt-3" style="height: 10px;">
                            <div class="progress-bar bg-success" role="progressbar"
                                 style="width: <?php echo $attendance_percentage; ?>%">
                            </div>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                <?php echo $attendance_percentage; ?>% attendance rate (Last 30 days)
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced hover effects
        document.addEventListener('DOMContentLoaded', function() {
            const statCards = document.querySelectorAll('.stat-card');

            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.05)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Add click animations to action buttons
            const actionButtons = document.querySelectorAll('.btn-action');
            actionButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
