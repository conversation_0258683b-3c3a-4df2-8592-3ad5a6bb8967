<?php
/**
 * Subject Details Page for Teachers
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a teacher
if (!isLoggedIn() || $_SESSION['user_type'] !== 'teacher') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get subject ID from URL
$subject_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$subject_id) {
    header('Location: my-subjects.php');
    exit();
}

// Get teacher information
$stmt = $conn->prepare("
    SELECT t.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, u.user_code
    FROM teachers t
    JOIN users u ON t.user_id = u.id
    WHERE t.user_id = ?
");
$stmt->execute([$user_id]);
$teacher = $stmt->fetch();

if (!$teacher) {
    header('Location: ../login.php');
    exit();
}

// Verify that this teacher is assigned to this subject
$stmt = $conn->prepare("
    SELECT COUNT(*) as count
    FROM teacher_subjects ts
    WHERE ts.teacher_id = ? AND ts.subject_id = ? AND ts.is_active = 1
");
$stmt->execute([$teacher['id'], $subject_id]);
$assignment_check = $stmt->fetch();

if ($assignment_check['count'] == 0) {
    header('Location: my-subjects.php');
    exit();
}

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();

// Get subject details
$stmt = $conn->prepare("
    SELECT s.*, l.level_name
    FROM subjects s
    JOIN levels l ON s.level_id = l.id
    WHERE s.id = ?
");
$stmt->execute([$subject_id]);
$subject = $stmt->fetch();

if (!$subject) {
    header('Location: my-subjects.php');
    exit();
}

// Get classes where this teacher teaches this subject
$stmt = $conn->prepare("
    SELECT 
        c.id as class_id,
        c.class_name,
        c.class_type,
        COUNT(DISTINCT st.id) as student_count,
        ts.assigned_date
    FROM teacher_subjects ts
    JOIN classes c ON ts.class_id = c.id
    LEFT JOIN students st ON c.id = st.class_id AND st.current_session_id = ?
    WHERE ts.teacher_id = ? AND ts.subject_id = ? AND ts.is_active = 1
    GROUP BY c.id, c.class_name, c.class_type, ts.assigned_date
    ORDER BY c.class_name
");
$stmt->execute([1, $teacher['id'], $subject_id]); // Using session_id = 1 for now
$classes = $stmt->fetchAll();

// Get total statistics
$total_students = 0;
$total_classes = count($classes);
foreach ($classes as $class) {
    $total_students += $class['student_count'];
}

// Get recent grades statistics for this subject
$stmt = $conn->prepare("
    SELECT 
        COUNT(*) as total_grades,
        AVG(total_score) as average_score,
        COUNT(CASE WHEN grade IN ('A', 'B') THEN 1 END) as excellent_count,
        COUNT(CASE WHEN grade = 'F' THEN 1 END) as failed_count
    FROM student_grades sg
    JOIN teacher_subjects ts ON sg.subject_id = ts.subject_id AND sg.class_id = ts.class_id
    WHERE ts.teacher_id = ? AND sg.subject_id = ? AND sg.session_id = ? AND sg.term_id = ?
");
$stmt->execute([$teacher['id'], $subject_id, 1, 1]); // Using session_id = 1, term_id = 1 for now
$grade_stats = $stmt->fetch();

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($subject['subject_name']); ?> Details - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #ff7b00 0%, #ff9500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }

        .sidebar {
            background: linear-gradient(135deg, #ff7b00 0%, #ff9500 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        /* Custom white scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.8);
        }

        /* For Firefox */
        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.6) rgba(255, 255, 255, 0.2);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 80px;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.25) 100%);
            border-radius: 60px 0 0 60px;
        }

        .sidebar-header {
            padding: 25px 20px;
            text-align: center;
            position: relative;
            background: rgba(255, 255, 255, 0.01);
            border-radius: 0 0 30px 30px;
            margin-bottom: 15px;
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 700;
            font-size: 1.4rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header small {
            opacity: 0.9;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            margin-bottom: 80px;
        }

        .sidebar-menu li {
            margin: 1px 15px;
        }

        .sidebar-menu a {
            color: white;
            text-decoration: none;
            padding: 8px 20px;
            display: block;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 25px;
            position: relative;
            font-weight: 500;
            font-size: 0.85rem;
            background: rgba(255, 255, 255, 0.05);
            border: none;
            margin-bottom: 1px;
        }

        .sidebar-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover::before, .sidebar-menu a.active::before {
            opacity: 1;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            transform: translateX(5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .sidebar-menu a.active {
            background: rgba(255, 255, 255, 0.2);
        }

        .back-to-home {
            position: absolute;
            bottom: 60px;
            left: 15px;
            right: 15px;
            z-index: 1001;
        }

        .back-to-home .btn {
            background: rgba(255, 255, 255, 0.3);
            border: 3px solid rgba(255, 255, 255, 0.6);
            color: white;
            font-weight: 800;
            padding: 12px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            width: 100%;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            font-size: 0.95rem;
        }

        .back-to-home .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
            position: relative;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px 30px;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 25px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .class-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .class-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .class-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }

        .class-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .class-type {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3px 10px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .class-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn-action {
            flex: 1;
            padding: 8px 15px;
            border-radius: 8px;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-action:hover {
            transform: translateY(-2px);
        }

        .subject-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .subject-code {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-chalkboard-teacher me-2"></i>IGSS Teacher</h4>
            <small>Management Portal</small>
        </div>

        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
            <li><a href="my-classes.php"><i class="fas fa-users me-2"></i>My Classes</a></li>
            <li><a href="my-subjects.php" class="active"><i class="fas fa-book me-2"></i>My Subjects</a></li>
            <li><a href="attendance.php"><i class="fas fa-calendar-check me-2"></i>Attendance</a></li>
            <li><a href="grades.php"><i class="fas fa-graduation-cap me-2"></i>Grades</a></li>
            <li><a href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
            <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
        </ul>

        <!-- Back to Home Button -->
        <div class="back-to-home">
            <a href="../index.php" class="btn">
                <i class="fas fa-home me-2"></i>Back to Home
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">
                        <i class="fas fa-book me-2"></i>
                        <?php echo htmlspecialchars($subject['subject_name']); ?>
                    </h4>
                    <small class="text-muted">Subject Details and Statistics</small>
                </div>
                <div>
                    <a href="my-subjects.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Subjects
                    </a>
                </div>
            </div>
        </div>

        <!-- Subject Information -->
        <div class="subject-info">
            <div class="subject-code"><?php echo htmlspecialchars($subject['subject_code']); ?></div>
            <h5 class="mb-2"><?php echo htmlspecialchars($subject['subject_name']); ?></h5>
            <p class="mb-0">
                <i class="fas fa-layer-group me-2"></i><?php echo htmlspecialchars($subject['level_name']); ?> Level
                <?php if ($subject['class_type'] !== 'All'): ?>
                    | <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($subject['class_type']); ?> Stream
                <?php endif; ?>
                <?php if ($subject['is_core']): ?>
                    | <i class="fas fa-star me-1"></i>Core Subject
                <?php endif; ?>
            </p>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $total_classes; ?></div>
                <div class="stat-label">Classes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $total_students; ?></div>
                <div class="stat-label">Total Students</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $grade_stats['total_grades'] ?: 0; ?></div>
                <div class="stat-label">Grades Recorded</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $grade_stats['average_score'] ? number_format($grade_stats['average_score'], 1) : '0.0'; ?>%</div>
                <div class="stat-label">Average Score</div>
            </div>
        </div>

        <!-- Classes List -->
        <div class="content-card">
            <h5 class="mb-4"><i class="fas fa-users me-2"></i>Classes Teaching This Subject</h5>

            <?php if (count($classes) > 0): ?>
                <?php foreach ($classes as $class): ?>
                    <div class="class-card">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div>
                                <h6 class="class-name mb-1"><?php echo htmlspecialchars($class['class_name']); ?></h6>
                                <?php if ($class['class_type'] !== 'General'): ?>
                                    <span class="class-type"><?php echo htmlspecialchars($class['class_type']); ?></span>
                                <?php endif; ?>
                            </div>
                            <div class="text-end">
                                <div class="text-muted small">
                                    <i class="fas fa-users me-1"></i><?php echo $class['student_count']; ?> students
                                </div>
                                <div class="text-muted small">
                                    <i class="fas fa-calendar me-1"></i>Since <?php echo date('M Y', strtotime($class['assigned_date'])); ?>
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <a href="attendance.php?class_id=<?php echo $class['class_id']; ?>&subject_id=<?php echo $subject_id; ?>"
                               class="btn btn-primary btn-action">
                                <i class="fas fa-calendar-check me-1"></i>Attendance
                            </a>
                            <a href="grades.php?class_id=<?php echo $class['class_id']; ?>&subject_id=<?php echo $subject_id; ?>"
                               class="btn btn-success btn-action">
                                <i class="fas fa-graduation-cap me-1"></i>Grades
                            </a>
                            <a href="class-details.php?id=<?php echo $class['class_id']; ?>"
                               class="btn btn-info btn-action">
                                <i class="fas fa-eye me-1"></i>Class Details
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">No classes assigned for this subject</h6>
                    <p class="text-muted">Contact the administrator to assign classes to this subject.</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Performance Overview -->
        <?php if ($grade_stats['total_grades'] > 0): ?>
        <div class="content-card">
            <h5 class="mb-4"><i class="fas fa-chart-bar me-2"></i>Performance Overview</h5>

            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number text-success"><?php echo $grade_stats['excellent_count']; ?></div>
                        <div class="stat-label">Excellent (A-B)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number text-warning"><?php echo $grade_stats['total_grades'] - $grade_stats['excellent_count'] - $grade_stats['failed_count']; ?></div>
                        <div class="stat-label">Average (C-E)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number text-danger"><?php echo $grade_stats['failed_count']; ?></div>
                        <div class="stat-label">Failed (F)</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number text-info"><?php echo number_format(($grade_stats['excellent_count'] / $grade_stats['total_grades']) * 100, 1); ?>%</div>
                        <div class="stat-label">Success Rate</div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="content-card">
            <h5 class="mb-4"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <a href="grades.php?subject_id=<?php echo $subject_id; ?>" class="btn btn-success w-100 py-3">
                        <i class="fas fa-graduation-cap fa-2x mb-2 d-block"></i>
                        <strong>Manage Grades</strong><br>
                        <small>Record and update student grades</small>
                    </a>
                </div>
                <div class="col-md-4 mb-3">
                    <a href="attendance.php?subject_id=<?php echo $subject_id; ?>" class="btn btn-primary w-100 py-3">
                        <i class="fas fa-calendar-check fa-2x mb-2 d-block"></i>
                        <strong>Take Attendance</strong><br>
                        <small>Mark student attendance</small>
                    </a>
                </div>
                <div class="col-md-4 mb-3">
                    <a href="my-subjects.php" class="btn btn-outline-secondary w-100 py-3">
                        <i class="fas fa-book fa-2x mb-2 d-block"></i>
                        <strong>All Subjects</strong><br>
                        <small>View all your subjects</small>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
