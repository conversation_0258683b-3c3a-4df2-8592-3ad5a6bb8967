<?php
/**
 * Direct Admin Password Reset
 * This file allows direct password reset for admin users only
 * Should be deleted after initial setup for security
 */

require_once 'config/config.php';

$message = '';
$error = '';

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $admin_code = $_POST['admin_code'] ?? '';

    // Validate inputs
    if (empty($new_password) || empty($confirm_password) || empty($admin_code)) {
        $error = 'All fields are required.';
    } elseif ($new_password !== $confirm_password) {
        $error = 'Passwords do not match.';
    } elseif (strlen($new_password) < PASSWORD_MIN_LENGTH) {
        $error = 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long.';
    } elseif ($admin_code !== 'ADMIN001') {
        $error = 'Invalid admin code.';
    } else {
        try {
            $conn = getDBConnection();

            // First check if admin user exists
            $stmt = $conn->prepare("SELECT id FROM users WHERE user_code = 'ADMIN001' AND user_type = 'admin'");
            $stmt->execute();
            $adminExists = $stmt->fetchColumn();

            // Hash the new password
            $password_hash = password_hash($new_password, PASSWORD_DEFAULT);

            if (!$adminExists) {
                // Create admin user if it doesn't exist
                $stmt = $conn->prepare("
                    INSERT INTO users (user_code, first_name, last_name, email, gender, user_type, password_hash, is_approved, is_active, created_at, updated_at)
                    VALUES ('ADMIN001', 'System', 'Administrator', '<EMAIL>', 'Male', 'admin', ?, 1, 1, NOW(), NOW())
                ");
                $result = $stmt->execute([$password_hash]);
                $action = 'created';
            } else {
                // Update existing admin user
                $stmt = $conn->prepare("
                    UPDATE users
                    SET password_hash = ?, is_approved = 1, is_active = 1, updated_at = NOW()
                    WHERE user_code = 'ADMIN001' AND user_type = 'admin'
                ");
                $result = $stmt->execute([$password_hash]);
                $action = 'updated';
            }

            if ($result) {
                // Verify the operation worked
                $stmt = $conn->prepare("SELECT id FROM users WHERE user_code = 'ADMIN001' AND user_type = 'admin'");
                $stmt->execute();
                $verifyUser = $stmt->fetchColumn();

                if ($verifyUser) {
                    // Clear any setup session variables
                    session_start();
                    unset($_SESSION['setup_required']);
                    unset($_SESSION['temp_admin_password']);

                    $message = "Admin password has been successfully {$action}. You can now log in with User Code: ADMIN001 and your new password. Please delete this file for security.";
                } else {
                    $error = 'Operation completed but verification failed. Please try again.';
                }
            } else {
                $error = 'Failed to update password. Database operation failed.';
            }
        } catch (Exception $e) {
            $error = 'Database error: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Password Reset - IGSS Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .reset-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
        }

        .reset-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .reset-header h2 {
            color: #333;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .reset-header p {
            color: #666;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }

        .form-control {
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #ff9a56;
            box-shadow: 0 0 0 0.2rem rgba(255, 154, 86, 0.25);
        }

        /* Password Toggle */
        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
            transition: color 0.3s ease;
            z-index: 10;
        }

        .password-toggle:hover {
            color: #ff6b35;
        }

        .btn-reset {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
            border: none;
            border-radius: 8px;
            padding: 12px 30px;
            font-weight: 500;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 154, 86, 0.4);
        }

        .alert {
            border-radius: 8px;
            border: none;
            padding: 15px;
            margin-bottom: 20px;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
        }

        .security-warning {
            background-color: #fff3cd;
            color: #856404;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #ffc107;
        }

        .security-warning i {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-header">
            <h2><i class="fas fa-shield-alt text-primary"></i> Admin Password Reset</h2>
            <p>Reset the administrator password for IGSS Management System</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="">
            <div class="form-group">
                <label for="admin_code" class="form-label">
                    <i class="fas fa-key"></i> Admin Code
                </label>
                <input type="text"
                       class="form-control"
                       id="admin_code"
                       name="admin_code"
                       placeholder="Enter admin code (ADMIN001)"
                       required>
            </div>

            <div class="form-group">
                <label for="new_password" class="form-label">
                    <i class="fas fa-lock"></i> New Password
                </label>
                <div class="password-container">
                    <input type="password"
                           class="form-control"
                           id="new_password"
                           name="new_password"
                           placeholder="Enter new password"
                           minlength="<?php echo PASSWORD_MIN_LENGTH; ?>"
                           required>
                    <span class="password-toggle" onclick="togglePassword('new_password')">
                        <i class="fas fa-eye" id="new_password-toggle-icon"></i>
                    </span>
                </div>
            </div>

            <div class="form-group">
                <label for="confirm_password" class="form-label">
                    <i class="fas fa-lock"></i> Confirm Password
                </label>
                <div class="password-container">
                    <input type="password"
                           class="form-control"
                           id="confirm_password"
                           name="confirm_password"
                           placeholder="Confirm new password"
                           minlength="<?php echo PASSWORD_MIN_LENGTH; ?>"
                           required>
                    <span class="password-toggle" onclick="togglePassword('confirm_password')">
                        <i class="fas fa-eye" id="confirm_password-toggle-icon"></i>
                    </span>
                </div>
            </div>

            <button type="submit" class="btn btn-primary btn-reset">
                <i class="fas fa-sync-alt"></i> Reset Password
            </button>
        </form>

        <div class="security-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Security Warning:</strong> Delete this file (direct-reset.php) immediately after resetting the password to prevent unauthorized access.
        </div>

        <div class="text-center mt-3">
            <a href="login.php" class="text-decoration-none">
                <i class="fas fa-arrow-left"></i> Back to Login
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password toggle function
        function togglePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const toggleIcon = document.getElementById(fieldId + '-toggle-icon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('new_password').value;
            const confirmPassword = this.value;

            if (password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
