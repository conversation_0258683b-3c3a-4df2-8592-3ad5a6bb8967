<?php
/**
 * AJAX handler for editing fee types
 * Professional fee management system
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !hasRole('admin')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

header('Content-Type: application/json');

try {
    $conn = getDBConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['id'])) {
        // Get fee type details for editing
        $feeId = intval($_GET['id']);
        
        $stmt = $conn->prepare("
            SELECT ft.*, 
                   CONCAT(u.first_name, ' ', u.last_name) as created_by_name
            FROM fee_types ft
            LEFT JOIN users u ON ft.created_by = u.id
            WHERE ft.id = ? AND ft.is_active = 1
        ");
        $stmt->execute([$feeId]);
        $feeType = $stmt->fetch();
        
        if (!$feeType) {
            echo json_encode(['success' => false, 'message' => 'Fee type not found']);
            exit();
        }
        
        echo json_encode(['success' => true, 'data' => $feeType]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Update fee type
        $feeId = intval($_POST['fee_id']);
        $feeName = sanitizeInput($_POST['fee_name']);
        $feeAmount = floatval($_POST['fee_amount']);
        $feeDescription = sanitizeInput($_POST['fee_description'] ?? '');
        $isExamFee = isset($_POST['is_exam_fee']) ? 1 : 0;
        $targetClass = $_POST['target_class'] ?? '';
        $paymentTerm = $_POST['payment_term'] ?? '';
        
        // Validation
        if (empty($feeName) || $feeAmount <= 0) {
            echo json_encode(['success' => false, 'message' => 'Fee name and amount are required']);
            exit();
        }
        
        if ($isExamFee && (empty($targetClass) || empty($paymentTerm))) {
            echo json_encode(['success' => false, 'message' => 'Target class and payment term are required for exam fees']);
            exit();
        }
        
        // Check if fee name already exists (excluding current fee)
        $stmt = $conn->prepare("
            SELECT id FROM fee_types 
            WHERE fee_name = ? AND id != ? AND is_active = 1
        ");
        $stmt->execute([$feeName, $feeId]);
        if ($stmt->fetch()) {
            echo json_encode(['success' => false, 'message' => 'Fee name already exists']);
            exit();
        }
        
        // Update fee type
        $stmt = $conn->prepare("
            UPDATE fee_types 
            SET fee_name = ?, 
                amount_js = ?, 
                amount_ss = ?, 
                fee_description = ?, 
                is_exam_fee = ?, 
                target_class = ?, 
                payment_term = ?,
                updated_at = NOW()
            WHERE id = ?
        ");
        
        $result = $stmt->execute([
            $feeName, 
            $feeAmount, 
            $feeAmount, 
            $feeDescription, 
            $isExamFee, 
            $targetClass, 
            $paymentTerm, 
            $feeId
        ]);
        
        if ($result) {
            // Log the activity
            $stmt = $conn->prepare("
                INSERT INTO activity_logs (user_id, activity_type, description, created_at)
                VALUES (?, 'fee_update', ?, NOW())
            ");
            $stmt->execute([
                $_SESSION['user_id'],
                "Updated fee type: {$feeName}"
            ]);
            
            echo json_encode(['success' => true, 'message' => 'Fee type updated successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update fee type']);
        }
        
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    }
    
} catch (Exception $e) {
    error_log("Edit fee type error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while processing your request']);
}
?>
