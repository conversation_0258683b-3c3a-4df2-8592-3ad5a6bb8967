<?php
/**
 * Fee Payment Notification Functions
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once 'messaging-functions.php';

/**
 * Send payment confirmation notification to parent, student, and form teacher
 */
function sendPaymentConfirmationNotification($payment_id, $action, $admin_remarks = '') {
    try {
        $conn = getDBConnection();

        // Get payment details with student and parent information
        $stmt = $conn->prepare("
            SELECT
                fp.*,
                s.id as student_id,
                s.admission_number,
                su.first_name as student_first_name,
                su.last_name as student_last_name,
                su.id as student_user_id,
                c.class_name,
                l.level_name,
                -- Get parent information
                pu.id as parent_user_id,
                pu.first_name as parent_first_name,
                pu.last_name as parent_last_name,
                pu.email as parent_email,
                -- Get form teacher information
                tu.id as teacher_user_id,
                tu.first_name as teacher_first_name,
                tu.last_name as teacher_last_name
            FROM fee_payments fp
            JOIN students s ON fp.student_id = s.id
            JOIN users su ON s.user_id = su.id
            JOIN classes c ON s.class_id = c.id
            JOIN levels l ON c.level_id = l.id
            -- Get parent through relationship
            LEFT JOIN parent_student_relationships psr ON s.id = psr.student_id AND psr.is_primary = 1
            LEFT JOIN parents p ON psr.parent_id = p.id
            LEFT JOIN users pu ON p.user_id = pu.id
            -- Get form teacher
            LEFT JOIN class_assignments ca ON c.id = ca.class_id AND ca.is_active = 1
            LEFT JOIN teachers t ON ca.teacher_id = t.id
            LEFT JOIN users tu ON t.user_id = tu.id
            WHERE fp.id = ?
        ");
        $stmt->execute([$payment_id]);
        $payment_info = $stmt->fetch();

        if (!$payment_info) {
            return false;
        }

        $school_info = getSchoolInfo();

        // Prepare notification content based on action
        $subject = '';
        $message_content = '';
        $message_type = 'fee_inquiry';
        $priority = 'high';

        if ($action === 'confirmed') {
            $subject = "Payment Confirmed - {$payment_info['student_first_name']} {$payment_info['student_last_name']}";
            $message_content = generatePaymentConfirmedMessage($payment_info, $school_info, $admin_remarks);
        } elseif ($action === 'rejected') {
            $subject = "Payment Rejected - {$payment_info['student_first_name']} {$payment_info['student_last_name']}";
            $message_content = generatePaymentRejectedMessage($payment_info, $school_info, $admin_remarks);
        } else {
            return false;
        }

        $notifications_sent = [];

        // Send notification to parent
        if ($payment_info['parent_user_id']) {
            $result = sendMessage(
                1, // Admin sender
                'admin',
                $subject,
                $message_content,
                $message_type,
                $priority,
                $payment_info['student_id'],
                'parent',
                $payment_info['parent_user_id']
            );
            $notifications_sent['parent'] = $result['success'];
        }

        // Send notification to student
        if ($payment_info['student_user_id']) {
            $result = sendMessage(
                1, // Admin sender
                'admin',
                $subject,
                $message_content,
                $message_type,
                $priority,
                $payment_info['student_id'],
                'student',
                $payment_info['student_user_id']
            );
            $notifications_sent['student'] = $result['success'];
        }

        // Send notification to form teacher
        if ($payment_info['teacher_user_id']) {
            $teacher_subject = "Student Payment Update - {$payment_info['student_first_name']} {$payment_info['student_last_name']}";
            $teacher_message = generateTeacherPaymentNotification($payment_info, $school_info, $action, $admin_remarks);

            $result = sendMessage(
                1, // Admin sender
                'admin',
                $teacher_subject,
                $teacher_message,
                $message_type,
                'normal',
                $payment_info['student_id'],
                'teacher',
                $payment_info['teacher_user_id']
            );
            $notifications_sent['teacher'] = $result['success'];
        }

        // Log the notification attempt
        $stmt = $conn->prepare("
            INSERT INTO fee_payment_notifications (payment_id, notification_type, action, recipients_notified, created_at)
            VALUES (?, 'system', ?, ?, NOW())
        ");
        $stmt->execute([$payment_id, $action, json_encode($notifications_sent)]);

        return $notifications_sent;

    } catch (Exception $e) {
        error_log("Failed to send payment notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate payment confirmed message content
 */
function generatePaymentConfirmedMessage($payment_info, $school_info, $admin_remarks) {
    $message = "
    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px;'>
        <div style='text-align: center; margin-bottom: 30px;'>
            <h2 style='color: #28a745; margin-bottom: 10px;'>✅ Payment Confirmed</h2>
            <h3 style='color: #333; margin: 0;'>{$school_info['name']}</h3>
        </div>

        <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>
            <h4 style='color: #333; margin-top: 0;'>Payment Details</h4>
            <table style='width: 100%; border-collapse: collapse;'>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Student:</td>
                    <td style='padding: 8px 0;'>{$payment_info['student_first_name']} {$payment_info['student_last_name']}</td>
                </tr>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Admission Number:</td>
                    <td style='padding: 8px 0;'>{$payment_info['admission_number']}</td>
                </tr>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Class:</td>
                    <td style='padding: 8px 0;'>{$payment_info['class_name']} - {$payment_info['level_name']}</td>
                </tr>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Amount:</td>
                    <td style='padding: 8px 0; color: #28a745; font-weight: bold;'>₦" . number_format($payment_info['amount_paid'], 2) . "</td>
                </tr>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Payment Method:</td>
                    <td style='padding: 8px 0;'>{$payment_info['payment_method']}</td>
                </tr>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Payment Date:</td>
                    <td style='padding: 8px 0;'>" . date('F j, Y', strtotime($payment_info['payment_date'])) . "</td>
                </tr>
            </table>
        </div>

        <div style='background: #d4edda; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; margin-bottom: 20px;'>
            <p style='margin: 0; color: #155724;'>
                <strong>✅ Your payment has been successfully confirmed and processed.</strong>
            </p>
        </div>";

    if ($admin_remarks) {
        $message .= "
        <div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin-bottom: 20px;'>
            <h5 style='color: #333; margin-top: 0;'>Admin Notes:</h5>
            <p style='margin: 0; color: #6c757d;'>" . htmlspecialchars($admin_remarks) . "</p>
        </div>";
    }

    $message .= "
        <div style='text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;'>
            <p style='color: #6c757d; margin: 0;'>
                Thank you for your prompt payment. If you have any questions, please contact the school administration.
            </p>
            <p style='color: #6c757d; margin: 10px 0 0 0; font-size: 14px;'>
                {$school_info['name']} | {$school_info['email']} | {$school_info['phone']}
            </p>
        </div>
    </div>";

    return $message;
}

/**
 * Generate payment rejected message content
 */
function generatePaymentRejectedMessage($payment_info, $school_info, $admin_remarks) {
    $message = "
    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px;'>
        <div style='text-align: center; margin-bottom: 30px;'>
            <h2 style='color: #dc3545; margin-bottom: 10px;'>❌ Payment Rejected</h2>
            <h3 style='color: #333; margin: 0;'>{$school_info['name']}</h3>
        </div>

        <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>
            <h4 style='color: #333; margin-top: 0;'>Payment Details</h4>
            <table style='width: 100%; border-collapse: collapse;'>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Student:</td>
                    <td style='padding: 8px 0;'>{$payment_info['student_first_name']} {$payment_info['student_last_name']}</td>
                </tr>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Admission Number:</td>
                    <td style='padding: 8px 0;'>{$payment_info['admission_number']}</td>
                </tr>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Class:</td>
                    <td style='padding: 8px 0;'>{$payment_info['class_name']} - {$payment_info['level_name']}</td>
                </tr>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Amount:</td>
                    <td style='padding: 8px 0; color: #dc3545; font-weight: bold;'>₦" . number_format($payment_info['amount_paid'], 2) . "</td>
                </tr>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Payment Method:</td>
                    <td style='padding: 8px 0;'>{$payment_info['payment_method']}</td>
                </tr>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Payment Date:</td>
                    <td style='padding: 8px 0;'>" . date('F j, Y', strtotime($payment_info['payment_date'])) . "</td>
                </tr>
            </table>
        </div>

        <div style='background: #f8d7da; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545; margin-bottom: 20px;'>
            <p style='margin: 0; color: #721c24;'>
                <strong>❌ Your payment has been rejected and requires attention.</strong>
            </p>
        </div>";

    if ($admin_remarks) {
        $message .= "
        <div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #ffc107;'>
            <h5 style='color: #856404; margin-top: 0;'>Reason for Rejection:</h5>
            <p style='margin: 0; color: #856404;'>" . htmlspecialchars($admin_remarks) . "</p>
        </div>";
    }

    $message .= "
        <div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin-bottom: 20px;'>
            <h5 style='color: #333; margin-top: 0;'>Next Steps:</h5>
            <ul style='margin: 0; color: #6c757d;'>
                <li>Review the rejection reason above</li>
                <li>Correct any issues with your payment documentation</li>
                <li>Resubmit your payment with the correct information</li>
                <li>Contact the school administration if you need assistance</li>
            </ul>
        </div>

        <div style='text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;'>
            <p style='color: #6c757d; margin: 0;'>
                Please contact the school administration for assistance with your payment.
            </p>
            <p style='color: #6c757d; margin: 10px 0 0 0; font-size: 14px;'>
                {$school_info['name']} | {$school_info['email']} | {$school_info['phone']}
            </p>
        </div>
    </div>";

    return $message;
}

/**
 * Generate teacher payment notification message
 */
function generateTeacherPaymentNotification($payment_info, $school_info, $action, $admin_remarks) {
    $status_color = $action === 'confirmed' ? '#28a745' : '#dc3545';
    $status_text = $action === 'confirmed' ? 'CONFIRMED' : 'REJECTED';
    $status_icon = $action === 'confirmed' ? '✅' : '❌';

    $message = "
    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px;'>
        <div style='text-align: center; margin-bottom: 30px;'>
            <h2 style='color: {$status_color}; margin-bottom: 10px;'>{$status_icon} Student Payment {$status_text}</h2>
            <h3 style='color: #333; margin: 0;'>{$school_info['name']}</h3>
            <p style='color: #6c757d; margin: 5px 0 0 0;'>Form Teacher Notification</p>
        </div>

        <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>
            <h4 style='color: #333; margin-top: 0;'>Student Information</h4>
            <table style='width: 100%; border-collapse: collapse;'>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Student:</td>
                    <td style='padding: 8px 0;'>{$payment_info['student_first_name']} {$payment_info['student_last_name']}</td>
                </tr>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Admission Number:</td>
                    <td style='padding: 8px 0;'>{$payment_info['admission_number']}</td>
                </tr>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Class:</td>
                    <td style='padding: 8px 0;'>{$payment_info['class_name']} - {$payment_info['level_name']}</td>
                </tr>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Amount:</td>
                    <td style='padding: 8px 0; color: {$status_color}; font-weight: bold;'>₦" . number_format($payment_info['amount_paid'], 2) . "</td>
                </tr>
                <tr>
                    <td style='padding: 8px 0; font-weight: bold;'>Payment Status:</td>
                    <td style='padding: 8px 0; color: {$status_color}; font-weight: bold;'>{$status_text}</td>
                </tr>
            </table>
        </div>";

    if ($admin_remarks) {
        $message .= "
        <div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin-bottom: 20px;'>
            <h5 style='color: #333; margin-top: 0;'>Admin Notes:</h5>
            <p style='margin: 0; color: #6c757d;'>" . htmlspecialchars($admin_remarks) . "</p>
        </div>";
    }

    $message .= "
        <div style='background: #cce7ff; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; margin-bottom: 20px;'>
            <p style='margin: 0; color: #004085;'>
                <strong>📋 As the form teacher, you have been notified of this payment status update for your student.</strong>
            </p>
        </div>

        <div style='text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;'>
            <p style='color: #6c757d; margin: 0;'>
                This is an automated notification from the school administration system.
            </p>
            <p style='color: #6c757d; margin: 10px 0 0 0; font-size: 14px;'>
                {$school_info['name']} | {$school_info['email']} | {$school_info['phone']}
            </p>
        </div>
    </div>";

    return $message;
}
?>