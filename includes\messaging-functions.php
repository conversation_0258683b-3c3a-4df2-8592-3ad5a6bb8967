<?php
/**
 * Messaging System Functions
 * Intellectual Giant Secondary School Electronic Management System
 */

/**
 * Send a message with proper routing
 */
function sendMessage($sender_id, $sender_type, $subject, $message_content, $message_type = 'general', $priority = 'normal', $ward_id = null, $recipient_type = 'admin', $specific_recipient_id = null) {
    $conn = getDBConnection();
    
    try {
        $conn->beginTransaction();
        
        // Insert main message
        $stmt = $conn->prepare("
            INSERT INTO messages (sender_id, sender_type, subject, message_content, message_type, priority, ward_id)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$sender_id, $sender_type, $subject, $message_content, $message_type, $priority, $ward_id]);
        $message_id = $conn->lastInsertId();
        
        // Determine recipients based on message type and ward
        $recipients = determineMessageRecipients($recipient_type, $ward_id, $specific_recipient_id);
        
        // Insert recipients
        foreach ($recipients as $recipient) {
            $stmt = $conn->prepare("
                INSERT INTO message_recipients (message_id, recipient_id, recipient_type)
                VALUES (?, ?, ?)
            ");
            $stmt->execute([$message_id, $recipient['id'], $recipient['type']]);
            
            // Create notification
            createMessageNotification($message_id, $recipient['id'], 'in_app');
            
            // Send email notification if enabled
            if ($priority === 'urgent' || $priority === 'high') {
                createMessageNotification($message_id, $recipient['id'], 'email');
            }
        }
        
        $conn->commit();
        return ['success' => true, 'message_id' => $message_id];
        
    } catch (Exception $e) {
        $conn->rollBack();
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Determine message recipients based on type and ward
 */
function determineMessageRecipients($recipient_type, $ward_id = null, $specific_recipient_id = null) {
    $conn = getDBConnection();
    $recipients = [];
    
    if ($specific_recipient_id) {
        // Send to specific recipient
        $stmt = $conn->prepare("SELECT id, user_type as type FROM users WHERE id = ? AND is_active = 1");
        $stmt->execute([$specific_recipient_id]);
        $recipient = $stmt->fetch();
        if ($recipient) {
            $recipients[] = $recipient;
        }
    } else {
        switch ($recipient_type) {
            case 'form_teacher':
                if ($ward_id) {
                    // Get form teacher for the ward's class
                    $stmt = $conn->prepare("
                        SELECT u.id, 'teacher' as type
                        FROM students s
                        JOIN form_teachers ft ON s.class_id = ft.class_id
                        JOIN users u ON ft.teacher_id = u.id
                        WHERE s.id = ? AND ft.is_active = 1 AND u.is_active = 1
                    ");
                    $stmt->execute([$ward_id]);
                    $recipient = $stmt->fetch();
                    if ($recipient) {
                        $recipients[] = $recipient;
                    }
                }
                break;
                
            case 'class_teacher':
                if ($ward_id) {
                    // Get all teachers teaching the ward's class
                    $stmt = $conn->prepare("
                        SELECT DISTINCT u.id, 'teacher' as type
                        FROM students s
                        JOIN subject_assignments sa ON s.class_id = sa.class_id
                        JOIN users u ON sa.teacher_id = u.id
                        WHERE s.id = ? AND sa.is_active = 1 AND u.is_active = 1
                    ");
                    $stmt->execute([$ward_id]);
                    $recipients = $stmt->fetchAll();
                }
                break;
                
            case 'admin':
                // Get all active admin users
                $stmt = $conn->prepare("
                    SELECT id, 'admin' as type 
                    FROM users 
                    WHERE user_type = 'admin' AND is_active = 1
                ");
                $stmt->execute();
                $recipients = $stmt->fetchAll();
                break;
                
            case 'principal':
                // Get principal (admin with highest privileges)
                $stmt = $conn->prepare("
                    SELECT id, 'admin' as type 
                    FROM users 
                    WHERE user_type = 'admin' AND is_active = 1 
                    ORDER BY created_at ASC 
                    LIMIT 1
                ");
                $stmt->execute();
                $recipient = $stmt->fetch();
                if ($recipient) {
                    $recipients[] = $recipient;
                }
                break;
        }
    }
    
    return $recipients;
}

/**
 * Create message notification
 */
function createMessageNotification($message_id, $recipient_id, $notification_type) {
    $conn = getDBConnection();
    
    $stmt = $conn->prepare("
        INSERT INTO message_notifications (message_id, recipient_id, notification_type)
        VALUES (?, ?, ?)
    ");
    $stmt->execute([$message_id, $recipient_id, $notification_type]);
}

/**
 * Get messages for a user (inbox)
 */
function getUserMessages($user_id, $user_type, $limit = 20, $offset = 0) {
    $conn = getDBConnection();
    
    $stmt = $conn->prepare("
        SELECT 
            m.*,
            mr.is_read,
            mr.read_at,
            mr.is_replied,
            mr.replied_at,
            sender.first_name as sender_first_name,
            sender.last_name as sender_last_name,
            sender.user_type as sender_user_type,
            ward.first_name as ward_first_name,
            ward.last_name as ward_last_name,
            ward.admission_number
        FROM messages m
        JOIN message_recipients mr ON m.id = mr.message_id
        JOIN users sender ON m.sender_id = sender.id
        LEFT JOIN students s ON m.ward_id = s.id
        LEFT JOIN users ward ON s.user_id = ward.id
        WHERE mr.recipient_id = ? AND mr.recipient_type = ?
        ORDER BY m.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$user_id, $user_type, $limit, $offset]);
    return $stmt->fetchAll();
}

/**
 * Mark message as read
 */
function markMessageAsRead($message_id, $recipient_id) {
    $conn = getDBConnection();
    
    $stmt = $conn->prepare("
        UPDATE message_recipients 
        SET is_read = 1, read_at = NOW() 
        WHERE message_id = ? AND recipient_id = ?
    ");
    $stmt->execute([$message_id, $recipient_id]);
}

/**
 * Get unread message count for user
 */
function getUnreadMessageCount($user_id, $user_type) {
    $conn = getDBConnection();
    
    $stmt = $conn->prepare("
        SELECT COUNT(*) 
        FROM message_recipients 
        WHERE recipient_id = ? AND recipient_type = ? AND is_read = 0
    ");
    $stmt->execute([$user_id, $user_type]);
    return $stmt->fetchColumn();
}

/**
 * Reply to a message
 */
function replyToMessage($original_message_id, $sender_id, $sender_type, $reply_content) {
    $conn = getDBConnection();
    
    try {
        // Get original message details
        $stmt = $conn->prepare("SELECT * FROM messages WHERE id = ?");
        $stmt->execute([$original_message_id]);
        $original_message = $stmt->fetch();
        
        if (!$original_message) {
            return ['success' => false, 'error' => 'Original message not found'];
        }
        
        // Create reply subject
        $reply_subject = 'Re: ' . $original_message['subject'];
        
        // Send reply back to original sender
        return sendMessage(
            $sender_id, 
            $sender_type, 
            $reply_subject, 
            $reply_content, 
            $original_message['message_type'], 
            'normal', 
            $original_message['ward_id'], 
            $original_message['sender_type'], 
            $original_message['sender_id']
        );
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Get message templates
 */
function getMessageTemplates($template_type = null) {
    $conn = getDBConnection();
    
    $sql = "SELECT * FROM message_templates WHERE is_active = 1";
    $params = [];
    
    if ($template_type) {
        $sql .= " AND template_type = ?";
        $params[] = $template_type;
    }
    
    $sql .= " ORDER BY template_name";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

/**
 * Get form teacher for a ward
 */
function getFormTeacherForWard($ward_id) {
    $conn = getDBConnection();
    
    $stmt = $conn->prepare("
        SELECT 
            u.id,
            u.first_name,
            u.last_name,
            u.email,
            t.employee_id
        FROM students s
        JOIN form_teachers ft ON s.class_id = ft.class_id
        JOIN users u ON ft.teacher_id = u.id
        JOIN teachers t ON u.id = t.user_id
        WHERE s.id = ? AND ft.is_active = 1 AND u.is_active = 1
    ");
    $stmt->execute([$ward_id]);
    return $stmt->fetch();
}
