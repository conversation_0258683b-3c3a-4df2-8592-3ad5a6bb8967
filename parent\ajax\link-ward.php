<?php
/**
 * AJAX Ward Linking
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';

header('Content-Type: application/json');

// Check if user is logged in and is a parent
if (!isLoggedIn() || $_SESSION['user_type'] !== 'parent') {
    echo json_encode(['success' => false, 'error' => 'Unauthorized access']);
    exit();
}

// Check if parent account is approved
if (!$_SESSION['is_approved']) {
    echo json_encode(['success' => false, 'error' => 'Account not approved']);
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get parent information
$stmt = $conn->prepare("SELECT id FROM parents WHERE user_id = ?");
$stmt->execute([$user_id]);
$parent = $stmt->fetch();

if (!$parent) {
    echo json_encode(['success' => false, 'error' => 'Parent record not found']);
    exit();
}

// Get form data
$student_id = intval($_POST['student_id'] ?? 0);
$relationship_type = trim($_POST['relationship_type'] ?? '');
$is_primary = isset($_POST['is_primary']) && $_POST['is_primary'] === 'true';

// Validate input
if (empty($student_id) || empty($relationship_type)) {
    echo json_encode(['success' => false, 'error' => 'Student ID and relationship type are required']);
    exit();
}

// Validate relationship type
$valid_relationships = ['Father', 'Mother', 'Guardian', 'Uncle', 'Aunt', 'Grandparent', 'Sibling', 'Other'];
if (!in_array($relationship_type, $valid_relationships)) {
    echo json_encode(['success' => false, 'error' => 'Invalid relationship type']);
    exit();
}

try {
    $conn->beginTransaction();

    // Verify student exists and is active
    $stmt = $conn->prepare("
        SELECT s.id, u.first_name, u.last_name, s.admission_number
        FROM students s
        JOIN users u ON s.user_id = u.id
        WHERE s.id = ? AND u.is_active = 1
    ");
    $stmt->execute([$student_id]);
    $student = $stmt->fetch();

    if (!$student) {
        throw new Exception('Student not found or inactive');
    }

    // Check if already linked
    $stmt = $conn->prepare("
        SELECT id FROM parent_student_relationships 
        WHERE parent_id = ? AND student_id = ?
    ");
    $stmt->execute([$parent['id'], $student_id]);
    $existing_link = $stmt->fetch();

    if ($existing_link) {
        throw new Exception('Student is already linked to your account');
    }

    // If setting as primary, remove primary status from other wards
    if ($is_primary) {
        $stmt = $conn->prepare("
            UPDATE parent_student_relationships 
            SET is_primary = 0 
            WHERE parent_id = ?
        ");
        $stmt->execute([$parent['id']]);
    }

    // If this is the first ward, automatically set as primary
    $stmt = $conn->prepare("
        SELECT COUNT(*) FROM parent_student_relationships WHERE parent_id = ?
    ");
    $stmt->execute([$parent['id']]);
    $existing_wards_count = $stmt->fetchColumn();

    if ($existing_wards_count == 0) {
        $is_primary = true;
    }

    // Create the parent-student relationship
    $stmt = $conn->prepare("
        INSERT INTO parent_student_relationships (parent_id, student_id, relationship_type, is_primary)
        VALUES (?, ?, ?, ?)
    ");
    $stmt->execute([$parent['id'], $student_id, $relationship_type, $is_primary ? 1 : 0]);

    $conn->commit();

    // Get updated ward information for response
    $stmt = $conn->prepare("
        SELECT 
            s.id as student_id,
            s.admission_number,
            u.first_name,
            u.last_name,
            u.middle_name,
            c.class_name,
            l.level_name,
            psr.relationship_type,
            psr.is_primary
        FROM parent_student_relationships psr
        JOIN students s ON psr.student_id = s.id
        JOIN users u ON s.user_id = u.id
        JOIN classes c ON s.class_id = c.id
        JOIN levels l ON c.level_id = l.id
        WHERE psr.parent_id = ? AND psr.student_id = ?
    ");
    $stmt->execute([$parent['id'], $student_id]);
    $linked_ward = $stmt->fetch();

    echo json_encode([
        'success' => true,
        'message' => 'Ward linked successfully!',
        'ward' => [
            'student_id' => $linked_ward['student_id'],
            'admission_number' => $linked_ward['admission_number'],
            'full_name' => trim($linked_ward['first_name'] . ' ' . ($linked_ward['middle_name'] ? $linked_ward['middle_name'] . ' ' : '') . $linked_ward['last_name']),
            'class_name' => $linked_ward['class_name'],
            'level_name' => $linked_ward['level_name'],
            'relationship_type' => $linked_ward['relationship_type'],
            'is_primary' => (bool)$linked_ward['is_primary']
        ]
    ]);

} catch (Exception $e) {
    $conn->rollBack();
    error_log("Ward linking error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
