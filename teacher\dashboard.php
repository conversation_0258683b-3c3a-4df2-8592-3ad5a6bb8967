<?php
/**
 * Teacher Dashboard for IGSS Management System
 * Intellectual Giant Secondary School Electronic Management System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is a teacher
if (!isLoggedIn() || $_SESSION['user_type'] !== 'teacher') {
    header('Location: ../login.php');
    exit();
}

$conn = getDBConnection();
$user_id = $_SESSION['user_id'];

// Get teacher information
$stmt = $conn->prepare("
    SELECT t.*, u.first_name, u.last_name, u.middle_name, u.email, u.phone, u.user_code
    FROM teachers t
    JOIN users u ON t.user_id = u.id
    WHERE t.user_id = ?
");
$stmt->execute([$user_id]);
$teacher = $stmt->fetch();

if (!$teacher) {
    header('Location: ../login.php');
    exit();
}

// Get current academic session and term
$current_academic = getCurrentAcademicInfo();

// Get teacher statistics
$stats = [];

// Get assigned subjects count
$stmt = $conn->prepare("
    SELECT COUNT(DISTINCT sa.subject_id) as subject_count
    FROM subject_assignments sa
    WHERE sa.teacher_id = ? AND sa.is_active = 1
");
$stmt->execute([$teacher['id']]);
$stats['subjects'] = $stmt->fetchColumn() ?: 0;

// Get assigned classes count
$stmt = $conn->prepare("
    SELECT COUNT(DISTINCT sa.class_id) as class_count
    FROM subject_assignments sa
    WHERE sa.teacher_id = ? AND sa.is_active = 1
");
$stmt->execute([$teacher['id']]);
$stats['classes'] = $stmt->fetchColumn() ?: 0;

// Get total students count
$stmt = $conn->prepare("
    SELECT COUNT(DISTINCT s.id) as student_count
    FROM students s
    JOIN subject_assignments sa ON s.class_id = sa.class_id
    WHERE sa.teacher_id = ? AND sa.is_active = 1
");
$stmt->execute([$teacher['id']]);
$stats['students'] = $stmt->fetchColumn() ?: 0;

// Check if form teacher
$stmt = $conn->prepare("
    SELECT c.class_name, c.id as class_id
    FROM form_teachers ft
    JOIN classes c ON ft.class_id = c.id
    WHERE ft.teacher_id = ? AND ft.is_active = 1
    LIMIT 1
");
$stmt->execute([$teacher['id']]);
$form_class = $stmt->fetch();
$stats['is_form_teacher'] = $form_class ? true : false;
$stats['form_class'] = $form_class ? $form_class['class_name'] : null;

// Get recent activities (placeholder for now)
$recent_activities = [];

$school_info = getSchoolInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teacher Dashboard - <?php echo $school_info['name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        body {
            background: linear-gradient(135deg, #ff7b00 0%, #ff9500 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"/><stop offset="100%" style="stop-color:rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23a)"/><circle cx="800" cy="300" r="100" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            pointer-events: none;
            z-index: -1;
        }

        .sidebar {
            background: linear-gradient(135deg, #ff7b00 0%, #ff9500 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        /* Custom white scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.8);
        }

        /* For Firefox */
        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.6) rgba(255, 255, 255, 0.2);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 80px;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.25) 100%);
            border-radius: 60px 0 0 60px;
        }

        .sidebar-header {
            padding: 25px 20px;
            text-align: center;
            position: relative;
            background: rgba(255, 255, 255, 0.01);
            border-radius: 0 0 30px 30px;
            margin-bottom: 15px;
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 700;
            font-size: 1.4rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header small {
            opacity: 0.9;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            margin-bottom: 80px;
        }

        .sidebar-menu li {
            margin: 1px 15px;
        }

        .sidebar-menu a {
            color: white;
            text-decoration: none;
            padding: 8px 20px;
            display: block;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 25px;
            position: relative;
            font-weight: 500;
            font-size: 0.85rem;
            background: rgba(255, 255, 255, 0.05);
            border: none;
            margin-bottom: 1px;
        }

        .sidebar-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover::before, .sidebar-menu a.active::before {
            opacity: 1;
        }

        .sidebar-menu a:hover, .sidebar-menu a.active {
            transform: translateX(5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .sidebar-menu a.active {
            background: rgba(255, 255, 255, 0.2);
        }

        .back-to-home {
            position: absolute;
            bottom: 60px;
            left: 15px;
            right: 15px;
            z-index: 1001;
        }

        .back-to-home .btn {
            background: rgba(255, 255, 255, 0.3);
            border: 3px solid rgba(255, 255, 255, 0.6);
            color: white;
            font-weight: 800;
            padding: 12px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            width: 100%;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            font-size: 0.95rem;
        }

        .back-to-home .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
            position: relative;
        }

        .top-navbar {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 30px;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .top-navbar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff7b00, #ff9500);
        }

        .stats-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff7b00, #ff9500);
        }

        .stats-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            cursor: pointer;
        }

        .stats-card.clickable {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .stats-card.clickable:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .stats-card.clickable:active {
            transform: translateY(-4px) scale(1.01);
        }

        .stats-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin-bottom: 20px;
        }

        .stats-icon.primary {
            background: linear-gradient(135deg, #ff7b00 0%, #ff9500 100%);
        }

        .stats-icon.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .stats-icon.info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }

        .stats-icon.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .stats-label {
            font-size: 1.1rem;
            color: #6c757d;
            font-weight: 500;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff7b00, #ff9500);
        }

        .activity-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff7b00 0%, #ff9500 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 15px;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .activity-description {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .activity-time {
            color: #adb5bd;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-chalkboard-teacher me-2"></i>IGSS Teacher</h4>
            <small>Management Portal</small>
        </div>

        <ul class="sidebar-menu">
            <li><a href="dashboard.php" class="active"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
            <li><a href="my-classes.php"><i class="fas fa-users me-2"></i>My Classes</a></li>
            <li><a href="my-subjects.php"><i class="fas fa-book me-2"></i>My Subjects</a></li>
            <li><a href="attendance.php"><i class="fas fa-calendar-check me-2"></i>Attendance</a></li>
            <li><a href="grades.php"><i class="fas fa-graduation-cap me-2"></i>Grades</a></li>
            <li><a href="messages.php"><i class="fas fa-envelope me-2"></i>Messages</a></li>
            <li><a href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
            <li><a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
        </ul>

        <!-- Back to Home Button -->
        <div class="back-to-home">
            <a href="../index.php" class="btn">
                <i class="fas fa-home me-2"></i>Back to Home
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar">
            <div>
                <h4 class="mb-0">Teacher Dashboard</h4>
                <small class="text-muted">Welcome back, <?php echo htmlspecialchars($teacher['first_name'] . ' ' . $teacher['last_name']); ?></small>
            </div>
            <div>
                <span class="badge bg-primary me-2">
                    Session: <?php echo $current_academic['session']; ?>
                </span>
                <span class="badge bg-info">
                    Term: <?php echo TERMS[$current_academic['term']]; ?>
                </span>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card clickable" onclick="navigateToPage('my-subjects.php')" title="Click to view your assigned subjects">
                    <div class="stats-icon primary">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['subjects']; ?></div>
                    <div class="stats-label">Assigned Subjects</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stats-card clickable" onclick="navigateToPage('my-classes.php')" title="Click to view your classes">
                    <div class="stats-icon success">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['classes']; ?></div>
                    <div class="stats-label">Classes Teaching</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stats-card clickable" onclick="navigateToPage('my-classes.php')" title="Click to view student details">
                    <div class="stats-icon info">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['students']; ?></div>
                    <div class="stats-label">Total Students</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stats-card clickable" onclick="<?php echo $stats['is_form_teacher'] ? "navigateToPage('my-classes.php')" : "showFormTeacherInfo()"; ?>" title="<?php echo $stats['is_form_teacher'] ? 'Click to view your form class' : 'You are not assigned as a form teacher'; ?>">
                    <div class="stats-icon warning">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['is_form_teacher'] ? '1' : '0'; ?></div>
                    <div class="stats-label">Form Teacher</div>
                    <?php if ($stats['is_form_teacher']): ?>
                        <small class="text-success">
                            <i class="fas fa-check me-1"></i><?php echo $stats['form_class']; ?>
                        </small>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="chart-container">
                    <h5 class="mb-4"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="attendance.php" class="btn btn-primary w-100 py-3">
                                <i class="fas fa-calendar-check fa-2x mb-2"></i><br>
                                Mark Attendance
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="grades.php" class="btn btn-success w-100 py-3">
                                <i class="fas fa-graduation-cap fa-2x mb-2"></i><br>
                                Enter Grades
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="my-classes.php" class="btn btn-info w-100 py-3">
                                <i class="fas fa-users fa-2x mb-2"></i><br>
                                View Classes
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="messages.php" class="btn btn-warning w-100 py-3">
                                <i class="fas fa-envelope fa-2x mb-2"></i><br>
                                Messages
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Navigation function for clickable cards
        function navigateToPage(page) {
            // Add a subtle loading effect
            const clickedCard = event.currentTarget;
            clickedCard.style.transform = 'translateY(-4px) scale(1.01)';

            // Navigate after a brief delay for visual feedback
            setTimeout(() => {
                window.location.href = page;
            }, 150);
        }

        // Function to show form teacher information
        function showFormTeacherInfo() {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-star text-warning me-2"></i>Form Teacher Status
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center">
                            <i class="fas fa-info-circle fa-3x text-info mb-3"></i>
                            <h6>You are not currently assigned as a form teacher.</h6>
                            <p class="text-muted">
                                Form teacher assignments are managed by the school administration.
                                If you believe this is an error, please contact the admin office.
                            </p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            // Remove modal from DOM after it's hidden
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        // Enhanced hover effects for cards
        document.addEventListener('DOMContentLoaded', function() {
            const clickableCards = document.querySelectorAll('.stats-card.clickable');

            clickableCards.forEach(card => {
                // Add ripple effect on click
                card.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255, 255, 255, 0.3);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                        z-index: 1;
                    `;

                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);

                    setTimeout(() => {
                        if (ripple.parentNode) {
                            ripple.parentNode.removeChild(ripple);
                        }
                    }, 600);
                });
            });

            // Add CSS for ripple animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        });

        // Quick action button enhancements
        document.addEventListener('DOMContentLoaded', function() {
            const quickActionBtns = document.querySelectorAll('.btn.w-100.py-3');

            quickActionBtns.forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.02)';
                });

                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });

                btn.addEventListener('click', function() {
                    this.style.transform = 'translateY(-1px) scale(1.01)';
                });
            });
        });
    </script>
</body>
</html>
